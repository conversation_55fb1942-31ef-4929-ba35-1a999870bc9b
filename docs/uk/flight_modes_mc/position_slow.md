# Повільний режим позиціонування (Мультикоптер)

<Badge type="tip" text="PX4 v1.15" />

<img src="../../assets/site/difficulty_easy.png" title="Easy to fly" width="30px" />&nbsp;<img src="../../assets/site/remote_control.svg" title="Manual/Remote control required" width="30px" />&nbsp;<img src="../../assets/site/position_fixed.svg" title="Position fix required (e.g. GPS)" width="30px" />

Режим повільного позиціювання - це версія з обмеженою швидкістю руху та повороту звичайного [Режиму позиціювання](../flight_modes_mc/position.md).

Режим працює точно так само, як _Режим позиціонування_, але з перемасштабованим відхиленням стіку контролера до менших максимальних швидкостей (і пропорційно меншим прискоренням).
Ви можете використовувати її, щоб швидко зменшити швидкість транспортного засобу до безпечної швидкості (якщо вона рухається швидше, ніж максимальна швидкість в обмеженій вісі).
Ви також можете використовувати його, щоб отримати більшу точність введення стіків, зокрема, при польотах поблизу перешкод, або для дотримання правил, таких як [режим/функція низької швидкості EASA](https://www.easa.europa.eu/en/light/topics/flying-drones-close-people).

Межу швидкості можна встановити за допомогою параметрів, з використанням роторної кнопки, слайдера або перемикача [ПДУ](../getting_started/rc_transmitter_receiver.md), або за допомогою MAVLink.
Обмеження, встановлені за допомогою пульту керування RC, перевищують ті, що встановлені за допомогою MAVLink, які, зі свого боку, перевищують ті, що встановлені за допомогою параметрів.
Ліміти можуть бути зменшені тільки нижче тих, що діють для звичайного режиму _Position_.

## Встановлення обмежень за допомогою параметрів

Максимальні значення для горизонтальної швидкості у режимі повільного руху, вертикальної швидкості та швидкості кочання можна встановити за допомогою параметрів.
Цей підхід корисний, коли максимальна бажана швидкість в повільному режимі фіксована, і ви просто хочете швидко знизити швидкість до безпечного діапазону (можливо, використовуючи перемикач на вашому контролері).

Таблиця нижче показує параметри, що використовуються для встановлення максимальних значень для режиму _Повільне встановлення позиції_ та режиму _Позиції_, відповідно, разом із їхніми значеннями за замовчуванням.

| Вісь                    | Режим повільного позиціювання                    | Режим позиції                                                                                         |
| ----------------------- | ------------------------------------------------ | ----------------------------------------------------------------------------------------------------- |
| Горизонтальна швидкість | [MC\_SLOW\_DEF\_HVEL][mc_slow_def_hvel] (3 m/s)  | [MPC\_VEL\_MANUAL][mpc_vel_manual] (10 m/s)                                                           |
| Вертикальна швидкість   | [MC\_SLOW\_DEF\_VVEL][mc_slow_def_vvel] (1 m/s)  | [MPC\_Z\_VEL\_MAX\_UP][mpc_z_vel_max_up] (3 m/s) / [MPC\_Z\_VEL\_MAX\_DN][mpc_z_vel_max_dn] (1.5 m/s) |
| Швидкість крену         | [MC\_SLOW\_DEF\_YAWR][mc_slow_def_yawr] (45 °/s) | [MPC\_MAN\_Y\_MAX][mpc_man_y_max] (150 °/s)                                                           |

З цього можна побачити, наприклад, що при переході з режиму Позиції в режим Повільної позиції, максимальна висотна швидкість у горизонтальному напрямку за замовчуванням зменшується з 10 м/с до 3 м/с.
Якщо подорожуєте швидше, ніж 3 м/с горизонтально, ви сповільнюватиметесь до 3 м/с.

Зверніть увагу, що параметри використовуються лише у випадку, якщо обмеження не надаються від RC або MAVLink.

<!-- links used in table above -->

[mpc_vel_manual]: ../advanced_config/parameter_reference.md#MPC_VEL_MANUAL
[mc_slow_def_hvel]: ../advanced_config/parameter_reference.md#MC_SLOW_DEF_HVEL
[mpc_z_vel_max_up]: ../advanced_config/parameter_reference.md#MPC_Z_VEL_MAX_UP
[mpc_z_vel_max_dn]: ../advanced_config/parameter_reference.md#MPC_Z_VEL_MAX_DN
[mc_slow_def_vvel]: ../advanced_config/parameter_reference.md#MC_SLOW_DEF_VVEL
[mpc_man_y_max]: ../advanced_config/parameter_reference.md#MPC_MAN_Y_MAX
[mc_slow_def_yawr]: ../advanced_config/parameter_reference.md#MC_SLOW_DEF_YAWR

## Встановлення обмежень за допомогою керування RC

Ви можете зіставити обертовий регулятор, слайдер або перемикач на [RC-контролері](../getting_started/rc_transmitter_receiver.md), щоб встановити максимальну швидкість вісі (горизонтальної/вертикальної/повороту).
Цей підхід корисний, коли відповідні значення максимальної сповільнення можуть відрізнятися під час польоту.

Якщо вхідне керування встановлено на найвище значення, транспортний засіб поїде так швидко, як у режимі _Position_.
Якщо вхід встановлено на найнижче значення, максимальна швидкість транспортного засобу встановлюється ​​на значення в відповідному параметрі `MC_SLOW_MIN_` (показано в таблиці нижче).
Якщо ввімкнено керування RC для вісі, воно має пріоритет над усіма іншими входами.

Таблиця нижче містить кожну вісь разом з параметром, який використовується для вибору того, який канал RC AUX відповідає кнопці керування, та параметром, що встановлює найнижче можливе "максимальне значення" для вісі.

| Вісь                    | Параметр для відображення допоміжного вводу | Параметр для мінімального значення максимальної швидкості |
| ----------------------- | ------------------------------------------- | --------------------------------------------------------- |
| Горизонтальна швидкість | [MC\_SLOW\_MAP\_HVEL][mc_slow_map_hvel]     | [MC\_SLOW\_MIN\_HVEL][mc_slow_min_hvel]                   |
| Вертикальна швидкість   | [MC\_SLOW\_MAP\_VVEL][mc_slow_map_vvel]     | [MC\_SLOW\_MIN\_VVEL][mc_slow_min_vvel]                   |
| Швидкість крену         | [MC\_SLOW\_MAP\_YAWR][mc_slow_map_yawr]     | [MC\_SLOW\_MIN\_YAWR][mc_slow_min_yawr]                   |

<!-- links used in table above -->

[mc_slow_map_hvel]: ../advanced_config/parameter_reference.md#MC_SLOW_MAP_HVEL
[mc_slow_min_hvel]: ../advanced_config/parameter_reference.md#MC_SLOW_MIN_HVEL
[mc_slow_map_vvel]: ../advanced_config/parameter_reference.md#MC_SLOW_MAP_VVEL
[mc_slow_min_vvel]: ../advanced_config/parameter_reference.md#MC_SLOW_MIN_VVEL
[mc_slow_map_yawr]: ../advanced_config/parameter_reference.md#MC_SLOW_MAP_YAWR
[mc_slow_min_yawr]: ../advanced_config/parameter_reference.md#MC_SLOW_MIN_YAWR

Щоб використовувати цей підхід:

1. Переконайтеся, що в вашому пульті є додатковий вхід та додатковий канал дистанційного керування для передачі його положення.
2. Відобразіть канал, який містить позицію ручок, як один з 6 додаткових входів пропускання, встановивши [RC_MAP_AUXn](../advanced_config/parameter_reference.md#RC_MAP_AUX1) на відповідний номер каналу RC.
3. Карта введення, використовуючи відповідний параметр `MC_SLOW_MAP_` для вісі, яку ви хочете контролювати (див. таблицю вище).

Наприклад, якщо ви хочете відобразити канал RC 8 для обмеження горизонтальної швидкості, ви можете встановити для [RC\_MAP\_AUX1](../advanced_config/parameter_reference.md#RC_MAP_AUX1) значення 8, а для [MC\_SLOW\_MAP\_HVEL][mc_slow_map_hvel] значення значення "1".
Потім вхід RC з каналу 8 встановлює обмеження горизонтальної швидкості між [MC\_SLOW\_MIN\_HVEL][mc_slow_min_hvel] і [MPC\_VEL\_MANUAL][mpc_vel_manual].

## Встановлення обмежень за допомогою MAVLink

Ви можете налаштувати ліміти швидкості, використовуючи повідомлення MAVLink [SET_VELOCITY_LIMITS](https://mavlink.io/en/messages/development.html#SET_VELOCITY_LIMITS).
Цей підхід використовується переважно автоматичними системами, наприклад, для сповільнення транспортного засобу при збільшенні масштабування камери.

Повідомлення може встановити максимальне значення на будь-якій з осей, надавши не-`NAN` обмеження.
Це заміняє обмеження, встановлені в параметрах, але ігнорується, якщо вісь відображена на ручку RC.
Значення може бути оновлене з повідомлення у будь-який момент, і залишається затриманим до наступного повідомлення або перемикання режиму.

Зверніть увагу, що PX4 не надає телеметрію обмеження швидкості (тобто воно не підтримує передавання повідомлення [VELOCITY_LIMITS](https://mavlink.io/en/messages/development.html#VELOCITY_LIMITS)).

## Дивіться також

- [Режим уповільненого розташування](../flight_modes_mc/position.md)
