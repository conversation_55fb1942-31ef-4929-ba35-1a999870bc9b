## Purpose

The idea of this tool is to automate the writing of the Advanced Lift Drag plugin by automatizing the coefficient generation and requiring minimal user calculations.

## Setup

In order to run this tool, it is necessary to follow these steps:

1. Download AVL 3.36 from <https://web.mit.edu/drela/Public/web/avl/>. The file for AVL version 3.36 can be found about halfway down the page.
2. After downloading, extract AVL and move it to the home directory using:

```shell
sudo tar -xf avl3.36.tgz
mv ./Avl /home/
```

Follow the README.md found in Avl to finish the setup process for AVL (requires to set up plotlib and eispack libraries). I recommend using the gfortran compile option. This might require you to install gfortran. This can be done by running:

```shell
sudo apt update
sudo apt install gfortran
```

When running the Makefile for AVL, you might encounter an Error 1 message stating that there is a directory missing. This does not prevent AVL from working for our purposes. Once the process described in the AVL README is completed, AVL is ready to be used. No further set up is required on the side of the AVL or the tool.
If you want to move the location of the AVL directory, this can simply be done by passing the `--avl_path` flag to the `input_avl.py` file, using the desired directory location for the flag (don't forget to place a "/" behind the last part of the path). Running this will automatically also adjust the paths where necessary.

## Run

To run the tool all that is needed is to modify the `input.yml` to the plane that you desire and then run `python input_avl.py <your_custom_yaml_file>.yml` Note that you require to have the yaml and argparse packages in your python environment to run this. An example template has been provided in the form of the `input.yml` that implements a standard plane with two ailerons, an elevator and a rudder. This example template can be run using: `python input_avl.py --yaml_file input.yml`.
Once the script has been executed, the generated .avl, .sdf and a plot of the proposed control surfaces can be found in <your-planes-name> directory. The sdf file is the generated Advanced Lift Drag Plugin that can be copied and pasted straight into a model.sdf file, which can then be run in Gazebo.

## Functionality

The tool first asks the user for a range of vehicle specific parameters that are needed in order to specify the geometry and physical properties of the plane. The user has the choice to define a completely custom model, or alternatively select a predefined model template (such as a Cessna or a VTOL), which has a known number of control surfaces, and then provide only some physical properties, without having to define the entire model themselves. The input_avl.py file takes the provided parameter and creates an .avl file from this that can be read by AVL (the program). This happens in the process.sh file. The necessary output generated by AVL will be saved in two files: custom_vehicle_body_axis_derivatives.txt and custom_vehicle_stability_derivatives.txt. These two files contain the parameters that are required in order to populate the Advanced Lift Drag Plugin. Finally, avl_out_parse.py reads the generated .txt files and accordingly assigns parameters to the correct element in sdf. Once this is done, it is only a question of copy and pasting the generated Advanced Lift Drag plugin (found as <custom_plane>.sdf into the desired model.sdf file. )


## Usability

The current implementation provides a minimal working example. More accurate measurements can be made by adjusting the chosen number of vortices along span and chord according to desired preferences. A good starting point for this can be found here: <https://www.redalyc.org/pdf/6735/673571173005.pdf>. Furthermore, one can also more accurately model a vehicle by using a larger number of sections. In the current .yml file, only a left and right edge are defined for each surface yielding exactly one section, but the code supports expanding this to any number of desired sections.

## IMPORTANT POINTS TO NOTE

- A control surface in AVL is always defined from left to right. This means you need to first provide the left edge of a surface and then the right edge. If you do this the opposite way around, a surface will essentially be defined upside down.
- The tool is designed to only support at most two control surfaces of any type on any one vehicle. Having more surfaces than that can lead to faulty behavior.
- Another important point is that these scripts make use of the match, case syntax, which was only introduced in Python in version 3.10.
- The primary reference resource for AVL can be found at <https://web.mit.edu/drela/Public/web/avl/AVL_User_Primer.pdf>. This document was written by the creators of AVL and contains all the variables that could be required in defining the control surfaces.
- AVL cannot predict stall values. As such these need to be calculated/estimated another way. In the current implementation, default stall values have been taken from PX4's Advanced Plane. These should naturally be changed for new/different models.

## Parameter Assignment

Below is a comprehensive list on how the parameters are assigned at output and what files in AVL they are taken from. I am by no means an AVL expert, so please verify that these are actually the correct parameters required by the Advanced Lift Drag Plugin. For an explanation of what the parameters do, please see take a look at the Advanced Lift Drag Plugin.

(name-in-AVL) -> (name-in-plugin)

From the stability derivatives log file, the following advanced lift drag plugin parameters are taken:

Alpha	-> alpha	The angle of attack

Cmtot	-> Cem0		Pitching moment coefficient at zero angle of attack

CLtot	-> CL0		Lift Coefficient at zero angle of attack

CDtot	-> CD0		Drag coefficient at zero angle of attack

CLa	-> CLa		dCL/da (slope of CL-alpha curve)

CYa	-> CYa		dCy/da (sideforce slope wrt alpha)

Cla	-> Cella	dCl/da (roll moment slope wrt alpha)

Cma	-> Cema		dCm/da (pitching moment slope wrt alpha - before stall)

Cna	-> Cena		dCn/da (yaw moment slope wrt alpha)

CLb	-> CLb		dCL/dbeta (lift coefficient slope wrt beta)

CYb	-> CYb		dCY/dbeta (side force slope wrt beta)

Clb	-> Cellb	dCl/dbeta (roll moment slope wrt beta)

Cmb	-> Cemb		dCm/dbeta (pitching moment slope wrt beta)

Cnb	-> Cenb		dCn/dbeta (yaw moment slope wrt beta)


From the body axis derivatives log file, the following advanced lift drag plugin parameters are taken:

e	-> eff		Wing efficiency (Oswald efficiency factor for a 3D wing)

CXp	-> CDp		dCD/dp (drag coefficient slope wrt roll rate)

CYp	-> CYp		dCY/dp (sideforce slope wrt roll rate)

CZp	-> CLp		dCL/dp (lift coefficient slope wrt roll rate)

Clp	-> Cellp	dCl/dp (roll moment slope wrt roll rate)

Cmp	-> Cemp		dCm/dp (pitching moment slope wrt roll rate)

Cmp	-> Cenp		dCn/dp (yaw moment slope wrt roll rate)

CXq	-> CDq		dCD/dq (drag coefficient slope wrt pitching rate)

CYq	-> CYq		dCY/dq (side force slope wrt pitching rate)

CZq	-> CLq		dCL/dq (lift coefficient slope wrt pitching rate)

Clq	-> Cellq	dCl/dq (roll moment slope wrt pitching rate)

Cmq	-> Cemq		dCm/dq (pitching moment slope wrt pitching rate)

Cnq	-> Cenq		dCn/dq (yaw moment slope wrt pitching rate)

CXr	-> CDr		dCD/dr (drag coefficient slope wrt yaw rate)

CYr	-> CYr		dCY/dr (side force slope wrt yaw rate)

CZr	-> CLr		dCL/dr (lift coefficient slope wrt yaw rate)

Clr	-> Cellr	dCl/dr (roll moment slope wrt yaw rate)

Cmr	-> Cemr		dCm/dr (pitching moment slope wrt yaw rate)

Cnr	-> Cenr		dCn/dr (yaw moment slope wrt yaw rate)


Furthermore, every control surface also has six own parameters, which are also derived from this log file. {i} below ranges from 1 to the number of unique control surface types in the model.

CXd{i}	-> CD_ctrl	Effect of the control surface's deflection on drag

CYd{i}	-> CY_ctrl	Effect of the control surface's deflection on side force

CZd{i}	-> CL_ctrl	Effect of the control surface's deflection on lift

Cld{i}	-> Cell_ctrl	Effect of the control surface's deflection on roll moment

Cmd{i}	-> Cem_ctrl	Effect of the control surface's deflection on pitching moment

Cnd{i}	-> Cen_ctrl	Effect of the control surface's deflection on yaw moment


## Future Work

The tool, while self-contained, could be expanded into multiple directions.

1. Currently hinge positions and gains are set at default levels, and these could, if desired be further customized for more control.
2. More vehicles could be added to provide default templates that require less input. At the moment, only "custom" works completely.
3. Fuselage modelling could be included to further improve the accuracy of calculated coefficients.
4. At the moment only NACA airfoils are provided as a way to generate cambered surfaces. An alternative to this would be to use custom airfoil files.
