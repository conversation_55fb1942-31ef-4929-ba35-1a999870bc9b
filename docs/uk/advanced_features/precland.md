# Точна посадка

PX4 підтримує точне приземлення для _Multicopters_ на стаціонарних або рухомих цілях.
Ціль може бути надана вбудованим ІЧ-датчиком та приземленням, або зовнішньою системою позиціонування.

Пристрій для точного приземлення може бути [запущений/ініційований](#initiating-a-precision-landing) як частина [місії](#mission), у режимі [повернення](#return-mode-precision-landing) на посадку або при вході у режим польоту [_Точна посадка_](#precision-landing-flight-mode).

:::info
Точна посадка можлива лише з дійсною глобальною позицією (через обмеження в поточній реалізації контролера позиції).
:::

## Загальний огляд

### Режими посадки

Точну посадку можна налаштувати як "обов'язкову" або "вигідну".
Вибір режиму впливає на те, як виконується точна посадка.

#### Необхідний режим

У _Required Mode_ транспортний засіб буде шукати ціль, якщо нічого не видно під час початку посадки.
Транспортний засіб виконає точну посадку, якщо ціль буде знайдена.

Процедура пошуку полягає у підйомі на висоту пошуку ([PLD_SRCH_ALT](../advanced_config/parameter_reference.md#PLD_SRCH_ALT)).
Якщо мішень все ще не видно на висоті пошуку після закінчення часу пошуку ([PLD_SRCH_TOUT](../advanced_config/parameter_reference.md#PLD_SRCH_TOUT)), то ініціюється звичайна посадка на поточному місці.

:::info
Якщо використовується позамежна система позиціонування, PX4 передбачає, що ціль буде видима, коли отримує повідомлення MAVLink [LANDING_TARGET](https://mavlink.io/en/messages/common.html#LANDING_TARGET).
:::

#### Опортуністичний режим

У режимі _Opportunistic Mode_ транспортний засіб використовуватиме точну посадку, _якщо_ (і тільки якщо) ціль буде видима, коли розпочинається посадка.
Якщо ціль не видно, транспортний засіб негайно виконує _звичайну_ посадку на поточному місці.

### Фази посадки

Режим Точної посадки має три етапи:

1. **Горизонтальний підхід:** Транспортний засіб підходить до цілі горизонтально, утримуючи свою поточну висоту.
  Як тільки положення цілі відносно транспортного засобу опускається нижче порогового значення ([PLD_HACC_RAD](../advanced_config/parameter_reference.md#PLD_HACC_RAD)), відбувається вхід до наступної фази.
  Якщо ціль втрачається під час цієї фази (не видно довше, ніж [PLD_BTOUT](../advanced_config/parameter_reference.md#PLD_BTOUT)), ініціюється процедура пошуку (під час необхідної точної посадки) або транспортний засіб робить звичайну посадку (під час можливої точної посадки).

2. **Спуск над ціль:** Транспортний засіб спускається, залишаючись при цьому над ціллю.
  Якщо ціль втрачається під час цієї фази (не видно довше, ніж `PLD_BTOUT`), ініціюється процедура пошуку (під час необхідної точної посадки) або транспортний засіб робить звичайну посадку (під час можливої точної посадки).

3. **Останній підхід:** Коли транспортний засіб знаходиться близько до землі (ближче, ніж [PLD_FAPPR_ALT](../advanced_config/parameter_reference.md#PLD_FAPPR_ALT)), він спускається, залишаючись при цьому над ціллю.
  Якщо ціль втрачається під час цієї фази, спуск продовжується незалежно від виду точної посадки.

Процедури пошуку ініціюються на перших і других етапах і виконуються не більше [PLD_MAX_SRCH разів](../advanced_config/parameter_reference.md#PLD_MAX_SRCH).
Діаграма потоку фаз посадки

Діаграма потоку, що показує фази, може бути знайдена на [діаграмі потоку фаз посадки](#landing-phases-flow-diagram) нижче.

## Початок точної посадки

Точне приземлення можна використовувати у місіях, під час фази посадки у режимі _Повернення_, або ввійшовши в режим _Точна посадка_.

<a id="mission"></a>

### Місія Точна посадка

Точне приземлення може бути запущено як частина [місії](../flying/missions.md), використовуючи [MAV_CMD_NAV_LAND](https://mavlink.io/en/messages/common.html#MAV_CMD_NAV_LAND) з відповідно встановленим `param2`:

- `0`: нормальне приземлення без використання цілі.
- `1`: [Opportunistic](#opportunistic-mode) режим точної посадки.
- `2`: [Required](#required-mode) режим точної посадки.

### Режим повернення з точною посадкою

Точна посадка може бути використана у фазі посадки у [режимі повернення](../flight_modes/return.md).

Це вмикається за допомогою параметра [RTL_PLD_MD](../advanced_config/parameter_reference.md#RTL_PLD_MD), який приймає такі значення:

- `0`: Точна посадка вимкнена (посадка як зазвичай).
- `1`: [Опортуничний](#opportunistic-mode) режим точної посадки.
- `2`: [Вимагається](#required-mode) режим точної посадки.

### Режим польоту з точною посадкою

Точна посадка може бути ввімкнена, переключившись у режим польоту з _точною посадкою_.

Ви можете перевірити це, використовуючи [консоль MAVLink _QGroundControl_](../debug/mavlink_shell.md#qgroundcontrol-mavlink-console) для введення наступної команди:

```sh
commander mode auto:precland
```

:::info
При переході до режиму таким чином, точна посадка завжди "вимагається"; немає способу вказати тип посадки.
:::

:::info
На момент написання немає _зручного_ способу безпосередньо викликати точну посадку (окрім командування режимом повернення):

- QGroundControl_ не надає його як параметр користувальницького інтерфейсу.
- Команда [MAV_CMD_NAV_LAND](https://mavlink.io/en/messages/common.html#MAV_CMD_NAV_LAND) працює лише в місіях.
- Команда [MAV_CMD_DO_SET_MODE](https://mavlink.io/en/messages/common.html#MAV_CMD_DO_SET_MODE) має працювати, але вам потрібно визначити відповідні базові і користувацькі режими, які використовує PX4 для представлення режиму точної посадки.

:::

## Налаштування програмного забезпечення

### Налаштування IR Сенсора/Бікона

Рішення з інфрачервоним датчиком/посадковим маяком потребує датчика [IR-LOCK](https://irlock.com/products/ir-lock-sensor-precision-landing-kit) та напрямленого донизу [датчика відстані](../sensor/rangefinders.md), підключеного до автопілота, а також інфрачервоного маяка в якості цілі (наприклад, [IR-LOCK MarkOne](https://irlock.com/collections/markone)).
Це дозволяє приземлитися з точністю приблизно 10 см (в той час як точність GPS може бути в декілька метрів).

Встановіть датчик IR-LOCK, слідуючи [офіційному посібнику](https://irlock.readme.io/v2.0/docs).
Переконайтеся, що ось x сенсора вирівняна з осью y транспортного засобу, а ось y сенсора вирівняна з напрямком -x транспортного засобу (це відбувається, якщо камера нахилена вниз на 90 градусів від напрямку вперед).

Установіть [датчик дальності/відстані](../sensor/rangefinders.md) (виявлено, що _LidarLite v3_ працює добре).

:::info
Багато датчиків дальності на основі інфрачервоного випромінювання погано працюють в присутності маяка IR-LOCK.
Зверніться до посібника з IR-LOCK для інших сумісних датчиків.
:::

## Позабортне позиціонування

Для позабортового рішення потрібна система позиціонування, яка реалізує [Landing Target Protocol](https://mavlink.io/en/services/landing_target.html) MAVLink.
Це може використовувати будь-який механізм позиціонування для визначення місця посадки, наприклад комп'ютерного зору та візуального маркера.

Система повинна публікувати координати цілі в повідомленні [LANDING_TARGET](https://mavlink.io/en/messages/common.html#LANDING_TARGET).
Зверніть увагу, що PX4 _вимагає_, щоб `LANDING_TARGET.frame` був [MAV_FRAME_LOCAL_NED](https://mavlink.io/en/messages/common.html#MAV_FRAME_LOCAL_NED) і заповнював лише поля `x`, `y`, та `z`.
Початок локальної системи NED [0,0] - це домашня позиція (ви можете відобразити цю домашню позицію на глобальні координати, використовуючи [GPS_GLOBAL_ORIGIN](https://mavlink.io/en/messages/common.html#GPS_GLOBAL_ORIGIN)).

PX4 does not explicitly require a [distance sensor](../sensor/rangefinders.md) or other sensors, but will perform better if it can more precisely determine its own position.

## Конфігурація прошивки

Для точної посадки потрібні модулі `irlock` та `landing_target_estimator`.
Ці модулі включені до прошивки PX4 за замовчуванням для більшості польотних контролерів.

Вони не включені за замовчуванням на контролерах, що базуються на FMUv2.
На цих та інших платах, де вони не включені, ви можете додати їх, встановивши наступні ключі на 'y' у відповідному конфігураційному файлі для вашого автопілота (наприклад, як зроблено тут для FMUv5: [PX4-Autopilot/boards/px4/fmu-v5/default.px4board](https://github.com/PX4/PX4-Autopilot/blob/main/boards/px4/fmu-v5/default.px4board)):

```
CONFIG_DRIVERS_IRLOCK=y
CONFIG_MODULES_LANDING_TARGET_ESTIMATOR=y
```

## Конфігурація PX4 (параметри)

Датчик IR-Lock вимкнено за замовчуванням.
Увімкніть його, встановивши [SENS_EN_IRLOCK](../advanced_config/parameter_reference.md#SENS_EN_IRLOCK) на `1` (true).

[LTEST_MODE](../advanced_config/parameter_reference.md#LTEST_MODE) визначає, чи припускається, що ціль є нерухомою або рухливою.
Якщо параметр `LTEST_MODE` встановлено на значення moving (наприклад, він встановлений на транспортний засіб, на який має сісти багтроплан), вимірювання цілі використовуються лише для генерації цілейових точок позиції в контролері точної посадки.
Якщо параметр `LTEST_MODE` встановлено ​​на значення stationary, вимірювання цілі також використовуються оцінювачем позиції транспортного засобу (EKF2 або LPE).

Інші важливі параметри перераховані в посиланнях на параметри в референсі параметрів під [Landing_target estimator](../advanced_config/parameter_reference.md#landing-target-estimator) та [Precision land](../advanced_config/parameter_reference.md#precision-land).
Деякі з найбільш корисних перераховані нижче.

| Параметр                                                                                                                                        | Опис                                                                                                                                                                                                 |
| ----------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a id="SENS_EN_IRLOCK"></a>[SENS_EN_IRLOCK](../advanced_config/parameter_reference.md#SENS_EN_IRLOCK) | IR-LOCK Sensor (зовнішній I2C). Disable: `0` (default): Enable: `1`).          |
| <a id="LTEST_MODE"></a>[LTEST_MODE](../advanced_config/parameter_reference.md#LTEST_MODE)                                  | Landing target is moving (`0`) or stationary (`1`). За замовчуванням - рухома.                                                 |
| <a id="PLD_HACC_RAD"></a>[PLD_HACC_RAD](../advanced_config/parameter_reference.md#PLD_HACC_RAD)       | Горизонтальний радіус прийняття, всередині якого транспортний засіб почне спускатися. За замовчуванням - 0,2 м.                                                      |
| <a id="PLD_BTOUT"></a>[PLD_BTOUT](../advanced_config/parameter_reference.md#PLD_BTOUT)                                     | Таймаут цілі посадки, після якого припускається, що ціль втрачена. За замовчуванням - 5 секунд.                                                                      |
| <a id="PLD_FAPPR_ALT"></a>[PLD_FAPPR_ALT](../advanced_config/parameter_reference.md#PLD_FAPPR_ALT)    | Висота останнього підходу. Висота останнього підходу. За замовчуванням - 0,1 метра.                                                                  |
| <a id="PLD_MAX_SRCH"></a>[PLD_MAX_SRCH](../advanced_config/parameter_reference.md#PLD_MAX_SRCH)       | Максимальна кількість спроб пошуку у вимаганій посадці.                                                                                                                              |
| <a id="RTL_PLD_MD"></a>[RTL_PLD_MD](../advanced_config/parameter_reference.md#RTL_PLD_MD)             | Режим точної посадки RTL. `0`: disabled, `1`: [Opportunistic](#opportunistic-mode), `2`: [Required](#required-mode). |

### Масштабування ІЧ-маяка

Масштабування вимірювань може бути необхідним через спотворення об'єктива датчика IR-LOCK.

[LTEST_SCALE_X](../advanced_config/parameter_reference.md#LTEST_SCALE_X) та [LTEST_SCALE_Y](../advanced_config/parameter_reference.md#LTEST_SCALE_Y) можуть бути використані для масштабування вимірів маяка, перед тим як вони будуть використані для оцінки позиції та швидкості маяка відносно транспортного засобу.
Зверніть увагу, що `LTEST_SCALE_X` та `LTEST_SCALE_Y` вважаються в рамках датчика, а не в рамках транспортного засобу.

Щоб калібрувати ці параметри масштабу, встановіть `LTEST_MODE` на значення moving, підніміть свій багатокоптер вище за маяк і виконайте рухи вперед-назад та ліворуч-праворуч з транспортним засобом, під час цього [записуючи](../dev_log/logging.md#configuration) дані з `landing_target_pose` та `vehicle_local_position`.
Потім порівняйте `landing_target_pose.vx_rel` та `landing_target_pose.vy_rel` з `vehicle_local_position.vx` та `vehicle_local_position.vy` відповідно (обидва виміри у системі координат NED).
Якщо оцінювані швидкості маяка завжди менші або більші за швидкості транспортного засобу, налаштуйте параметри масштабування для компенсації.

Якщо ви спостерігаєте повільні бокові коливання літального апарата під час точної посадки з параметром `LTEST_MODE`, встановленим на стаціонарний, ймовірно, вимірювання маяка занадто високі, і вам слід зменшити параметр масштабування у відповідному напрямку.

## Симуляція

Точне приземлення з датчиком IR-LOCK та маяком може бути симульоване в [Gazebo Classic](../sim_gazebo_classic/README.md).

Щоб запустити симуляцію зі світом, що містить маяк IR-LOCK та транспортний засіб із датчиком дальності та камерою IR-LOCK, виконайте наступну команду:

```sh
make px4_sitl gazebo-classic_iris_irlock
```

Ви можете змінити розташування маяка або перемістивши її в Gazebo Classic GUI або змінивши її місце в [Gazebo World](https://github.com/PX4/PX4-SITL_gazebo-classic/blob/main/worlds/iris_irlock.world#L42).

## Принцип дії

### Оцінювач цілей посадки

`landing_target_estimator` бере вимірювання з драйвера irlock, а також оцінює висоту місцевості для оцінки позиції маяка відносно транспортного засобу.

The measurements in `irlock_report` містять тангенс кутів від центру зображення до маяка.
Іншими словами, вимірювання - це компоненти x та y вектора, що вказує на маяк, де компонент z має довжину "1".
Це означає, що масштабування вимірювання за відстанню від камери до маяка призводить до вектора від камери до маяка.
Ця відносна позиція потім повертається у зіставлений з півночі, рівномірний корпусний каркас за допомогою оцінки польоту засобами повітряного судна.
Обидва компоненти x та y відносного вимірювання позиції фільтруються в окремих фільтрах Калмана, які діють як прості фільтри згладжування з низькою пропускною спроможністю, що також генерують оцінку швидкості та дозволяють відкидати викиди.

`landing_target_estimator` публікує приблизну відносну позицію і швидкість щоразу, коли новий звіт про `irlock_report` об'єднується в оцінку.
Нічого не публікується, якщо маяк не бачиться або вимірювання маяка відхиляються.
Оцінка посадкової цілі публікується в повідомленні uORB `landing_target_pose`.

### Покращена оцінка положення транспортного засобу

Якщо ціль вказана як стаціонарна за допомогою параметра `LTEST_MODE`, оцінку положення/швидкості транспортного засобу можна покращити за допомогою вимірів цілі.
Це виконується шляхом злиття швидкості цілі як вимірювання від'ємної швидкості транспортного засобу.

### Діаграма потоку фаз посадки

Це зображення показує [фази посадки](#landing-phases) у вигляді діаграми потоку.

![Precision Landing Flow Diagram](../../assets/precision_land/precland-flow-diagram.png)
