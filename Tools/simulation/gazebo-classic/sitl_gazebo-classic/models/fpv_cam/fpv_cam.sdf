<?xml version='1.0'?>
<sdf version='1.6'>
    <model name='fpv_cam'>
    <pose>-0.158979 -0.04405 0.045074 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>0.015</mass>
          <inertia>
            <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.01 0.01 0.01</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.01 0.01 0.01</size>
            </box>
          </geometry>
        </visual>
        <sensor name='camera' type='camera'>
          <camera name='__default__'>
            <horizontal_fov>1.047</horizontal_fov>
            <image>
              <width>320</width>
              <height>240</height>
            </image>
            <clip>
              <near>0.1</near>
              <far>100</far>
            </clip>
            <lens>
              <type>custom</type>
              <custom_function>
                <c1>1.05</c1>
                <c2>4</c2>
                <f>1</f>
                <fun>tan</fun>
              </custom_function>
              <scale_to_hfov>1</scale_to_hfov>
              <cutoff_angle>3.1415</cutoff_angle>
              <env_texture_size>1080</env_texture_size>
            </lens>
          </camera>
          <always_on>1</always_on>
          <update_rate>30</update_rate>
          <visualize>1</visualize>
          <plugin name='camera_plugin' filename='libgazebo_ros_camera.so'>
              <robotNamespace></robotNamespace>
            <alwaysOn>true</alwaysOn>
            <imageTopicName>image_raw</imageTopicName>
            <cameraInfoTopicName>camera_info</cameraInfoTopicName>
            <updateRate>30.0</updateRate>
            <cameraName>usb_cam</cameraName>
            <frameName>/robot_camera_link</frameName>
            <CxPrime>320.5</CxPrime>
            <Cx>320.5</Cx>
            <Cy>240.5</Cy>
            <hackBaseline>0</hackBaseline>
            <focalLength>277.191356</focalLength>
            <distortionK1>0.0</distortionK1>
            <distortionK2>0.0</distortionK2>
            <distortionK3>0.0</distortionK3>
            <distortionT1>0.0</distortionT1>
            <distortionT2>0.0</distortionT2>
          </plugin>
          <!-- GStreamer camera plugin (needs a lot of CPU! Consider lowering the
             camera image size) -->
          <plugin name="GstCameraPlugin" filename="libgazebo_gst_camera_plugin.so">
            <robotNamespace></robotNamespace>
            <udpPort>5600</udpPort>
          </plugin>
        </sensor>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
  </model>
</sdf>
