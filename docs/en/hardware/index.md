# Hardware Integration

This section contains topics about integrating PX4 with _new_ autopilot and peripheral hardware, including:

- [Flight Controller Reference Design](../hardware/reference_design.md)
- [Manufacturer’s Board Support Guide](../hardware/board_support_guide.md)
- [Flight Controller Porting Guide](../hardware/porting_guide.md)
- [Serial Port Mapping](../hardware/serial_port_mapping.md)
- [Airframes](../dev_airframes/index.md)
- [Device Drivers](../middleware/drivers.md)
- [Telemetry Radio/Modems](../data_links/telemetry.md) and other communications links
- [Sensor and Actuator I/O](../sensor_bus/index.md)
- [RTK GPS (Integration)](../advanced/rtk_gps.md)

:::tip
Other sections show how to _use_ and _configure_ supported [autopilot](../flight_controller/index.md), [companion computer](../companion_computer/index.md) and [peripheral](../peripherals/index.md) hardware.
:::
