# Апаратне забезпечення контролера польоту (автопілота)

Flight Controllers (FCs) are the autopilot hardware onto which PX4 firmware is uploaded.
This section contains topics about compatible flight controller and baseboard hardware, and how it is mounted and configured.

## Selecting a Flight Controller

Information about how to choose a PX4-compatible flight controller and the available controllers:

- [Flight Controller Selection](../getting_started/flight_controller_selection.md)
- [Pixhawk Standard/Supported Autopilots](../flight_controller/autopilot_pixhawk_standard.md)
- [Manufacturer-Supported Autopilots](../flight_controller/autopilot_manufacturer_supported.md)
- [Experimental Autopilots](../flight_controller/autopilot_experimental.md)
- [Discontinued Autopilots & Complete Vehicles](../flight_controller/autopilot_discontinued.md)
- [Pixhawk Autopilot Bus & Carriers](../flight_controller/pixhawk_autopilot_bus.md)

:::info
There may be other [Pixhawk Series](../flight_controller/pixhawk_series.md) compatible flight controllers and variants, including those [documented here on Github](https://github.com/PX4/PX4-Autopilot/#supported-hardware).
:::

## Flight Controller Mounting and Setup

Information about how to mount the flight controller, upload firmware (replacing an incompatible bootloader if needed), and configure its internal sensors and orientation:

- [Flight Controller Selection](../getting_started/flight_controller_selection.md)
- [Mounting the Flight Controller](../assembly/mount_and_orient_controller.md)
- [Updating Firmware](../config/firmware.md)
- [Sensor Orientation](../config/flight_controller_orientation.md)
- [Level Horizon Calibration](../config/level_horizon_calibration.md)
- [Розширена орієнтація контролера](../advanced_config/advanced_flight_controller_orientation_leveling.md)
- [Оновлення бутлоадера](../advanced_config/bootloader_update.md)
