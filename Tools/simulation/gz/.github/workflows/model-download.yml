name: Model Download

on:
  workflow_dispatch:
  push:
    branches:
      - '*'

jobs:
    test:
        runs-on: ubuntu-latest

        steps:
        - name: Checkout repository
          uses: actions/checkout@v4

        - name: Set up Python
          uses: actions/setup-python@v3
          with:
            python-version: 3.9

        - name: Install dependencies
          run: |
            python -m pip install --upgrade pip
            pip install pytest

        - name: Run pytest
          run: |
            pytest -v -s tests/test_model.py



