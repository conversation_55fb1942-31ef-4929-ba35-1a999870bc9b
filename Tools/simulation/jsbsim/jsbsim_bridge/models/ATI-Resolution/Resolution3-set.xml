<?xml version="1.0"?>
<!--
************************************************************************
Rascal 110 R/C airplane config. This files ties together all the components
used by FGFS to represent the Rascal 110 (by Sig Mfg) including the flight
dynamics model, and external 3D model.
************************************************************************
-->

<PropertyList>
 <sim>

  <description>ATI Resolution 3 (Blended Body Marinized UAS)</description>
  <author><PERSON>, Copyright ATI, Inc.</author>
  <aircraft-version>3.0</aircraft-version>

  <tags>
    <tag>delta-wing</tag>
    <tag>tricycle</tag>
    <tag>fixed-gear</tag>
    <tag>castering</tag>
    <tag>reconnaissance</tag>
    <tag>uav</tag>
  </tags>

  <startup>
    <splash-texture>Aircraft/ATI-Resolution/Resolution3-splash.png</splash-texture>
  </startup>

  <flight-model>yasim</flight-model>
  <aero>resolution3-yasim</aero>
  <fuel-fraction>0.5</fuel-fraction>

  <systems>
   <autopilot>
    <path>Aircraft/ATI-Resolution/Autopilots/Resolution3.xml</path>
   </autopilot>
  </systems>

  <instrumentation>
    <path>Aircraft/ATI-Resolution/instrumentation.xml</path>
  </instrumentation>

  <sound>
   <path>Aircraft/Generic/generic-sound.xml</path>
  </sound>

  <hud>
   <path n="1">Huds/NTPS.xml</path>
   <!-- <path n="1">Huds/RSVP.xml</path> -->
   <visibility type="bool" n="1">true</visibility>
   <enable3d type="bool" n="1">false</enable3d>
   <color>
    <transparent type="bool">true</transparent>
    <antialiased type="bool">true</antialiased>
    <brightness type="float">0.85</brightness>
    <alpha type="float">0.85</alpha>
   </color>
   <palette>
    <color n="0">
     <red type="float">0.38</red>
     <green type="float">1.0</green>
     <blue type="float">0.22</blue>
    </color>
    <color n="1">
     <red type="float">1.0</red>
     <green type="float">0.0</green>
     <blue type="float">0.0</blue>
    </color>
   </palette>
   <clipping>
     <left type="double">-360</left>
     <right type="double">360</right>
     <top type="double">360</top>
     <bottom type="double">-360</bottom>
   </clipping>
  </hud>

  <view n="101">
    <name>Gyro Camera</name>
    <type>lookat</type>
    <internal type="bool">true</internal>
    <config>
      <eye-lat-deg-path>/position/latitude-deg</eye-lat-deg-path>
      <eye-lon-deg-path>/position/longitude-deg</eye-lon-deg-path>
      <eye-alt-ft-path>/position/altitude-ft</eye-alt-ft-path>
      <eye-heading-deg-path>/orientation/heading-deg</eye-heading-deg-path>
      <eye-pitch-deg-path>/orientation/pitch-deg</eye-pitch-deg-path>
      <eye-roll-deg-path>/orientation/roll-deg</eye-roll-deg-path>
      <x-offset-m>0.0</x-offset-m>
      <y-offset-m>-2.00</y-offset-m>
      <z-offset-m>7.7</z-offset-m>
      <target-lat-deg-path>/sim/input/click/latitude-deg</target-lat-deg-path>
      <target-lon-deg-path>/sim/input/click/longitude-deg</target-lon-deg-path>
      <target-alt-ft-path>/sim/input/click/elevation-ft</target-alt-ft-path>
    </config>
  </view>

  <view n="102">
    <name>Pan/Tilt View</name>
    <enabled type="bool" userarchive="y">true</enabled>
    <type>lookfrom</type>
    <internal type="bool">false</internal>
    <config>
      <from-model type="bool">true</from-model>
      <from-model-idx type="int">0</from-model-idx>
      <ground-level-nearplane-m type="double">0.5f</ground-level-nearplane-m>
      <default-field-of-view-deg type="double">55.0</default-field-of-view-deg>
    </config>
  </view>

  <submodels>
    <serviceable type="bool">1</serviceable>
    <path>Aircraft/ATI-Resolution/Submodels.xml</path>
  </submodels>

  <panel>
   <path>Aircraft/ATI-Resolution/UASPanels/Panel-glass1.xml</path>
   <visibility type="bool" archive="n">false</visibility>
  </panel>

  <model>
   <path archive="y">Aircraft/ATI-Resolution/Models/Resolution3.xml</path>
   <livery>
     <file type="string"/>
   </livery>
  </model>
  
  <menubar>
    <default>
      <menu n="50">
        <label>Resolution-3</label>
        <item>
          <label>Operator Dialog (internal/nasal autopilot)</label>
          <binding>
            <command>nasal</command>
            <script>uas.nasal_ap_dialog.open()</script>
          </binding>
        </item>
        <item>
          <label>Operator Dialog (external/avior autopilot)</label>
          <binding>
            <command>nasal</command>
            <script>uas.avior_ap_dialog.open()</script>
          </binding>
        </item>
      </menu>
    </default>
  </menubar>

  <view n="0">
   <internal archive="y">true</internal>
   <config>
     <x-offset-m archive="y">0.0</x-offset-m>
     <y-offset-m archive="y">0.26</y-offset-m>
     <z-offset-m archive="y">0.34</z-offset-m>
     <pitch-offset-deg>-8</pitch-offset-deg>
   </config>
  </view>

  <view n="1">
   <config>
    <target-z-offset-m archive="y" type="double">0.374</target-z-offset-m>
   </config>
  </view>

  <view n="2">
   <config>
    <target-z-offset-m archive="y" type="double">0.374</target-z-offset-m>
   </config>
  </view>

  <view n="3">
   <config>
    <target-z-offset-m archive="y" type="double">0.374</target-z-offset-m>
   </config>
  </view>

  <view n="4">
   <config>
    <target-z-offset-m archive="y" type="double">0.374</target-z-offset-m>
   </config>
  </view>

  <view n="5">
   <config>
    <target-z-offset-m archive="y" type="double">0.374</target-z-offset-m>
   </config>
  </view>

  <view n="6">
   <config>
    <target-z-offset-m archive="y" type="double">0.374</target-z-offset-m>
   </config>
  </view>

  <chase-distance-m archive="y" type="double">-5.0</chase-distance-m>

  <multiplay>
    <chat_display>1</chat_display>
    <generic>
      <int type="bool">0</int>  <!-- smoke -->
      <int type="bool">0</int>  <!-- trajectory markers -->
    </generic>
  </multiplay>

  <help>
    <title>Resolution 3</title>
    <line>Cruise speed: 50-75 kts</line>
    <line>Never-exceed (Vne): 85 kts</line>
    <line>Best Glide (Vglide): 35 kts</line>
    <line>Maneuvering (Va): 50 kts</line>
    <line>Approach speed: 25-35 kts</line>
    <line>Stall speed (Vs): 20 kts</line>
  </help>

 </sim>

 <controls>
  <flight>
   <aileron-trim>0.0</aileron-trim>   <!-- fixed -->
   <elevator-trim>0.00</elevator-trim> <!-- controllable -->
   <rudder-trim>0.00</rudder-trim>     <!-- fixed -->
  </flight>
  <engines>
   <engine n="0">
    <magnetos>3</magnetos>
   </engine>
  </engines>
  <door>1.0</door>
 </controls>

 <engines>
  <engine>
   <rpm type="double">700</rpm>
  </engine>
 </engines>
  
 <input>
   <keyboard>
     <key n="9">
       <name>Ctrl-I</name>
       <desc>Show Master Configuration Dialog</desc>
       <binding>
         <command>nasal</command>
         <script>uas.nasal_ap_dialog.toggle()</script>
       </binding>
     </key>
     <key n="108">
      <name>l</name>
      <desc>Show livery select dialog</desc>
      <binding>
        <command>nasal</command>
        <script>aircraft.livery.dialog.toggle()</script>
      </binding>
    </key>
   </keyboard>
 </input>

 <nasal>
  <uas>
   <file>Aircraft/ATI-Resolution/Nasal/Resolution3.nas</file>
   <file>Aircraft/ATI-Resolution/Nasal/avior.nas</file>
   <!-- <file>Aircraft/ATI-Resolution/Nasal/roll-doublet.nas</file> -->
   <file>Aircraft/ATI-Resolution/Nasal/ati-uas-demo.nas</file>
   <!--<file>Aircraft/ATI-Resolution/Nasal/rsvp.nas</file> -->
   <!--<file>Aircraft/ATI-Resolution/Nasal/wmi.nas</file> -->
  </uas>
 </nasal>

</PropertyList>
