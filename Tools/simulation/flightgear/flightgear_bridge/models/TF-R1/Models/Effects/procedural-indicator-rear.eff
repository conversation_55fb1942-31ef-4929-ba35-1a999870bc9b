<?xml version="1.0" encoding="utf-8"?>
<PropertyList>

    <name>procedural-light-headlight-left</name>
    <inherits-from>Effects/procedural-light</inherits-from>

    <parameters>
        <light_color_base_r type="float">9.900</light_color_base_r>
        <light_color_base_g type="float">0.900</light_color_base_g>
        <light_color_base_b type="float">0.200</light_color_base_b>
        <light_color_center_r type="float">0.9</light_color_center_r>
        <light_color_center_g type="float">0.9</light_color_center_g>
        <light_color_center_b type="float">0.1</light_color_center_b>
        <intensity_scale type="float">1.0</intensity_scale>

        <!-- Arc is 110 deg, is 55 deg per side, giving 35 deg from wing -->
        <pointing_x type="float">-1.0</pointing_x>
        <pointing_y type="float">0.2</pointing_y>
        <pointing_z type="float">0.2</pointing_z>

        <is_directional type="bool">true</is_directional>
        <is_strobe type="bool">false</is_strobe>
        <inner_angle type="float">0.8</inner_angle>
        <outer_angle type="float">1.0</outer_angle>
        <zero_angle type="float">0.9</zero_angle>
        <outer_gain type="float">0.2</outer_gain>
    </parameters>

</PropertyList>
