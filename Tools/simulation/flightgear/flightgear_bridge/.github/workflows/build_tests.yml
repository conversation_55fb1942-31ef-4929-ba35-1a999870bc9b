name: Build Tests

on:
  push:
    branches:
    - 'master'
  pull_request:
    branches:
    - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ubuntu_release: [
          bionic
          ]
    container: px4io/px4-dev-simulation-${{ matrix.ubuntu_release }}:2020-04-01
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}
    - name: build
      run:
        mkdir build;
        cd build;
        cmake ..;
        make
