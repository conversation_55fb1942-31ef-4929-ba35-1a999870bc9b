<?xml version="1.0"?>
  <PropertyList> 
    <path>pilot-b.ac</path>
    


<!-- arms -->
 <animation>
  <type>rotate</type>
  <object-name>arm1.R</object-name>
  <object-name>arm2.R</object-name>
  <object-name>hand.R</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>90</factor>
  <center>
   <x-m>-0.015</x-m>
   <y-m>0.23</y-m>
   <z-m>0.45</z-m>
  </center>
  <axis>
   <x>-1.0</x>
   <y>0.03</y>
   <z>0.25</z>
  </axis>
 </animation>
<!--
 <animation>
  <type>rotate</type>
  <object-name>arm1.R</object-name>
  <object-name>arm2.R</object-name>
  <object-name>hand.R</object-name>
  <property>/controls/flight/elevator</property>
  <factor>20</factor>
  <center>
   <x-m>-0.015</x-m>
   <y-m>0.23</y-m>
   <z-m>0.45</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.0</y>
   <z>1.0</z>
  </axis>
 </animation>
 <animation>
  <type>rotate</type>
  <object-name>arm1.R</object-name>
  <object-name>arm2.R</object-name>
  <object-name>hand.R</object-name>
  <property>/controls/flight/aileron</property>
  <factor>-15</factor>
  <center>
   <x-m>-0.015</x-m>
   <y-m>0.23</y-m>
   <z-m>0.45</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.0</y>
   <z>1.0</z>
  </axis>
 </animation>
-->
 <animation>
  <type>rotate</type>
  <object-name>arm2.R</object-name>
  <object-name>hand.R</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>75</factor>
  <center>
   <x-m>0.007</x-m>
   <y-m>0.533</y-m>
   <z-m>0.455</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.0</y>
   <z>1.0</z>
  </axis>
 </animation>
<!--
 <animation>
  <type>rotate</type>
  <object-name>arm2.R</object-name>
  <object-name>hand.R</object-name>
  <property>/controls/flight/elevator</property>
  <factor>-15</factor>
  <center>
   <x-m>0.007</x-m>
   <y-m>0.533</y-m>
   <z-m>0.455</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.0</y>
   <z>1.0</z>
  </axis>
 </animation>

 <animation>
  <type>rotate</type>
  <object-name>arm2.R</object-name>
  <object-name>hand.R</object-name>
  <property>/controls/flight/aileron</property>
  <factor>60</factor>
  <center>
   <x-m>0.0</x-m>
   <y-m>0.16</y-m>
   <z-m>0.33</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>1.0</y>
   <z>-0.033</z>
  </axis>
 </animation>
-->
 <animation>
  <type>rotate</type>
  <object-name>arm1.L</object-name>
  <object-name>arm2.L</object-name>
  <object-name>hand.L</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>90</factor>
  <center>
   <x-m>-0.015</x-m>
   <y-m>-0.23</y-m>
   <z-m>0.45</z-m>
  </center>
  <axis>
   <x>1.0</x>
   <y>-0.13</y>
   <z>-0.15</z>
  </axis>
 </animation>
 <animation>
  <type>rotate</type>
  <object-name>arm2.L</object-name>
  <object-name>hand.L</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>85</factor>
  <center>
   <x-m>0.007</x-m>
   <y-m>-0.533</y-m>
   <z-m>0.455</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.0</y>
   <z>-1.0</z>
  </axis>
 </animation>
<!-- legs -->
 <animation>
  <type>rotate</type>
  <object-name>leg1.R</object-name>
  <object-name>leg2.R</object-name>
  <object-name>foot.R</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>80</factor>
  <center>
   <x-m>0.012</x-m>
   <y-m>0.199</y-m>
   <z-m>-0.016</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.6</y>
   <z>0.0</z>
  </axis>
 </animation>

 <animation>
  <type>rotate</type>
  <object-name>leg2.R</object-name>
  <object-name>foot.R</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>40</factor>
  <center>
   <x-m>-0.064</x-m>
   <y-m>0.109</y-m>
   <z-m>-0.35</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>-0.1</y>
   <z>0.0</z>
  </axis>
 </animation>

 <animation>
  <type>rotate</type>
  <object-name>leg1.L</object-name>
  <object-name>leg2.L</object-name>
  <object-name>foot.L</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>80</factor>
  <center>
   <x-m>0.012</x-m>
   <y-m>-0.199</y-m>
   <z-m>-0.016</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.6</y>
   <z>0.0</z>
  </axis>
 </animation>

 <animation>
  <type>rotate</type>
  <object-name>leg2.L</object-name>
  <object-name>foot.L</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>40</factor>
  <center>
   <x-m>-0.064</x-m>
   <y-m>-0.109</y-m>
   <z-m>-0.35</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>-0.1</y>
   <z>0.0</z>
  </axis>
 </animation>

<!-- head -->
 <animation>
  <type>rotate</type>
  <object-name>head</object-name>
  <property>sim/model/crew/pilot/visible</property>
  <factor>10</factor>
  <center>
   <x-m>0.055</x-m>
   <y-m>0.0</y-m>
   <z-m>0.563</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>-1.0</y>
   <z>0.0</z>
  </axis>
 </animation>

<!-- movement -->

<!--
 <animation>
  <type>rotate</type>
  <object-name>arm2.R</object-name>
  <object-name>hand.R</object-name>
  <property>/controls/flight/elevator</property>
  <factor>15</factor>

  <center>
   <x-m>0.08</x-m>
   <y-m>0.22</y-m>
   <z-m>0.45</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.0</y>
   <z>1.0</z>
  </axis>
 </animation>

-->



  </PropertyList>
