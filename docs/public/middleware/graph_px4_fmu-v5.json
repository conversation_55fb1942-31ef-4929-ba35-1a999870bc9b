{"nodes": [{"id": "m_fw_autotune_attitude_control", "name": "fw_autotune_attitude_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_autotune_attitude_control", "name": "mc_autotune_attitude_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_landing_target_estimator", "name": "landing_target_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_temperature_compensation", "name": "temperature_compensation", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lightware_laser_serial", "name": "lightware_laser_serial", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_attitude_estimator_q", "name": "attitude_estimator_q", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lightware_laser_i2c", "name": "lightware_laser_i2c", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_flight_mode_manager", "name": "flight_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_lat_lon_control", "name": "fw_lat_lon_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mag_bias_estimator", "name": "mag_bias_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_is31fl3195", "name": "rgbled_is31fl3195", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_airspeed_selector", "name": "airspeed_selector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_control_allocator", "name": "control_allocator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_pos_control", "name": "rover_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_cdcacm_autostart", "name": "cdcacm_autostart", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gyro_calibration", "name": "gyro_calibration", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uxrce_dds_client", "name": "uxrce_dds_client", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vtol_att_control", "name": "vtol_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_ncp5623c", "name": "rgbled_ncp5623c", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pca9685_pwm_out", "name": "pca9685_pwm_out", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_frsky_telemetry", "name": "frsky_telemetry", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_feedback", "name": "camera_feedback", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_mode_manager", "name": "fw_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_rate_control", "name": "fw_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_rate_control", "name": "mc_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_baro_sim", "name": "sensor_baro_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uuv_att_control", "name": "uuv_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uuv_pos_control", "name": "uuv_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_capture", "name": "camera_capture", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_trigger", "name": "camera_trigger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ulanding_radar", "name": "ulanding_radar", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_hott_telemetry", "name": "hott_telemetry", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_battery_status", "name": "battery_status", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_att_control", "name": "fw_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_manual_control", "name": "manual_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_att_control", "name": "mc_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_pos_control", "name": "mc_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_gps_sim", "name": "sensor_gps_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_mag_sim", "name": "sensor_mag_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_lp5562", "name": "rgbled_lp5562", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_safety_button", "name": "safety_button", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_land_detector", "name": "land_detector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_simulator_sih", "name": "simulator_sih", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_actuator_test", "name": "actuator_test", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tune_control", "name": "tune_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_esc_battery", "name": "esc_battery", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_out_sim", "name": "pwm_out_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_led_control", "name": "led_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_batt_smbus", "name": "batt_smbus", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_teraranger", "name": "terar<PERSON>", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_pwm", "name": "rgbled_pwm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tone_alarm", "name": "tone_alarm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_send_event", "name": "send_event", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_board_adc", "name": "board_adc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms5525dso", "name": "ms5525dso", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_adis16448", "name": "adis16448", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm42688p", "name": "icm42688p", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lsm303agr", "name": "lsm303agr", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mmc5983ma", "name": "mmc5983ma", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_thoneflow", "name": "thoneflow", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_input", "name": "pwm_input", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_commander", "name": "commander", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_navigator", "name": "navigator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rc_update", "name": "rc_update", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icp101xx", "name": "icp101xx", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icp201xx", "name": "icp201xx", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms4525do", "name": "ms4525do", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20602", "name": "icm20602", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20689", "name": "icm20689", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20948", "name": "icm20948", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_qmc5883l", "name": "qmc5883l", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vcm1193l", "name": "vcm1193l", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rc_input", "name": "rc_input", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_load_mon", "name": "load_mon", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ads1115", "name": "ads1115", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lps22hb", "name": "lps22hb", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lps33hw", "name": "lps33hw", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mpc2520", "name": "mpc2520", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_cm8jl65", "name": "cm8jl65", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tf02pro", "name": "tf02pro", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vl53l0x", "name": "vl53l0x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vl53l1x", "name": "vl53l1x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ak09916", "name": "ak09916", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_hmc5883", "name": "hmc5883", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ist8308", "name": "ist8308", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ist8310", "name": "ist8310", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lis3mdl", "name": "lis3mdl", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_iis2mdc", "name": "iis2mdc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_paa3905", "name": "paa3905", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_paw3902", "name": "paw3902", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pmw3901", "name": "pmw3901", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_px4flow", "name": "px4flow", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_msp_osd", "name": "msp_osd", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_out", "name": "pwm_out", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_crsf_rc", "name": "crsf_rc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ghst_rc", "name": "ghst_rc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sbus_rc", "name": "sbus_rc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dataman", "name": "dataman", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mavlink", "name": "mavlink", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensors", "name": "sensors", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmp280", "name": "bmp280", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmp388", "name": "bmp388", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmp581", "name": "bmp581", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dps310", "name": "dps310", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms5611", "name": "ms5611", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ll40ls", "name": "ll40ls", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tfmini", "name": "tfmini", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_heater", "name": "heater", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmi055", "name": "bmi055", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_irlock", "name": "irlock", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled", "name": "rgbled", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ak8963", "name": "ak8963", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmm150", "name": "bmm150", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rm3100", "name": "rm3100", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_atxxxx", "name": "atxxxx", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ina226", "name": "ina226", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dsm_rc", "name": "dsm_rc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_batmon", "name": "batmon", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uavcan", "name": "uavcan", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gimbal", "name": "gimbal", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_logger", "name": "logger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_spa06", "name": "spa06", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_spl06", "name": "spl06", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sdp3x", "name": "sdp3x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dshot", "name": "dshot", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sht3x", "name": "sht3x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_px4io", "name": "px4io", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ekf2", "name": "ekf2", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gps", "name": "gps", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bst", "name": "bst", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "t_vehicle_angular_velocity_groundtruth", "name": "vehicle_angular_velocity_groundtruth", "type": "topic", "color": "#4161d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_distance_sensor_mode_change_request", "name": "distance_sensor_mode_change_request", "type": "topic", "color": "#c841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_global_position_groundtruth", "name": "vehicle_global_position_groundtruth", "type": "topic", "color": "#d84147", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_longitudinal_control_configuration", "name": "longitudinal_control_configuration", "type": "topic", "color": "#d8ae41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_controller_landing_status", "name": "position_controller_landing_status", "type": "topic", "color": "#d8b541", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position_groundtruth", "name": "vehicle_local_position_groundtruth", "type": "topic", "color": "#9bd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_set_manual_control", "name": "gimbal_manager_set_manual_control", "type": "topic", "color": "#41d867", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_longitudinal_setpoint", "name": "fixed_wing_longitudinal_setpoint", "type": "topic", "color": "#41d89b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_autotune_attitude_control_status", "name": "autotune_attitude_control_status", "type": "topic", "color": "#cf41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position_setpoint", "name": "vehicle_local_position_setpoint", "type": "topic", "color": "#d87441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_attitude_status", "name": "gimbal_device_attitude_status", "type": "topic", "color": "#4174d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_lateral_control_configuration", "name": "lateral_control_configuration", "type": "topic", "color": "#6141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_register_ext_component_reply", "name": "register_ext_component_reply", "type": "topic", "color": "#d85a41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fw_virtual_attitude_setpoint", "name": "fw_virtual_attitude_setpoint", "type": "topic", "color": "#c2d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mc_virtual_attitude_setpoint", "name": "mc_virtual_attitude_setpoint", "type": "topic", "color": "#414dd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude_groundtruth", "name": "vehicle_attitude_groundtruth", "type": "topic", "color": "#d841cf", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_lateral_setpoint", "name": "fixed_wing_lateral_setpoint", "type": "topic", "color": "#d85441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_set_attitude", "name": "gimbal_manager_set_attitude", "type": "topic", "color": "#6ed841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_controller_status", "name": "position_controller_status", "type": "topic", "color": "#54d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_information", "name": "gimbal_manager_information", "type": "topic", "color": "#41d8c2", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_controls_status_0", "name": "actuator_controls_status_0", "type": "topic", "color": "#41a8d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorControlsStatus.msg"}, {"id": "t_gimbal_device_set_attitude", "name": "gimbal_device_set_attitude", "type": "topic", "color": "#d841c8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_information", "name": "gimbal_device_information", "type": "topic", "color": "#d84141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude_setpoint", "name": "vehicle_attitude_setpoint", "type": "topic", "color": "#d5d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_operator_id", "name": "open_drone_id_operator_id", "type": "topic", "color": "#bbd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_selector_status", "name": "estimator_selector_status", "type": "topic", "color": "#88d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_setpoint_triplet", "name": "position_setpoint_triplet", "type": "topic", "color": "#7bd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_runway_control", "name": "fixed_wing_runway_control", "type": "topic", "color": "#4154d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tiltrotor_extra_controls", "name": "tiltrotor_extra_controls", "type": "topic", "color": "#4188d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_uavcan_parameter_request", "name": "uavcan_parameter_request", "type": "topic", "color": "#4181d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_control_allocator_status", "name": "control_allocator_status", "type": "topic", "color": "#8841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_arm_status", "name": "open_drone_id_arm_status", "type": "topic", "color": "#d84161", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_thrust_setpoint", "name": "vehicle_thrust_setpoint", "type": "topic", "color": "#d8bb41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/VehicleThrustSetpoint.msg"}, {"id": "t_launch_detection_status", "name": "launch_detection_status", "type": "topic", "color": "#c8d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_flight_phase_estimation", "name": "flight_phase_estimation", "type": "topic", "color": "#94d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_setpoint", "name": "manual_control_setpoint", "type": "topic", "color": "#8ed841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_torque_setpoint", "name": "vehicle_torque_setpoint", "type": "topic", "color": "#47d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/VehicleTorqueSetpoint.msg"}, {"id": "t_failure_detector_status", "name": "failure_detector_status", "type": "topic", "color": "#41d854", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_global_position", "name": "vehicle_global_position", "type": "topic", "color": "#41d881", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_visual_odometry", "name": "vehicle_visual_odometry", "type": "topic", "color": "#5a41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_switches", "name": "manual_control_switches", "type": "topic", "color": "#d84154", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_uavcan_parameter_value", "name": "uavcan_parameter_value", "type": "topic", "color": "#d8a841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status_flags", "name": "estimator_status_flags", "type": "topic", "color": "#41cfd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position", "name": "vehicle_local_position", "type": "topic", "color": "#416ed8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_rates_setpoint", "name": "vehicle_rates_setpoint", "type": "topic", "color": "#4147d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_mocap_odometry", "name": "vehicle_mocap_odometry", "type": "topic", "color": "#5441d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_sensor_bias", "name": "estimator_sensor_bias", "type": "topic", "color": "#d86141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_self_id", "name": "open_drone_id_self_id", "type": "topic", "color": "#41d85a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_offboard_control_mode", "name": "offboard_control_mode", "type": "topic", "color": "#41d8c8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_differential_pressure", "name": "differential_pressure", "type": "topic", "color": "#b541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_land_detected", "name": "vehicle_land_detected", "type": "topic", "color": "#bb41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_status", "name": "gimbal_manager_status", "type": "topic", "color": "#d8419b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_outputs_sim", "name": "actuator_outputs_sim", "type": "topic", "color": "#d84741", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorOutputs.msg"}, {"id": "t_open_drone_id_system", "name": "open_drone_id_system", "type": "topic", "color": "#41d8d5", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_servos_trim", "name": "actuator_servos_trim", "type": "topic", "color": "#41aed8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_control_mode", "name": "vehicle_control_mode", "type": "topic", "color": "#a141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_constraints", "name": "vehicle_constraints", "type": "topic", "color": "#41d84d", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_target_pose", "name": "landing_target_pose", "type": "topic", "color": "#41d8bb", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_optical_flow", "name": "sensor_optical_flow", "type": "topic", "color": "#41a1d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vtol_vehicle_status", "name": "vtol_vehicle_status", "type": "topic", "color": "#4194d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_figure_eight_status", "name": "figure_eight_status", "type": "topic", "color": "#9b41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command_ack", "name": "vehicle_command_ack", "type": "topic", "color": "#d84174", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_trajectory_setpoint", "name": "trajectory_setpoint", "type": "topic", "color": "#d84167", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed_validated", "name": "airspeed_validated", "type": "topic", "color": "#d8cf41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu_status", "name": "vehicle_imu_status", "type": "topic", "color": "#41d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensors_status_imu", "name": "sensors_status_imu", "type": "topic", "color": "#41c2d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_transponder_report", "name": "transponder_report", "type": "topic", "color": "#4741d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear_wheel", "name": "landing_gear_wheel", "type": "topic", "color": "#d841a1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_power_button_state", "name": "power_button_state", "type": "topic", "color": "#d8418e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_hygrometer", "name": "sensor_hygrometer", "type": "topic", "color": "#aed841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_spoilers_setpoint", "name": "spoilers_setpoint", "type": "topic", "color": "#a8d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/NormalizedUnsignedSetpoint.msg"}, {"id": "t_mount_orientation", "name": "mount_orientation", "type": "topic", "color": "#a1d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_time_estimate", "name": "rtl_time_estimate", "type": "topic", "color": "#41d8b5", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_correction", "name": "sensor_correction", "type": "topic", "color": "#d84181", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_v1_command", "name": "gimbal_v1_command", "type": "topic", "color": "#d8414d", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude", "name": "vehicle_attitude", "type": "topic", "color": "#d87b41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_telemetry_status", "name": "telemetry_status", "type": "topic", "color": "#d8a141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status", "name": "estimator_status", "type": "topic", "color": "#d8d541", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_outputs", "name": "actuator_outputs", "type": "topic", "color": "#61d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorOutputs.msg"}, {"id": "t_rc_parameter_map", "name": "rc_parameter_map", "type": "topic", "color": "#5ad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_response", "name": "dataman_response", "type": "topic", "color": "#41d86e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_odometry", "name": "vehicle_odometry", "type": "topic", "color": "#41d874", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_navigator_status", "name": "navigator_status", "type": "topic", "color": "#6741d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_selection", "name": "sensor_selection", "type": "topic", "color": "#d8417b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command", "name": "vehicle_command", "type": "topic", "color": "#d84d41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_distance_sensor", "name": "distance_sensor", "type": "topic", "color": "#d88141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream_ack", "name": "ulog_stream_ack", "type": "topic", "color": "#d88841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gps_inject_data", "name": "gps_inject_data", "type": "topic", "color": "#d8c241", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_combined", "name": "sensor_combined", "type": "topic", "color": "#67d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_result", "name": "geofence_result", "type": "topic", "color": "#41d87b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_request", "name": "dataman_request", "type": "topic", "color": "#41d88e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_motors", "name": "actuator_motors", "type": "topic", "color": "#41c8d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_controls", "name": "gimbal_controls", "type": "topic", "color": "#4141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_key_value", "name": "debug_key_value", "type": "topic", "color": "#c241d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_status", "name": "geofence_status", "type": "topic", "color": "#d841bb", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_armed", "name": "actuator_armed", "type": "topic", "color": "#d8c841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_status", "name": "vehicle_status", "type": "topic", "color": "#4dd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_trigger", "name": "camera_trigger", "type": "topic", "color": "#41b5d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_flaps_setpoint", "name": "flaps_setpoint", "type": "topic", "color": "#4d41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/NormalizedUnsignedSetpoint.msg"}, {"id": "t_mission_result", "name": "mission_result", "type": "topic", "color": "#a841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_satellite_info", "name": "satellite_info", "type": "topic", "color": "#d841d5", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_failsafe_flags", "name": "failsafe_flags", "type": "topic", "color": "#d841c2", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_capture", "name": "camera_capture", "type": "topic", "color": "#d841ae", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_battery_status", "name": "battery_status", "type": "topic", "color": "#d841a8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_action_request", "name": "action_request", "type": "topic", "color": "#d8416e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_takeoff_status", "name": "takeoff_status", "type": "topic", "color": "#d8415a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_status", "name": "camera_status", "type": "topic", "color": "#d88e41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_home_position", "name": "home_position", "type": "topic", "color": "#41d847", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_test", "name": "actuator_test", "type": "topic", "color": "#41d8ae", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_irlock_report", "name": "irlock_report", "type": "topic", "color": "#41bbd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_safety_button", "name": "safety_button", "type": "topic", "color": "#7b41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_logger_status", "name": "logger_status", "type": "topic", "color": "#8e41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_health_report", "name": "health_report", "type": "topic", "color": "#9441d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_system_power", "name": "system_power", "type": "topic", "color": "#d86e41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tune_control", "name": "tune_control", "type": "topic", "color": "#74d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_accel", "name": "sensor_accel", "type": "topic", "color": "#6e41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_orbit_status", "name": "orbit_status", "type": "topic", "color": "#7441d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear", "name": "landing_gear", "type": "topic", "color": "#8141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_px4io_status", "name": "px4io_status", "type": "topic", "color": "#d84194", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_array", "name": "debug_array", "type": "topic", "color": "#d86741", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_baro", "name": "sensor_baro", "type": "topic", "color": "#d89b41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tecs_status", "name": "tecs_status", "type": "topic", "color": "#b5d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gyro", "name": "sensor_gyro", "type": "topic", "color": "#41d861", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu", "name": "vehicle_imu", "type": "topic", "color": "#41d8a8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_led_control", "name": "led_control", "type": "topic", "color": "#41d5d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_roi", "name": "vehicle_roi", "type": "topic", "color": "#419bd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_value", "name": "debug_value", "type": "topic", "color": "#415ad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream", "name": "ulog_stream", "type": "topic", "color": "#d541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_mag", "name": "sensor_mag", "type": "topic", "color": "#d89441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_esc_status", "name": "esc_status", "type": "topic", "color": "#cfd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_vect", "name": "debug_vect", "type": "topic", "color": "#41d8a1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_status", "name": "rtl_status", "type": "topic", "color": "#418ed8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gps", "name": "sensor_gps", "type": "topic", "color": "#417bd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/SensorGps.msg"}, {"id": "t_adc_report", "name": "adc_report", "type": "topic", "color": "#d84188", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_pwm_input", "name": "pwm_input", "type": "topic", "color": "#ae41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_input_rc", "name": "input_rc", "type": "topic", "color": "#41d8cf", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed", "name": "airspeed", "type": "topic", "color": "#d841b5", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/EstimatorAidSource1d.msg"}, {"id": "t_mission", "name": "mission", "type": "topic", "color": "#81d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_cpuload", "name": "cpuload", "type": "topic", "color": "#41d888", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_event", "name": "event", "type": "topic", "color": "#41d894", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_wind", "name": "wind", "type": "topic", "color": "#4167d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/Wind.msg"}], "links": [{"source": "m_ads1115", "target": "t_adc_report", "color": "#d84188", "style": "dashed"}, {"source": "m_board_adc", "target": "t_system_power", "color": "#d86e41", "style": "dashed"}, {"source": "m_board_adc", "target": "t_adc_report", "color": "#d84188", "style": "dashed"}, {"source": "m_bmp280", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_bmp388", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_bmp581", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_dps310", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_spa06", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_spl06", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_icp101xx", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_icp201xx", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_lps22hb", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_lps33hw", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_mpc2520", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_ms5611", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_batt_smbus", "target": "t_battery_status", "color": "#d841a8", "style": "dashed"}, {"source": "m_camera_capture", "target": "t_camera_trigger", "color": "#41b5d8", "style": "dashed"}, {"source": "m_camera_capture", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_camera_trigger", "color": "#41b5d8", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_ms4525do", "target": "t_differential_pressure", "color": "#b541d8", "style": "dashed"}, {"source": "m_ms5525dso", "target": "t_differential_pressure", "color": "#b541d8", "style": "dashed"}, {"source": "m_sdp3x", "target": "t_differential_pressure", "color": "#b541d8", "style": "dashed"}, {"source": "m_cm8jl65", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_lightware_laser_i2c", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_lightware_laser_serial", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_ll40ls", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_teraranger", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_tf02pro", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_tfmini", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_ulanding_radar", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_vl53l0x", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_vl53l1x", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_dshot", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_armed", "color": "#d8c841", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_outputs", "color": "#61d841", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_motors", "color": "#41c8d8", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_test", "color": "#41d8ae", "style": "dashed"}, {"source": "m_dshot", "target": "t_esc_status", "color": "#cfd841", "style": "dashed"}, {"source": "m_gps", "target": "t_gps_inject_data", "color": "#d8c241", "style": "dashed"}, {"source": "m_gps", "target": "t_satellite_info", "color": "#d841d5", "style": "dashed"}, {"source": "m_gps", "target": "t_sensor_gps", "color": "#417bd8", "style": "dashed"}, {"source": "m_sht3x", "target": "t_sensor_hygrometer", "color": "#aed841", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_gyro", "color": "#41d861", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_accel", "color": "#6e41d8", "style": "dashed"}, {"source": "m_bmi055", "target": "t_sensor_gyro", "color": "#41d861", "style": "dashed"}, {"source": "m_bmi055", "target": "t_sensor_accel", "color": "#6e41d8", "style": "dashed"}, {"source": "m_icm20602", "target": "t_sensor_gyro", "color": "#41d861", "style": "dashed"}, {"source": "m_icm20602", "target": "t_sensor_accel", "color": "#6e41d8", "style": "dashed"}, {"source": "m_icm20689", "target": "t_sensor_gyro", "color": "#41d861", "style": "dashed"}, {"source": "m_icm20689", "target": "t_sensor_accel", "color": "#6e41d8", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_gyro", "color": "#41d861", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_accel", "color": "#6e41d8", "style": "dashed"}, {"source": "m_icm42688p", "target": "t_sensor_gyro", "color": "#41d861", "style": "dashed"}, {"source": "m_icm42688p", "target": "t_sensor_accel", "color": "#6e41d8", "style": "dashed"}, {"source": "m_irlock", "target": "t_irlock_report", "color": "#41bbd8", "style": "dashed"}, {"source": "m_ak09916", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_ak8963", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_bmm150", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_hmc5883", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_ist8308", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_ist8310", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_lis3mdl", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_lsm303agr", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_mmc5983ma", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_qmc5883l", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_rm3100", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_iis2mdc", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_vcm1193l", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_paa3905", "target": "t_sensor_optical_flow", "color": "#41a1d8", "style": "dashed"}, {"source": "m_paw3902", "target": "t_sensor_optical_flow", "color": "#41a1d8", "style": "dashed"}, {"source": "m_pmw3901", "target": "t_sensor_optical_flow", "color": "#41a1d8", "style": "dashed"}, {"source": "m_px4flow", "target": "t_sensor_optical_flow", "color": "#41a1d8", "style": "dashed"}, {"source": "m_thoneflow", "target": "t_sensor_optical_flow", "color": "#41a1d8", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_armed", "color": "#d8c841", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_outputs", "color": "#61d841", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_motors", "color": "#41c8d8", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_test", "color": "#41d8ae", "style": "dashed"}, {"source": "m_ina226", "target": "t_battery_status", "color": "#d841a8", "style": "dashed"}, {"source": "m_pwm_input", "target": "t_pwm_input", "color": "#ae41d8", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_armed", "color": "#d8c841", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_outputs", "color": "#61d841", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_motors", "color": "#41c8d8", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_test", "color": "#41d8ae", "style": "dashed"}, {"source": "m_px4io", "target": "t_tune_control", "color": "#74d841", "style": "dashed"}, {"source": "m_px4io", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_px4io", "target": "t_px4io_status", "color": "#d84194", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_armed", "color": "#d8c841", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_outputs", "color": "#61d841", "style": "dashed"}, {"source": "m_px4io", "target": "t_input_rc", "color": "#41d8cf", "style": "dashed"}, {"source": "m_px4io", "target": "t_led_control", "color": "#41d5d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_motors", "color": "#41c8d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_test", "color": "#41d8ae", "style": "dashed"}, {"source": "m_px4io", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_px4io", "target": "t_safety_button", "color": "#7b41d8", "style": "dashed"}, {"source": "m_crsf_rc", "target": "t_input_rc", "color": "#41d8cf", "style": "dashed"}, {"source": "m_dsm_rc", "target": "t_input_rc", "color": "#41d8cf", "style": "dashed"}, {"source": "m_dsm_rc", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_dsm_rc", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_ghst_rc", "target": "t_input_rc", "color": "#41d8cf", "style": "dashed"}, {"source": "m_sbus_rc", "target": "t_input_rc", "color": "#41d8cf", "style": "dashed"}, {"source": "m_rc_input", "target": "t_input_rc", "color": "#41d8cf", "style": "dashed"}, {"source": "m_rc_input", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_rc_input", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_safety_button", "target": "t_tune_control", "color": "#74d841", "style": "dashed"}, {"source": "m_safety_button", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_safety_button", "target": "t_led_control", "color": "#41d5d8", "style": "dashed"}, {"source": "m_safety_button", "target": "t_safety_button", "color": "#7b41d8", "style": "dashed"}, {"source": "m_batmon", "target": "t_battery_status", "color": "#d841a8", "style": "dashed"}, {"source": "m_hott_telemetry", "target": "t_esc_status", "color": "#cfd841", "style": "dashed"}, {"source": "m_tone_alarm", "target": "t_tune_control", "color": "#74d841", "style": "dashed"}, {"source": "m_uavcan", "target": "t_tune_control", "color": "#74d841", "style": "dashed"}, {"source": "m_uavcan", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_uavcan", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_armed", "color": "#d8c841", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_outputs", "color": "#61d841", "style": "dashed"}, {"source": "m_uavcan", "target": "t_led_control", "color": "#41d5d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_open_drone_id_arm_status", "color": "#d84161", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_motors", "color": "#41c8d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_test", "color": "#41d8ae", "style": "dashed"}, {"source": "m_uavcan", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_uavcan", "target": "t_esc_status", "color": "#cfd841", "style": "dashed"}, {"source": "m_uavcan", "target": "t_uavcan_parameter_value", "color": "#d8a841", "style": "dashed"}, {"source": "m_uavcan", "target": "t_safety_button", "color": "#7b41d8", "style": "dashed"}, {"source": "m_airspeed_selector", "target": "t_airspeed_validated", "color": "#d8cf41", "style": "dashed"}, {"source": "m_attitude_estimator_q", "target": "t_vehicle_attitude", "color": "#d87b41", "style": "dashed"}, {"source": "m_battery_status", "target": "t_battery_status", "color": "#d841a8", "style": "dashed"}, {"source": "m_camera_feedback", "target": "t_camera_capture", "color": "#d841ae", "style": "dashed"}, {"source": "m_commander", "target": "t_tune_control", "color": "#74d841", "style": "dashed"}, {"source": "m_commander", "target": "t_home_position", "color": "#41d847", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_armed", "color": "#d8c841", "style": "dashed"}, {"source": "m_commander", "target": "t_failure_detector_status", "color": "#41d854", "style": "dashed"}, {"source": "m_commander", "target": "t_power_button_state", "color": "#d8418e", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_control_mode", "color": "#a141d8", "style": "dashed"}, {"source": "m_commander", "target": "t_led_control", "color": "#41d5d8", "style": "dashed"}, {"source": "m_commander", "target": "t_health_report", "color": "#9441d8", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_test", "color": "#41d8ae", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_status", "color": "#4dd841", "style": "dashed"}, {"source": "m_commander", "target": "t_failsafe_flags", "color": "#d841c2", "style": "dashed"}, {"source": "m_commander", "target": "t_register_ext_component_reply", "color": "#d85a41", "style": "dashed"}, {"source": "m_commander", "target": "t_event", "color": "#41d894", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_control_allocator_status", "color": "#8841d8", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_motors", "color": "#41c8d8", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_servos_trim", "color": "#41aed8", "style": "dashed"}, {"source": "m_dataman", "target": "t_dataman_response", "color": "#41d86e", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status_flags", "color": "#41cfd8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_sensor_bias", "color": "#d86141", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_attitude", "color": "#d87b41", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_odometry", "color": "#41d874", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_local_position", "color": "#416ed8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_global_position", "color": "#41d881", "style": "dashed"}, {"source": "m_ekf2", "target": "t_wind", "color": "#4167d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status", "color": "#d8d541", "style": "dashed"}, {"source": "m_ekf2", "target": "t_sensor_selection", "color": "#d8417b", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_selector_status", "color": "#88d841", "style": "dashed"}, {"source": "m_esc_battery", "target": "t_battery_status", "color": "#d841a8", "style": "dashed"}, {"source": "m_send_event", "target": "t_tune_control", "color": "#74d841", "style": "dashed"}, {"source": "m_send_event", "target": "t_led_control", "color": "#41d5d8", "style": "dashed"}, {"source": "m_send_event", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_vehicle_constraints", "color": "#41d84d", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_landing_gear", "color": "#8141d8", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_trajectory_setpoint", "color": "#d84167", "style": "dashed"}, {"source": "m_fw_att_control", "target": "t_vehicle_rates_setpoint", "color": "#4147d8", "style": "dashed"}, {"source": "m_fw_att_control", "target": "t_landing_gear_wheel", "color": "#d841a1", "style": "dashed"}, {"source": "m_fw_autotune_attitude_control", "target": "t_autotune_attitude_control_status", "color": "#cf41d8", "style": "dashed"}, {"source": "m_fw_lat_lon_control", "target": "t_tecs_status", "color": "#b5d841", "style": "dashed"}, {"source": "m_fw_lat_lon_control", "target": "t_flight_phase_estimation", "color": "#94d841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_spoilers_setpoint", "color": "#a8d841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_longitudinal_setpoint", "color": "#41d89b", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_figure_eight_status", "color": "#9b41d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_lateral_control_configuration", "color": "#6141d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_runway_control", "color": "#4154d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_lateral_setpoint", "color": "#d85441", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_launch_detection_status", "color": "#c8d841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_longitudinal_control_configuration", "color": "#d8ae41", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_position_controller_landing_status", "color": "#d8b541", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_flaps_setpoint", "color": "#4d41d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_orbit_status", "color": "#7441d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_landing_gear", "color": "#8141d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_vehicle_local_position_setpoint", "color": "#d87441", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#4147d8", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_spoilers_setpoint", "color": "#a8d841", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_flaps_setpoint", "color": "#4d41d8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_mount_orientation", "color": "#a1d841", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_manager_information", "color": "#41d8c2", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_manager_status", "color": "#d8419b", "style": "dashed"}, {"source": "m_gimbal", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_device_set_attitude", "color": "#d841c8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_controls", "color": "#4141d8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_v1_command", "color": "#d8414d", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_device_attitude_status", "color": "#4174d8", "style": "dashed"}, {"source": "m_land_detector", "target": "t_vehicle_land_detected", "color": "#bb41d8", "style": "dashed"}, {"source": "m_landing_target_estimator", "target": "t_landing_target_pose", "color": "#41d8bb", "style": "dashed"}, {"source": "m_load_mon", "target": "t_cpuload", "color": "#41d888", "style": "dashed"}, {"source": "m_logger", "target": "t_ulog_stream", "color": "#d541d8", "style": "dashed"}, {"source": "m_logger", "target": "t_logger_status", "color": "#8e41d8", "style": "dashed"}, {"source": "m_logger", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_setpoint", "color": "#8ed841", "style": "dashed"}, {"source": "m_manual_control", "target": "t_action_request", "color": "#d8416e", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_status", "color": "#4dd841", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_switches", "color": "#d84154", "style": "dashed"}, {"source": "m_manual_control", "target": "t_landing_gear", "color": "#8141d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_tune_control", "color": "#74d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_manager_set_attitude", "color": "#6ed841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_offboard_control_mode", "color": "#41d8c8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_information", "color": "#d84141", "style": "dashed"}, {"source": "m_mavlink", "target": "t_input_rc", "color": "#41d8cf", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_system", "color": "#41d8d5", "style": "dashed"}, {"source": "m_mavlink", "target": "t_rc_parameter_map", "color": "#5ad841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_irlock_report", "color": "#41bbd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_differential_pressure", "color": "#b541d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_array", "color": "#d86741", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_optical_flow", "color": "#41a1d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude", "color": "#d87b41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_mavlink", "target": "t_ulog_stream_ack", "color": "#d88841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_camera_status", "color": "#d88e41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_self_id", "color": "#41d85a", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_key_value", "color": "#c241d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gyro", "color": "#41d861", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_manager_set_manual_control", "color": "#41d867", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_mavlink", "target": "t_airspeed", "color": "#d841b5", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_telemetry_status", "color": "#d8a141", "style": "dashed"}, {"source": "m_mavlink", "target": "t_uavcan_parameter_request", "color": "#4181d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_attitude_status", "color": "#4174d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_local_position", "color": "#416ed8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_global_position", "color": "#41d881", "style": "dashed"}, {"source": "m_mavlink", "target": "t_battery_status", "color": "#d841a8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gps_inject_data", "color": "#d8c241", "style": "dashed"}, {"source": "m_mavlink", "target": "t_dataman_request", "color": "#41d88e", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_value", "color": "#415ad8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mc_virtual_attitude_setpoint", "color": "#414dd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude_setpoint", "color": "#d5d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_fw_virtual_attitude_setpoint", "color": "#c2d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_rates_setpoint", "color": "#4147d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_operator_id", "color": "#bbd841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_event", "color": "#41d894", "style": "dashed"}, {"source": "m_mavlink", "target": "t_transponder_report", "color": "#4741d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_vect", "color": "#41d8a1", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_mocap_odometry", "color": "#5441d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_visual_odometry", "color": "#5a41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_trajectory_setpoint", "color": "#d84167", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mission", "color": "#81d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_accel", "color": "#6e41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gps", "color": "#417bd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_landing_target_pose", "color": "#41d8bb", "style": "dashed"}, {"source": "m_mc_att_control", "target": "t_vehicle_rates_setpoint", "color": "#4147d8", "style": "dashed"}, {"source": "m_mc_autotune_attitude_control", "target": "t_autotune_attitude_control_status", "color": "#cf41d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_constraints", "color": "#41d84d", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_trajectory_setpoint", "color": "#d84167", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_takeoff_status", "color": "#d8415a", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#d5d841", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_local_position_setpoint", "color": "#d87441", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#4147d8", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_actuator_controls_status_0", "color": "#41a8d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission_result", "color": "#a841d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_status", "color": "#4dd841", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_land_detected", "color": "#bb41d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_home_position", "color": "#41d847", "style": "dashed"}, {"source": "m_navigator", "target": "t_distance_sensor_mode_change_request", "color": "#c841d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_roi", "color": "#419bd8", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_status", "color": "#d841bb", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_status", "color": "#418ed8", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_result", "color": "#41d87b", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_global_position", "color": "#41d881", "style": "dashed"}, {"source": "m_navigator", "target": "t_dataman_request", "color": "#41d88e", "style": "dashed"}, {"source": "m_navigator", "target": "t_transponder_report", "color": "#4741d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_time_estimate", "color": "#41d8b5", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission", "color": "#81d841", "style": "dashed"}, {"source": "m_navigator", "target": "t_navigator_status", "color": "#6741d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_position_setpoint_triplet", "color": "#7bd841", "style": "dashed"}, {"source": "m_rc_update", "target": "t_manual_control_switches", "color": "#d84154", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_thrust_setpoint", "color": "#d8bb41", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_position_controller_status", "color": "#54d841", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#d5d841", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_torque_setpoint", "color": "#47d841", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu_status", "color": "#41d841", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu", "color": "#41d8a8", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_combined", "color": "#67d841", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensors_status_imu", "color": "#41c2d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_airspeed", "color": "#d841b5", "style": "dashed"}, {"source": "m_sensors", "target": "t_differential_pressure", "color": "#b541d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_selection", "color": "#d8417b", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_armed", "color": "#d8c841", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_outputs", "color": "#61d841", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_outputs_sim", "color": "#d84741", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_motors", "color": "#41c8d8", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_test", "color": "#41d8ae", "style": "dashed"}, {"source": "m_sensor_baro_sim", "target": "t_sensor_baro", "color": "#d89b41", "style": "dashed"}, {"source": "m_sensor_gps_sim", "target": "t_sensor_gps", "color": "#417bd8", "style": "dashed"}, {"source": "m_sensor_mag_sim", "target": "t_sensor_mag", "color": "#d89441", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_local_position_groundtruth", "color": "#9bd841", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_distance_sensor", "color": "#d88141", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_sensor_gyro", "color": "#41d861", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_attitude_groundtruth", "color": "#d841cf", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_angular_velocity_groundtruth", "color": "#4161d8", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_airspeed", "color": "#d841b5", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_sensor_accel", "color": "#6e41d8", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_global_position_groundtruth", "color": "#d84147", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_sensor_correction", "color": "#d84181", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_led_control", "color": "#41d5d8", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_uuv_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#d8bb41", "style": "dashed"}, {"source": "m_uuv_att_control", "target": "t_vehicle_torque_setpoint", "color": "#47d841", "style": "dashed"}, {"source": "m_uuv_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#d5d841", "style": "dashed"}, {"source": "m_uxrce_dds_client", "target": "t_vehicle_command", "color": "#d84d41", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_spoilers_setpoint", "color": "#a8d841", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#d8bb41", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_torque_setpoint", "color": "#47d841", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_command_ack", "color": "#d84174", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_attitude_setpoint", "color": "#d5d841", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vtol_vehicle_status", "color": "#4194d8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_flaps_setpoint", "color": "#4d41d8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_tiltrotor_extra_controls", "color": "#4188d8", "style": "dashed"}, {"source": "m_actuator_test", "target": "t_actuator_test", "color": "#41d8ae", "style": "dashed"}, {"source": "m_led_control", "target": "t_led_control", "color": "#41d5d8", "style": "dashed"}, {"source": "m_tune_control", "target": "t_tune_control", "color": "#74d841", "style": "dashed"}, {"source": "t_adc_report", "target": "m_board_adc", "color": "#d84188", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_camera_capture", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_camera_trigger", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_camera_trigger", "color": "#d84d41", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_cdcacm_autostart", "color": "#d8c841", "style": "normal"}, {"source": "t_distance_sensor_mode_change_request", "target": "m_lightware_laser_i2c", "color": "#c841d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_lightware_laser_i2c", "color": "#4dd841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_dshot", "color": "#d841a1", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_dshot", "color": "#8ed841", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_dshot", "color": "#d8c841", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_dshot", "color": "#41c8d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_dshot", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_dshot", "color": "#d84d41", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_dshot", "color": "#41aed8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_dshot", "color": "#4141d8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_dshot", "color": "#8141d8", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_gps", "color": "#d8c241", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_heater", "color": "#6e41d8", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled", "color": "#41d5d8", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_is31fl3195", "color": "#41d5d8", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_lp5562", "color": "#41d5d8", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_ncp5623c", "color": "#41d5d8", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_pwm", "color": "#41d5d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_atxxxx", "color": "#416ed8", "style": "normal"}, {"source": "t_battery_status", "target": "m_atxxxx", "color": "#d841a8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_atxxxx", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_msp_osd", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_msp_osd", "color": "#41d881", "style": "normal"}, {"source": "t_battery_status", "target": "m_msp_osd", "color": "#d841a8", "style": "normal"}, {"source": "t_home_position", "target": "m_msp_osd", "color": "#41d847", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_msp_osd", "color": "#d87b41", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_msp_osd", "color": "#d8cf41", "style": "normal"}, {"source": "t_input_rc", "target": "m_msp_osd", "color": "#41d8cf", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_msp_osd", "color": "#4dd841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_pca9685_pwm_out", "color": "#d841a1", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pca9685_pwm_out", "color": "#8ed841", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pca9685_pwm_out", "color": "#d8c841", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pca9685_pwm_out", "color": "#41c8d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pca9685_pwm_out", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pca9685_pwm_out", "color": "#d84d41", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pca9685_pwm_out", "color": "#41aed8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_pca9685_pwm_out", "color": "#4141d8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pca9685_pwm_out", "color": "#8141d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ina226", "color": "#4dd841", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_ina226", "color": "#94d841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_pwm_out", "color": "#d841a1", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pwm_out", "color": "#8ed841", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pwm_out", "color": "#d8c841", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pwm_out", "color": "#41c8d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pwm_out", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pwm_out", "color": "#d84d41", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pwm_out", "color": "#41aed8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_pwm_out", "color": "#4141d8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pwm_out", "color": "#8141d8", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_px4io", "color": "#d841a1", "style": "normal"}, {"source": "t_px4io_status", "target": "m_px4io", "color": "#d84194", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_px4io", "color": "#8ed841", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_px4io", "color": "#d8c841", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_px4io", "color": "#41c8d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_px4io", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_px4io", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_px4io", "color": "#4dd841", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_px4io", "color": "#41aed8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_px4io", "color": "#4141d8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_px4io", "color": "#8141d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_crsf_rc", "color": "#d841a8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_crsf_rc", "color": "#d87b41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_crsf_rc", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_dsm_rc", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_dsm_rc", "color": "#4dd841", "style": "normal"}, {"source": "t_battery_status", "target": "m_ghst_rc", "color": "#d841a8", "style": "normal"}, {"source": "t_battery_status", "target": "m_rc_input", "color": "#d841a8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rc_input", "color": "#d87b41", "style": "normal"}, {"source": "t_adc_report", "target": "m_rc_input", "color": "#d84188", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_rc_input", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_rc_input", "color": "#4dd841", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_safety_button", "color": "#d8c841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_bst", "color": "#d87b41", "style": "normal"}, {"source": "t_battery_status", "target": "m_bst", "color": "#d841a8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_frsky_telemetry", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_frsky_telemetry", "color": "#41d881", "style": "normal"}, {"source": "t_battery_status", "target": "m_frsky_telemetry", "color": "#d841a8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_frsky_telemetry", "color": "#4dd841", "style": "normal"}, {"source": "t_battery_status", "target": "m_hott_telemetry", "color": "#d841a8", "style": "normal"}, {"source": "t_home_position", "target": "m_hott_telemetry", "color": "#41d847", "style": "normal"}, {"source": "t_esc_status", "target": "m_hott_telemetry", "color": "#cfd841", "style": "normal"}, {"source": "t_airspeed", "target": "m_hott_telemetry", "color": "#d841b5", "style": "normal"}, {"source": "t_tune_control", "target": "m_tone_alarm", "color": "#74d841", "style": "normal"}, {"source": "t_tune_control", "target": "m_uavcan", "color": "#74d841", "style": "normal"}, {"source": "t_open_drone_id_system", "target": "m_uavcan", "color": "#41d8d5", "style": "normal"}, {"source": "t_led_control", "target": "m_uavcan", "color": "#41d5d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_uavcan", "color": "#41c8d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_uavcan", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_uavcan", "color": "#4dd841", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_uavcan", "color": "#41aed8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_uavcan", "color": "#bb41d8", "style": "normal"}, {"source": "t_home_position", "target": "m_uavcan", "color": "#41d847", "style": "normal"}, {"source": "t_open_drone_id_self_id", "target": "m_uavcan", "color": "#41d85a", "style": "normal"}, {"source": "t_uavcan_parameter_request", "target": "m_uavcan", "color": "#4181d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_uavcan", "color": "#416ed8", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_uavcan", "color": "#d841a1", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_uavcan", "color": "#d8c241", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_uavcan", "color": "#d8c841", "style": "normal"}, {"source": "t_open_drone_id_operator_id", "target": "m_uavcan", "color": "#bbd841", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_uavcan", "color": "#4141d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_uavcan", "color": "#8ed841", "style": "normal"}, {"source": "t_actuator_test", "target": "m_uavcan", "color": "#41d8ae", "style": "normal"}, {"source": "t_landing_gear", "target": "m_uavcan", "color": "#8141d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_airspeed_selector", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_airspeed_selector", "color": "#d8bb41", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_airspeed_selector", "color": "#d87b41", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_airspeed_selector", "color": "#94d841", "style": "normal"}, {"source": "t_estimator_status", "target": "m_airspeed_selector", "color": "#d8d541", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_airspeed_selector", "color": "#88d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_airspeed_selector", "color": "#4dd841", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_airspeed_selector", "color": "#c8d841", "style": "normal"}, {"source": "t_airspeed", "target": "m_airspeed_selector", "color": "#d841b5", "style": "normal"}, {"source": "t_tecs_status", "target": "m_airspeed_selector", "color": "#b5d841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_airspeed_selector", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_attitude_estimator_q", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_attitude_estimator_q", "color": "#d87b41", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_attitude_estimator_q", "color": "#67d841", "style": "normal"}, {"source": "t_vehicle_mocap_odometry", "target": "m_attitude_estimator_q", "color": "#5441d8", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_attitude_estimator_q", "color": "#5a41d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_battery_status", "color": "#4dd841", "style": "normal"}, {"source": "t_adc_report", "target": "m_battery_status", "color": "#d84188", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_battery_status", "color": "#94d841", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_camera_feedback", "color": "#41d881", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_camera_feedback", "color": "#d87b41", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_camera_feedback", "color": "#4174d8", "style": "normal"}, {"source": "t_camera_trigger", "target": "m_camera_feedback", "color": "#41b5d8", "style": "normal"}, {"source": "t_logger_status", "target": "m_commander", "color": "#8e41d8", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_commander", "color": "#41d8c8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_commander", "color": "#d84154", "style": "normal"}, {"source": "t_estimator_status_flags", "target": "m_commander", "color": "#41cfd8", "style": "normal"}, {"source": "t_mission_result", "target": "m_commander", "color": "#a841d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_commander", "color": "#41c8d8", "style": "normal"}, {"source": "t_navigator_status", "target": "m_commander", "color": "#6741d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_commander", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_commander", "color": "#4dd841", "style": "normal"}, {"source": "t_pwm_input", "target": "m_commander", "color": "#ae41d8", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_commander", "color": "#41c2d8", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_commander", "color": "#b541d8", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_commander", "color": "#d86141", "style": "normal"}, {"source": "t_system_power", "target": "m_commander", "color": "#d86e41", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_commander", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_commander", "color": "#d87b41", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_commander", "color": "#41d841", "style": "normal"}, {"source": "t_home_position", "target": "m_commander", "color": "#41d847", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_commander", "color": "#d88141", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_commander", "color": "#41d861", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_commander", "color": "#d89441", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_commander", "color": "#d89b41", "style": "normal"}, {"source": "t_vtol_vehicle_status", "target": "m_commander", "color": "#4194d8", "style": "normal"}, {"source": "t_telemetry_status", "target": "m_commander", "color": "#d8a141", "style": "normal"}, {"source": "t_geofence_result", "target": "m_commander", "color": "#41d87b", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_commander", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_commander", "color": "#41d881", "style": "normal"}, {"source": "t_battery_status", "target": "m_commander", "color": "#d841a8", "style": "normal"}, {"source": "t_cpuload", "target": "m_commander", "color": "#41d888", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_commander", "color": "#d8cf41", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_commander", "color": "#d8c841", "style": "normal"}, {"source": "t_power_button_state", "target": "m_commander", "color": "#d8418e", "style": "normal"}, {"source": "t_estimator_status", "target": "m_commander", "color": "#d8d541", "style": "normal"}, {"source": "t_wind", "target": "m_commander", "color": "#4167d8", "style": "normal"}, {"source": "t_esc_status", "target": "m_commander", "color": "#cfd841", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_commander", "color": "#d84181", "style": "normal"}, {"source": "t_event", "target": "m_commander", "color": "#41d894", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_commander", "color": "#d8417b", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_commander", "color": "#d84174", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_commander", "color": "#8ed841", "style": "normal"}, {"source": "t_action_request", "target": "m_commander", "color": "#d8416e", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_commander", "color": "#88d841", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_commander", "color": "#41d8b5", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_commander", "color": "#6e41d8", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_commander", "color": "#417bd8", "style": "normal"}, {"source": "t_safety_button", "target": "m_commander", "color": "#7b41d8", "style": "normal"}, {"source": "t_spoilers_setpoint", "target": "m_control_allocator", "color": "#a8d841", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_control_allocator", "color": "#d8bb41", "style": "normal"}, {"source": "t_vehicle_torque_setpoint", "target": "m_control_allocator", "color": "#47d841", "style": "normal"}, {"source": "t_failure_detector_status", "target": "m_control_allocator", "color": "#41d854", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_control_allocator", "color": "#a141d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_control_allocator", "color": "#4dd841", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_control_allocator", "color": "#d84154", "style": "normal"}, {"source": "t_flaps_setpoint", "target": "m_control_allocator", "color": "#4d41d8", "style": "normal"}, {"source": "t_tiltrotor_extra_controls", "target": "m_control_allocator", "color": "#4188d8", "style": "normal"}, {"source": "t_dataman_request", "target": "m_dataman", "color": "#41d88e", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_ekf2", "color": "#41d8a8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_ekf2", "color": "#d8417b", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_ekf2", "color": "#d88141", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_ekf2", "color": "#d8cf41", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_ekf2", "color": "#67d841", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_ekf2", "color": "#5a41d8", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_ekf2", "color": "#41c2d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ekf2", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_ekf2", "color": "#bb41d8", "style": "normal"}, {"source": "t_airspeed", "target": "m_ekf2", "color": "#d841b5", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_ekf2", "color": "#c8d841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_ekf2", "color": "#d84d41", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_ekf2", "color": "#41d8bb", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_esc_battery", "color": "#4dd841", "style": "normal"}, {"source": "t_esc_status", "target": "m_esc_battery", "color": "#cfd841", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_esc_battery", "color": "#94d841", "style": "normal"}, {"source": "t_battery_status", "target": "m_send_event", "color": "#d841a8", "style": "normal"}, {"source": "t_cpuload", "target": "m_send_event", "color": "#41d888", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_send_event", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_send_event", "color": "#4dd841", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_send_event", "color": "#d841c2", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_flight_mode_manager", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_flight_mode_manager", "color": "#a141d8", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_flight_mode_manager", "color": "#d8415a", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_flight_mode_manager", "color": "#d5d841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_flight_mode_manager", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_flight_mode_manager", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_flight_mode_manager", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_att_control", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_att_control", "color": "#d87b41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_att_control", "color": "#8ed841", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_att_control", "color": "#d8cf41", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_fw_att_control", "color": "#cf41d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_att_control", "color": "#a141d8", "style": "normal"}, {"source": "t_fixed_wing_runway_control", "target": "m_fw_att_control", "color": "#4154d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_fw_att_control", "color": "#d5d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_att_control", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_att_control", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_autotune_attitude_control", "color": "#4dd841", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_autotune_attitude_control", "color": "#8ed841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_lat_lon_control", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_lat_lon_control", "color": "#d87b41", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_lat_lon_control", "color": "#d8cf41", "style": "normal"}, {"source": "t_fixed_wing_longitudinal_setpoint", "target": "m_fw_lat_lon_control", "color": "#41d89b", "style": "normal"}, {"source": "t_wind", "target": "m_fw_lat_lon_control", "color": "#4167d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_lat_lon_control", "color": "#a141d8", "style": "normal"}, {"source": "t_lateral_control_configuration", "target": "m_fw_lat_lon_control", "color": "#6141d8", "style": "normal"}, {"source": "t_fixed_wing_lateral_setpoint", "target": "m_fw_lat_lon_control", "color": "#d85441", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_lat_lon_control", "color": "#4dd841", "style": "normal"}, {"source": "t_longitudinal_control_configuration", "target": "m_fw_lat_lon_control", "color": "#d8ae41", "style": "normal"}, {"source": "t_flaps_setpoint", "target": "m_fw_lat_lon_control", "color": "#4d41d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_lat_lon_control", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_mode_manager", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_fw_mode_manager", "color": "#41d881", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_mode_manager", "color": "#d87b41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_mode_manager", "color": "#8ed841", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_mode_manager", "color": "#d8cf41", "style": "normal"}, {"source": "t_wind", "target": "m_fw_mode_manager", "color": "#4167d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_mode_manager", "color": "#a141d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_fw_mode_manager", "color": "#d84167", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_fw_mode_manager", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_mode_manager", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_mode_manager", "color": "#4dd841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_fw_mode_manager", "color": "#7bd841", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_fw_rate_control", "color": "#8841d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_fw_rate_control", "color": "#d841a8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_rate_control", "color": "#8ed841", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_rate_control", "color": "#d8cf41", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_rate_control", "color": "#a141d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_rate_control", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_fw_rate_control", "color": "#4147d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_rate_control", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_gimbal", "color": "#41d881", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_gimbal", "color": "#d87b41", "style": "normal"}, {"source": "t_gimbal_manager_set_attitude", "target": "m_gimbal", "color": "#6ed841", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_gimbal", "color": "#8ed841", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_gimbal", "color": "#d84141", "style": "normal"}, {"source": "t_gimbal_manager_set_manual_control", "target": "m_gimbal", "color": "#41d867", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_gimbal", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_roi", "target": "m_gimbal", "color": "#419bd8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_gimbal", "color": "#7bd841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_gimbal", "color": "#bb41d8", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_gimbal", "color": "#4174d8", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_gyro_calibration", "color": "#d84181", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_gyro_calibration", "color": "#41d861", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_gyro_calibration", "color": "#4dd841", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_gyro_calibration", "color": "#6e41d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_land_detector", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_land_detector", "color": "#d8bb41", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_land_detector", "color": "#41d841", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_land_detector", "color": "#41d881", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_land_detector", "color": "#d8cf41", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_land_detector", "color": "#d8c841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_land_detector", "color": "#a141d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_land_detector", "color": "#d84167", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_land_detector", "color": "#d8415a", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_land_detector", "color": "#4dd841", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_land_detector", "color": "#c8d841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_land_detector", "color": "#7bd841", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_land_detector", "color": "#d8417b", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_landing_target_estimator", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_landing_target_estimator", "color": "#d87b41", "style": "normal"}, {"source": "t_irlock_report", "target": "m_landing_target_estimator", "color": "#41bbd8", "style": "normal"}, {"source": "t_battery_status", "target": "m_logger", "color": "#d841a8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_logger", "color": "#8ed841", "style": "normal"}, {"source": "t_ulog_stream_ack", "target": "m_logger", "color": "#d88841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_logger", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_logger", "color": "#4dd841", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mag_bias_estimator", "color": "#d89441", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mag_bias_estimator", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_manual_control", "color": "#4dd841", "style": "normal"}, {"source": "t_action_request", "target": "m_manual_control", "color": "#d8416e", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_manual_control", "color": "#d84154", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_manual_control", "color": "#8ed841", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_mavlink", "color": "#d84141", "style": "normal"}, {"source": "t_actuator_outputs_sim", "target": "m_mavlink", "color": "#d84741", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_mavlink", "color": "#d84d41", "style": "normal"}, {"source": "t_register_ext_component_reply", "target": "m_mavlink", "color": "#d85a41", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_mavlink", "color": "#d86141", "style": "normal"}, {"source": "t_debug_array", "target": "m_mavlink", "color": "#d86741", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_mavlink", "color": "#d87441", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mavlink", "color": "#d87b41", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_mavlink", "color": "#d88141", "style": "normal"}, {"source": "t_camera_status", "target": "m_mavlink", "color": "#d88e41", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mavlink", "color": "#d89441", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_mavlink", "color": "#d89b41", "style": "normal"}, {"source": "t_uavcan_parameter_value", "target": "m_mavlink", "color": "#d8a841", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_mavlink", "color": "#d8bb41", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_mavlink", "color": "#d8c241", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_mavlink", "color": "#d8c841", "style": "normal"}, {"source": "t_estimator_status", "target": "m_mavlink", "color": "#d8d541", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_mavlink", "color": "#d8cf41", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mavlink", "color": "#d5d841", "style": "normal"}, {"source": "t_esc_status", "target": "m_mavlink", "color": "#cfd841", "style": "normal"}, {"source": "t_tecs_status", "target": "m_mavlink", "color": "#b5d841", "style": "normal"}, {"source": "t_sensor_hygrometer", "target": "m_mavlink", "color": "#aed841", "style": "normal"}, {"source": "t_mount_orientation", "target": "m_mavlink", "color": "#a1d841", "style": "normal"}, {"source": "t_vehicle_local_position_groundtruth", "target": "m_mavlink", "color": "#9bd841", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mavlink", "color": "#8ed841", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_mavlink", "color": "#88d841", "style": "normal"}, {"source": "t_mission", "target": "m_mavlink", "color": "#81d841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_mavlink", "color": "#7bd841", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_mavlink", "color": "#61d841", "style": "normal"}, {"source": "t_position_controller_status", "target": "m_mavlink", "color": "#54d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mavlink", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_mavlink", "color": "#41d841", "style": "normal"}, {"source": "t_home_position", "target": "m_mavlink", "color": "#41d847", "style": "normal"}, {"source": "t_vehicle_odometry", "target": "m_mavlink", "color": "#41d874", "style": "normal"}, {"source": "t_dataman_response", "target": "m_mavlink", "color": "#41d86e", "style": "normal"}, {"source": "t_geofence_result", "target": "m_mavlink", "color": "#41d87b", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_mavlink", "color": "#41d881", "style": "normal"}, {"source": "t_cpuload", "target": "m_mavlink", "color": "#41d888", "style": "normal"}, {"source": "t_event", "target": "m_mavlink", "color": "#41d894", "style": "normal"}, {"source": "t_debug_vect", "target": "m_mavlink", "color": "#41d8a1", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_mavlink", "color": "#41d8a8", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_mavlink", "color": "#41d8b5", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_mavlink", "color": "#41d8bb", "style": "normal"}, {"source": "t_gimbal_manager_information", "target": "m_mavlink", "color": "#41d8c2", "style": "normal"}, {"source": "t_input_rc", "target": "m_mavlink", "color": "#41d8cf", "style": "normal"}, {"source": "t_camera_trigger", "target": "m_mavlink", "color": "#41b5d8", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_mavlink", "color": "#4174d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mavlink", "color": "#416ed8", "style": "normal"}, {"source": "t_wind", "target": "m_mavlink", "color": "#4167d8", "style": "normal"}, {"source": "t_vehicle_angular_velocity_groundtruth", "target": "m_mavlink", "color": "#4161d8", "style": "normal"}, {"source": "t_debug_value", "target": "m_mavlink", "color": "#415ad8", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mavlink", "color": "#4147d8", "style": "normal"}, {"source": "t_transponder_report", "target": "m_mavlink", "color": "#4741d8", "style": "normal"}, {"source": "t_orbit_status", "target": "m_mavlink", "color": "#7441d8", "style": "normal"}, {"source": "t_health_report", "target": "m_mavlink", "color": "#9441d8", "style": "normal"}, {"source": "t_figure_eight_status", "target": "m_mavlink", "color": "#9b41d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mavlink", "color": "#a141d8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_mavlink", "color": "#d84154", "style": "normal"}, {"source": "t_mission_result", "target": "m_mavlink", "color": "#a841d8", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_mavlink", "color": "#b541d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mavlink", "color": "#bb41d8", "style": "normal"}, {"source": "t_debug_key_value", "target": "m_mavlink", "color": "#c241d8", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_mavlink", "color": "#cf41d8", "style": "normal"}, {"source": "t_vehicle_attitude_groundtruth", "target": "m_mavlink", "color": "#d841cf", "style": "normal"}, {"source": "t_ulog_stream", "target": "m_mavlink", "color": "#d541d8", "style": "normal"}, {"source": "t_satellite_info", "target": "m_mavlink", "color": "#d841d5", "style": "normal"}, {"source": "t_gimbal_device_set_attitude", "target": "m_mavlink", "color": "#d841c8", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_mavlink", "color": "#d841c2", "style": "normal"}, {"source": "t_airspeed", "target": "m_mavlink", "color": "#d841b5", "style": "normal"}, {"source": "t_camera_capture", "target": "m_mavlink", "color": "#d841ae", "style": "normal"}, {"source": "t_battery_status", "target": "m_mavlink", "color": "#d841a8", "style": "normal"}, {"source": "t_gimbal_manager_status", "target": "m_mavlink", "color": "#d8419b", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_mavlink", "color": "#d84181", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_mavlink", "color": "#d8417b", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_mavlink", "color": "#d84174", "style": "normal"}, {"source": "t_open_drone_id_arm_status", "target": "m_mavlink", "color": "#d84161", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_mavlink", "color": "#417bd8", "style": "normal"}, {"source": "t_gimbal_v1_command", "target": "m_mavlink", "color": "#d8414d", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_mavlink", "color": "#d84147", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_att_control", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mc_att_control", "color": "#d87b41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_att_control", "color": "#8ed841", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_mc_att_control", "color": "#cf41d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_att_control", "color": "#a141d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mc_att_control", "color": "#d5d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_att_control", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_att_control", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_torque_setpoint", "target": "m_mc_autotune_attitude_control", "color": "#47d841", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_autotune_attitude_control", "color": "#8ed841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_autotune_attitude_control", "color": "#4dd841", "style": "normal"}, {"source": "t_actuator_controls_status_0", "target": "m_mc_autotune_attitude_control", "color": "#41a8d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_pos_control", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_constraints", "target": "m_mc_pos_control", "color": "#41d84d", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_pos_control", "color": "#a141d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_mc_pos_control", "color": "#d84167", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_pos_control", "color": "#bb41d8", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_mc_rate_control", "color": "#8841d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_mc_rate_control", "color": "#d841a8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_rate_control", "color": "#8ed841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_rate_control", "color": "#a141d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_rate_control", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mc_rate_control", "color": "#4147d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_rate_control", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_navigator", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_navigator", "color": "#41d881", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_navigator", "color": "#41d8bb", "style": "normal"}, {"source": "t_home_position", "target": "m_navigator", "color": "#41d847", "style": "normal"}, {"source": "t_wind", "target": "m_navigator", "color": "#4167d8", "style": "normal"}, {"source": "t_position_controller_status", "target": "m_navigator", "color": "#54d841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_navigator", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_navigator", "color": "#4dd841", "style": "normal"}, {"source": "t_mission", "target": "m_navigator", "color": "#81d841", "style": "normal"}, {"source": "t_geofence_status", "target": "m_navigator", "color": "#d841bb", "style": "normal"}, {"source": "t_rtl_status", "target": "m_navigator", "color": "#418ed8", "style": "normal"}, {"source": "t_position_controller_landing_status", "target": "m_navigator", "color": "#d8b541", "style": "normal"}, {"source": "t_dataman_response", "target": "m_navigator", "color": "#41d86e", "style": "normal"}, {"source": "t_transponder_report", "target": "m_navigator", "color": "#4741d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_navigator", "color": "#bb41d8", "style": "normal"}, {"source": "t_rc_parameter_map", "target": "m_rc_update", "color": "#5ad841", "style": "normal"}, {"source": "t_input_rc", "target": "m_rc_update", "color": "#41d8cf", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_rc_update", "color": "#d84154", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_pos_control", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_rover_pos_control", "color": "#41d881", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_pos_control", "color": "#d87b41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_pos_control", "color": "#8ed841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_pos_control", "color": "#a141d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_pos_control", "color": "#d84167", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_rover_pos_control", "color": "#d5d841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_pos_control", "color": "#7bd841", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_sensors", "color": "#41d841", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_sensors", "color": "#41d8a8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_sensors", "color": "#d8417b", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_sensors", "color": "#d84181", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_sensors", "color": "#41d861", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_sensors", "color": "#a141d8", "style": "normal"}, {"source": "t_adc_report", "target": "m_sensors", "color": "#d84188", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_sensors", "color": "#d89441", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_sensors", "color": "#6e41d8", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_sensors", "color": "#b541d8", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_sensors", "color": "#d86141", "style": "normal"}, {"source": "t_sensor_optical_flow", "target": "m_sensors", "color": "#41a1d8", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_pwm_out_sim", "color": "#d841a1", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pwm_out_sim", "color": "#8ed841", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pwm_out_sim", "color": "#d8c841", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pwm_out_sim", "color": "#41c8d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pwm_out_sim", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pwm_out_sim", "color": "#d84d41", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pwm_out_sim", "color": "#41aed8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_pwm_out_sim", "color": "#4141d8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pwm_out_sim", "color": "#8141d8", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_baro_sim", "color": "#d84147", "style": "normal"}, {"source": "t_vehicle_local_position_groundtruth", "target": "m_sensor_gps_sim", "color": "#9bd841", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_gps_sim", "color": "#d84147", "style": "normal"}, {"source": "t_vehicle_attitude_groundtruth", "target": "m_sensor_mag_sim", "color": "#d841cf", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_mag_sim", "color": "#d84147", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_simulator_sih", "color": "#61d841", "style": "normal"}, {"source": "t_actuator_outputs_sim", "target": "m_simulator_sih", "color": "#d84741", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_temperature_compensation", "color": "#41d861", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_temperature_compensation", "color": "#d84d41", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_temperature_compensation", "color": "#d89441", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_temperature_compensation", "color": "#d89b41", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_temperature_compensation", "color": "#6e41d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_uuv_att_control", "color": "#d87b41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_uuv_att_control", "color": "#8ed841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_uuv_att_control", "color": "#a141d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_uuv_att_control", "color": "#d5d841", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_uuv_att_control", "color": "#4147d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_uuv_pos_control", "color": "#416ed8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_uuv_pos_control", "color": "#d87b41", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_uuv_pos_control", "color": "#a141d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_uuv_pos_control", "color": "#d84167", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_uxrce_dds_client", "color": "#d84174", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_vtol_att_control", "color": "#a141d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_vtol_att_control", "color": "#d84d41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_vtol_att_control", "color": "#4dd841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_vtol_att_control", "color": "#bb41d8", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_vtol_att_control", "color": "#d87441", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_vtol_att_control", "color": "#d87b41", "style": "normal"}, {"source": "t_home_position", "target": "m_vtol_att_control", "color": "#41d847", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_vtol_att_control", "color": "#416ed8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_vtol_att_control", "color": "#d8cf41", "style": "normal"}, {"source": "t_mc_virtual_attitude_setpoint", "target": "m_vtol_att_control", "color": "#414dd8", "style": "normal"}, {"source": "t_fw_virtual_attitude_setpoint", "target": "m_vtol_att_control", "color": "#c2d841", "style": "normal"}, {"source": "t_tecs_status", "target": "m_vtol_att_control", "color": "#b5d841", "style": "normal"}, {"source": "t_action_request", "target": "m_vtol_att_control", "color": "#d8416e", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_vtol_att_control", "color": "#7bd841", "style": "normal"}]}