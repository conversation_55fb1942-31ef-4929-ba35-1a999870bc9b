{"nodes": [{"id": "m_flight_mode_manager", "name": "flight_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_control_allocator", "name": "control_allocator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_cdcacm_autostart", "name": "cdcacm_autostart", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_rate_control", "name": "mc_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_battery_status", "name": "battery_status", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_manual_control", "name": "manual_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_att_control", "name": "mc_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_pos_control", "name": "mc_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_land_detector", "name": "land_detector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tune_control", "name": "tune_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_septentrio", "name": "septentrio", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tone_alarm", "name": "tone_alarm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_board_adc", "name": "board_adc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_commander", "name": "commander", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_navigator", "name": "navigator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rc_update", "name": "rc_update", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mpu6000", "name": "mpu6000", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lsm303d", "name": "lsm303d", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_hmc5883", "name": "hmc5883", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_out", "name": "pwm_out", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dataman", "name": "dataman", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mavlink", "name": "mavlink", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensors", "name": "sensors", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms5611", "name": "ms5611", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_l3gd20", "name": "l3gd20", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled", "name": "rgbled", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_logger", "name": "logger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_px4io", "name": "px4io", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ekf2", "name": "ekf2", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gps", "name": "gps", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "t_vehicle_local_position_setpoint", "name": "vehicle_local_position_setpoint", "type": "topic", "color": "#c941d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_attitude_status", "name": "gimbal_device_attitude_status", "type": "topic", "color": "#6ad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_register_ext_component_reply", "name": "register_ext_component_reply", "type": "topic", "color": "#6a41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_information", "name": "gimbal_device_information", "type": "topic", "color": "#d87541", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_selector_status", "name": "estimator_selector_status", "type": "topic", "color": "#8ad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude_setpoint", "name": "vehicle_attitude_setpoint", "type": "topic", "color": "#4155d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_setpoint_triplet", "name": "position_setpoint_triplet", "type": "topic", "color": "#d84160", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_control_allocator_status", "name": "control_allocator_status", "type": "topic", "color": "#d86a41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_failure_detector_status", "name": "failure_detector_status", "type": "topic", "color": "#41d84b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_visual_odometry", "name": "vehicle_visual_odometry", "type": "topic", "color": "#416ad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_setpoint", "name": "manual_control_setpoint", "type": "topic", "color": "#5541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_switches", "name": "manual_control_switches", "type": "topic", "color": "#a941d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_global_position", "name": "vehicle_global_position", "type": "topic", "color": "#d8416a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status_flags", "name": "estimator_status_flags", "type": "topic", "color": "#9fd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position", "name": "vehicle_local_position", "type": "topic", "color": "#41b4d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_rates_setpoint", "name": "vehicle_rates_setpoint", "type": "topic", "color": "#b441d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_differential_pressure", "name": "differential_pressure", "type": "topic", "color": "#d8a941", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_sensor_bias", "name": "estimator_sensor_bias", "type": "topic", "color": "#c9d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_offboard_control_mode", "name": "offboard_control_mode", "type": "topic", "color": "#41d88a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_land_detected", "name": "vehicle_land_detected", "type": "topic", "color": "#41d8a9", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_servos_trim", "name": "actuator_servos_trim", "type": "topic", "color": "#d88a41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_control_mode", "name": "vehicle_control_mode", "type": "topic", "color": "#75d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_constraints", "name": "vehicle_constraints", "type": "topic", "color": "#7fd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_target_pose", "name": "landing_target_pose", "type": "topic", "color": "#60d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command_ack", "name": "vehicle_command_ack", "type": "topic", "color": "#417fd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_optical_flow", "name": "sensor_optical_flow", "type": "topic", "color": "#7f41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_trajectory_setpoint", "name": "trajectory_setpoint", "type": "topic", "color": "#d84194", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensors_status_imu", "name": "sensors_status_imu", "type": "topic", "color": "#d85541", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu_status", "name": "vehicle_imu_status", "type": "topic", "color": "#41d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_power_button_state", "name": "power_button_state", "type": "topic", "color": "#41c9d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_transponder_report", "name": "transponder_report", "type": "topic", "color": "#4b41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_time_estimate", "name": "rtl_time_estimate", "type": "topic", "color": "#414bd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rc_parameter_map", "name": "rc_parameter_map", "type": "topic", "color": "#d87f41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_outputs", "name": "actuator_outputs", "type": "topic", "color": "#d8b441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorOutputs.msg"}, {"id": "t_vehicle_odometry", "name": "vehicle_odometry", "type": "topic", "color": "#d8c941", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status", "name": "estimator_status", "type": "topic", "color": "#b4d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_selection", "name": "sensor_selection", "type": "topic", "color": "#418ad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude", "name": "vehicle_attitude", "type": "topic", "color": "#d841d3", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_response", "name": "dataman_response", "type": "topic", "color": "#d8419f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_navigator_status", "name": "navigator_status", "type": "topic", "color": "#d8418a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_telemetry_status", "name": "telemetry_status", "type": "topic", "color": "#d8417f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_request", "name": "dataman_request", "type": "topic", "color": "#d84b41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_distance_sensor", "name": "distance_sensor", "type": "topic", "color": "#41d855", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_key_value", "name": "debug_key_value", "type": "topic", "color": "#41d860", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gps_inject_data", "name": "gps_inject_data", "type": "topic", "color": "#41d3d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_status", "name": "geofence_status", "type": "topic", "color": "#41bed8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_combined", "name": "sensor_combined", "type": "topic", "color": "#41a9d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_result", "name": "geofence_result", "type": "topic", "color": "#419fd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_motors", "name": "actuator_motors", "type": "topic", "color": "#4194d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream_ack", "name": "ulog_stream_ack", "type": "topic", "color": "#4141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command", "name": "vehicle_command", "type": "topic", "color": "#be41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_armed", "name": "actuator_armed", "type": "topic", "color": "#d86041", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_action_request", "name": "action_request", "type": "topic", "color": "#d89441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_battery_status", "name": "battery_status", "type": "topic", "color": "#4bd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_satellite_info", "name": "satellite_info", "type": "topic", "color": "#41d8b4", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mission_result", "name": "mission_result", "type": "topic", "color": "#41d8be", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_failsafe_flags", "name": "failsafe_flags", "type": "topic", "color": "#4160d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_status", "name": "vehicle_status", "type": "topic", "color": "#9441d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_takeoff_status", "name": "takeoff_status", "type": "topic", "color": "#d84175", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_health_report", "name": "health_report", "type": "topic", "color": "#41d894", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_logger_status", "name": "logger_status", "type": "topic", "color": "#41d8c9", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_home_position", "name": "home_position", "type": "topic", "color": "#7541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_status", "name": "camera_status", "type": "topic", "color": "#8a41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_safety_button", "name": "safety_button", "type": "topic", "color": "#9f41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_actuator_test", "name": "actuator_test", "type": "topic", "color": "#d841b4", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_system_power", "name": "system_power", "type": "topic", "color": "#d84141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_accel", "name": "sensor_accel", "type": "topic", "color": "#bed841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_px4io_status", "name": "px4io_status", "type": "topic", "color": "#41d89f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tune_control", "name": "tune_control", "type": "topic", "color": "#6041d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear", "name": "landing_gear", "type": "topic", "color": "#d341d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu", "name": "vehicle_imu", "type": "topic", "color": "#d8d341", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream", "name": "ulog_stream", "type": "topic", "color": "#d3d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_baro", "name": "sensor_baro", "type": "topic", "color": "#94d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_array", "name": "debug_array", "type": "topic", "color": "#55d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_led_control", "name": "led_control", "type": "topic", "color": "#4175d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_value", "name": "debug_value", "type": "topic", "color": "#d841a9", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gyro", "name": "sensor_gyro", "type": "topic", "color": "#d84155", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_mag", "name": "sensor_mag", "type": "topic", "color": "#d8be41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_vect", "name": "debug_vect", "type": "topic", "color": "#a9d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_adc_report", "name": "adc_report", "type": "topic", "color": "#41d86a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_status", "name": "rtl_status", "type": "topic", "color": "#41d875", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gps", "name": "sensor_gps", "type": "topic", "color": "#d8414b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/SensorGps.msg"}, {"id": "t_input_rc", "name": "input_rc", "type": "topic", "color": "#41d87f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed", "name": "airspeed", "type": "topic", "color": "#41d8d3", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/EstimatorAidSource1d.msg"}, {"id": "t_mission", "name": "mission", "type": "topic", "color": "#d841be", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_event", "name": "event", "type": "topic", "color": "#d841c9", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_wind", "name": "wind", "type": "topic", "color": "#d89f41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/Wind.msg"}], "links": [{"source": "m_board_adc", "target": "t_system_power", "color": "#d84141", "style": "dashed"}, {"source": "m_board_adc", "target": "t_adc_report", "color": "#41d86a", "style": "dashed"}, {"source": "m_ms5611", "target": "t_sensor_baro", "color": "#94d841", "style": "dashed"}, {"source": "m_septentrio", "target": "t_gps_inject_data", "color": "#41d3d8", "style": "dashed"}, {"source": "m_septentrio", "target": "t_sensor_gps", "color": "#d8414b", "style": "dashed"}, {"source": "m_septentrio", "target": "t_satellite_info", "color": "#41d8b4", "style": "dashed"}, {"source": "m_gps", "target": "t_gps_inject_data", "color": "#41d3d8", "style": "dashed"}, {"source": "m_gps", "target": "t_satellite_info", "color": "#41d8b4", "style": "dashed"}, {"source": "m_gps", "target": "t_sensor_gps", "color": "#d8414b", "style": "dashed"}, {"source": "m_mpu6000", "target": "t_sensor_accel", "color": "#bed841", "style": "dashed"}, {"source": "m_mpu6000", "target": "t_sensor_gyro", "color": "#d84155", "style": "dashed"}, {"source": "m_l3gd20", "target": "t_sensor_gyro", "color": "#d84155", "style": "dashed"}, {"source": "m_lsm303d", "target": "t_sensor_accel", "color": "#bed841", "style": "dashed"}, {"source": "m_lsm303d", "target": "t_sensor_mag", "color": "#d8be41", "style": "dashed"}, {"source": "m_hmc5883", "target": "t_sensor_mag", "color": "#d8be41", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_motors", "color": "#4194d8", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_armed", "color": "#d86041", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_outputs", "color": "#d8b441", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_test", "color": "#d841b4", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_motors", "color": "#4194d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_vehicle_command_ack", "color": "#417fd8", "style": "dashed"}, {"source": "m_px4io", "target": "t_led_control", "color": "#4175d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_safety_button", "color": "#9f41d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_armed", "color": "#d86041", "style": "dashed"}, {"source": "m_px4io", "target": "t_tune_control", "color": "#6041d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_test", "color": "#d841b4", "style": "dashed"}, {"source": "m_px4io", "target": "t_vehicle_command", "color": "#be41d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_outputs", "color": "#d8b441", "style": "dashed"}, {"source": "m_px4io", "target": "t_px4io_status", "color": "#41d89f", "style": "dashed"}, {"source": "m_px4io", "target": "t_input_rc", "color": "#41d87f", "style": "dashed"}, {"source": "m_tone_alarm", "target": "t_tune_control", "color": "#6041d8", "style": "dashed"}, {"source": "m_battery_status", "target": "t_battery_status", "color": "#4bd841", "style": "dashed"}, {"source": "m_commander", "target": "t_failsafe_flags", "color": "#4160d8", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_status", "color": "#9441d8", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_control_mode", "color": "#75d841", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command_ack", "color": "#417fd8", "style": "dashed"}, {"source": "m_commander", "target": "t_event", "color": "#d841c9", "style": "dashed"}, {"source": "m_commander", "target": "t_led_control", "color": "#4175d8", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_armed", "color": "#d86041", "style": "dashed"}, {"source": "m_commander", "target": "t_tune_control", "color": "#6041d8", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_test", "color": "#d841b4", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command", "color": "#be41d8", "style": "dashed"}, {"source": "m_commander", "target": "t_power_button_state", "color": "#41c9d8", "style": "dashed"}, {"source": "m_commander", "target": "t_home_position", "color": "#7541d8", "style": "dashed"}, {"source": "m_commander", "target": "t_register_ext_component_reply", "color": "#6a41d8", "style": "dashed"}, {"source": "m_commander", "target": "t_health_report", "color": "#41d894", "style": "dashed"}, {"source": "m_commander", "target": "t_failure_detector_status", "color": "#41d84b", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_control_allocator_status", "color": "#d86a41", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_motors", "color": "#4194d8", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_servos_trim", "color": "#d88a41", "style": "dashed"}, {"source": "m_dataman", "target": "t_dataman_response", "color": "#d8419f", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_attitude", "color": "#d841d3", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_local_position", "color": "#41b4d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_sensor_selection", "color": "#418ad8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_command_ack", "color": "#417fd8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_wind", "color": "#d89f41", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_odometry", "color": "#d8c941", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_sensor_bias", "color": "#c9d841", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status", "color": "#b4d841", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_global_position", "color": "#d8416a", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status_flags", "color": "#9fd841", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_selector_status", "color": "#8ad841", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_vehicle_constraints", "color": "#7fd841", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_trajectory_setpoint", "color": "#d84194", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_landing_gear", "color": "#d341d8", "style": "dashed"}, {"source": "m_land_detector", "target": "t_vehicle_land_detected", "color": "#41d8a9", "style": "dashed"}, {"source": "m_logger", "target": "t_logger_status", "color": "#41d8c9", "style": "dashed"}, {"source": "m_logger", "target": "t_vehicle_command_ack", "color": "#417fd8", "style": "dashed"}, {"source": "m_logger", "target": "t_ulog_stream", "color": "#d3d841", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_status", "color": "#9441d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_action_request", "color": "#d89441", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_setpoint", "color": "#5541d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_command", "color": "#be41d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_landing_gear", "color": "#d341d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_switches", "color": "#a941d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gps_inject_data", "color": "#41d3d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_attitude_status", "color": "#6ad841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_dataman_request", "color": "#d84b41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_rates_setpoint", "color": "#b441d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_landing_target_pose", "color": "#60d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_baro", "color": "#94d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_array", "color": "#55d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_information", "color": "#d87541", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command", "color": "#be41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_battery_status", "color": "#4bd841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude", "color": "#d841d3", "style": "dashed"}, {"source": "m_mavlink", "target": "t_rc_parameter_map", "color": "#d87f41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_distance_sensor", "color": "#41d855", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_local_position", "color": "#41b4d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_key_value", "color": "#41d860", "style": "dashed"}, {"source": "m_mavlink", "target": "t_event", "color": "#d841c9", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command_ack", "color": "#417fd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mission", "color": "#d841be", "style": "dashed"}, {"source": "m_mavlink", "target": "t_differential_pressure", "color": "#d8a941", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_visual_odometry", "color": "#416ad8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_input_rc", "color": "#41d87f", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_value", "color": "#d841a9", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_mag", "color": "#d8be41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_trajectory_setpoint", "color": "#d84194", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude_setpoint", "color": "#4155d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_offboard_control_mode", "color": "#41d88a", "style": "dashed"}, {"source": "m_mavlink", "target": "t_transponder_report", "color": "#4b41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_ulog_stream_ack", "color": "#4141d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_tune_control", "color": "#6041d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_accel", "color": "#bed841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_optical_flow", "color": "#7f41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_camera_status", "color": "#8a41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_telemetry_status", "color": "#d8417f", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_global_position", "color": "#d8416a", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_vect", "color": "#a9d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gyro", "color": "#d84155", "style": "dashed"}, {"source": "m_mavlink", "target": "t_airspeed", "color": "#41d8d3", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gps", "color": "#d8414b", "style": "dashed"}, {"source": "m_mc_att_control", "target": "t_vehicle_rates_setpoint", "color": "#b441d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_trajectory_setpoint", "color": "#d84194", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#4155d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_takeoff_status", "color": "#d84175", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_local_position_setpoint", "color": "#c941d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_constraints", "color": "#7fd841", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#b441d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_dataman_request", "color": "#d84b41", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command", "color": "#be41d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_status", "color": "#41bed8", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_result", "color": "#419fd8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command_ack", "color": "#417fd8", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission", "color": "#d841be", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_status", "color": "#41d875", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_time_estimate", "color": "#414bd8", "style": "dashed"}, {"source": "m_navigator", "target": "t_transponder_report", "color": "#4b41d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_navigator_status", "color": "#d8418a", "style": "dashed"}, {"source": "m_navigator", "target": "t_home_position", "color": "#7541d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_land_detected", "color": "#41d8a9", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_status", "color": "#9441d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission_result", "color": "#41d8be", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_global_position", "color": "#d8416a", "style": "dashed"}, {"source": "m_navigator", "target": "t_position_setpoint_triplet", "color": "#d84160", "style": "dashed"}, {"source": "m_rc_update", "target": "t_manual_control_switches", "color": "#a941d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensors_status_imu", "color": "#d85541", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_combined", "color": "#41a9d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_selection", "color": "#418ad8", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu", "color": "#d8d341", "style": "dashed"}, {"source": "m_sensors", "target": "t_differential_pressure", "color": "#d8a941", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu_status", "color": "#41d841", "style": "dashed"}, {"source": "m_sensors", "target": "t_airspeed", "color": "#41d8d3", "style": "dashed"}, {"source": "m_tune_control", "target": "t_tune_control", "color": "#6041d8", "style": "dashed"}, {"source": "t_adc_report", "target": "m_board_adc", "color": "#41d86a", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_cdcacm_autostart", "color": "#d86041", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_septentrio", "color": "#41d3d8", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_gps", "color": "#41d3d8", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled", "color": "#4175d8", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pwm_out", "color": "#d88a41", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pwm_out", "color": "#4194d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pwm_out", "color": "#d86041", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pwm_out", "color": "#5541d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pwm_out", "color": "#d841b4", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pwm_out", "color": "#be41d8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pwm_out", "color": "#d341d8", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_px4io", "color": "#d88a41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_px4io", "color": "#9441d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_px4io", "color": "#4194d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_px4io", "color": "#d86041", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_px4io", "color": "#5541d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_px4io", "color": "#d841b4", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_px4io", "color": "#be41d8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_px4io", "color": "#d341d8", "style": "normal"}, {"source": "t_px4io_status", "target": "m_px4io", "color": "#41d89f", "style": "normal"}, {"source": "t_tune_control", "target": "m_tone_alarm", "color": "#6041d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_battery_status", "color": "#9441d8", "style": "normal"}, {"source": "t_adc_report", "target": "m_battery_status", "color": "#41d86a", "style": "normal"}, {"source": "t_system_power", "target": "m_commander", "color": "#d84141", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_commander", "color": "#d85541", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_commander", "color": "#d86041", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_commander", "color": "#be41d8", "style": "normal"}, {"source": "t_power_button_state", "target": "m_commander", "color": "#41c9d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_commander", "color": "#4bd841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_commander", "color": "#d841d3", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_commander", "color": "#41d841", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_commander", "color": "#41d855", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_commander", "color": "#41b4d8", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_commander", "color": "#d8414b", "style": "normal"}, {"source": "t_geofence_result", "target": "m_commander", "color": "#419fd8", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_commander", "color": "#417fd8", "style": "normal"}, {"source": "t_action_request", "target": "m_commander", "color": "#d89441", "style": "normal"}, {"source": "t_event", "target": "m_commander", "color": "#d841c9", "style": "normal"}, {"source": "t_wind", "target": "m_commander", "color": "#d89f41", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_commander", "color": "#d8a941", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_commander", "color": "#418ad8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_commander", "color": "#4194d8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_commander", "color": "#a941d8", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_commander", "color": "#d8be41", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_commander", "color": "#41d88a", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_commander", "color": "#414bd8", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_commander", "color": "#c9d841", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_commander", "color": "#bed841", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_commander", "color": "#5541d8", "style": "normal"}, {"source": "t_navigator_status", "target": "m_commander", "color": "#d8418a", "style": "normal"}, {"source": "t_home_position", "target": "m_commander", "color": "#7541d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_commander", "color": "#41d8a9", "style": "normal"}, {"source": "t_telemetry_status", "target": "m_commander", "color": "#d8417f", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_commander", "color": "#9441d8", "style": "normal"}, {"source": "t_mission_result", "target": "m_commander", "color": "#41d8be", "style": "normal"}, {"source": "t_estimator_status", "target": "m_commander", "color": "#b4d841", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_commander", "color": "#d8416a", "style": "normal"}, {"source": "t_safety_button", "target": "m_commander", "color": "#9f41d8", "style": "normal"}, {"source": "t_estimator_status_flags", "target": "m_commander", "color": "#9fd841", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_commander", "color": "#d84155", "style": "normal"}, {"source": "t_logger_status", "target": "m_commander", "color": "#41d8c9", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_commander", "color": "#94d841", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_commander", "color": "#8ad841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_control_allocator", "color": "#9441d8", "style": "normal"}, {"source": "t_failure_detector_status", "target": "m_control_allocator", "color": "#41d84b", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_control_allocator", "color": "#a941d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_control_allocator", "color": "#75d841", "style": "normal"}, {"source": "t_dataman_request", "target": "m_dataman", "color": "#d84b41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ekf2", "color": "#9441d8", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_ekf2", "color": "#d85541", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_ekf2", "color": "#41a9d8", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_ekf2", "color": "#60d841", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_ekf2", "color": "#d8d341", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_ekf2", "color": "#418ad8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_ekf2", "color": "#be41d8", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_ekf2", "color": "#416ad8", "style": "normal"}, {"source": "t_airspeed", "target": "m_ekf2", "color": "#41d8d3", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_ekf2", "color": "#41d8a9", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_ekf2", "color": "#41d855", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_flight_mode_manager", "color": "#41b4d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_flight_mode_manager", "color": "#4155d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_flight_mode_manager", "color": "#9441d8", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_flight_mode_manager", "color": "#d84175", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_flight_mode_manager", "color": "#75d841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_flight_mode_manager", "color": "#be41d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_flight_mode_manager", "color": "#41d8a9", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_land_detector", "color": "#41b4d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_land_detector", "color": "#d84194", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_land_detector", "color": "#9441d8", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_land_detector", "color": "#d84175", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_land_detector", "color": "#418ad8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_land_detector", "color": "#d8416a", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_land_detector", "color": "#d84160", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_land_detector", "color": "#d86041", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_land_detector", "color": "#41d841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_land_detector", "color": "#75d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_logger", "color": "#9441d8", "style": "normal"}, {"source": "t_ulog_stream_ack", "target": "m_logger", "color": "#4141d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_logger", "color": "#5541d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_logger", "color": "#be41d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_logger", "color": "#4bd841", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_manual_control", "color": "#a941d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_manual_control", "color": "#5541d8", "style": "normal"}, {"source": "t_action_request", "target": "m_manual_control", "color": "#d89441", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_manual_control", "color": "#9441d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_mavlink", "color": "#d86041", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_mavlink", "color": "#d87541", "style": "normal"}, {"source": "t_wind", "target": "m_mavlink", "color": "#d89f41", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_mavlink", "color": "#d8a941", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_mavlink", "color": "#d8b441", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mavlink", "color": "#d8be41", "style": "normal"}, {"source": "t_vehicle_odometry", "target": "m_mavlink", "color": "#d8c941", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_mavlink", "color": "#d8d341", "style": "normal"}, {"source": "t_ulog_stream", "target": "m_mavlink", "color": "#d3d841", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_mavlink", "color": "#c9d841", "style": "normal"}, {"source": "t_estimator_status", "target": "m_mavlink", "color": "#b4d841", "style": "normal"}, {"source": "t_debug_vect", "target": "m_mavlink", "color": "#a9d841", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_mavlink", "color": "#94d841", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_mavlink", "color": "#8ad841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mavlink", "color": "#75d841", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_mavlink", "color": "#6ad841", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_mavlink", "color": "#60d841", "style": "normal"}, {"source": "t_debug_array", "target": "m_mavlink", "color": "#55d841", "style": "normal"}, {"source": "t_battery_status", "target": "m_mavlink", "color": "#4bd841", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_mavlink", "color": "#41d841", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_mavlink", "color": "#41d855", "style": "normal"}, {"source": "t_debug_key_value", "target": "m_mavlink", "color": "#41d860", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_mavlink", "color": "#d8414b", "style": "normal"}, {"source": "t_input_rc", "target": "m_mavlink", "color": "#41d87f", "style": "normal"}, {"source": "t_health_report", "target": "m_mavlink", "color": "#41d894", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mavlink", "color": "#41d8a9", "style": "normal"}, {"source": "t_satellite_info", "target": "m_mavlink", "color": "#41d8b4", "style": "normal"}, {"source": "t_mission_result", "target": "m_mavlink", "color": "#41d8be", "style": "normal"}, {"source": "t_airspeed", "target": "m_mavlink", "color": "#41d8d3", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_mavlink", "color": "#41d3d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mavlink", "color": "#41b4d8", "style": "normal"}, {"source": "t_geofence_result", "target": "m_mavlink", "color": "#419fd8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_mavlink", "color": "#418ad8", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_mavlink", "color": "#417fd8", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_mavlink", "color": "#4160d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mavlink", "color": "#4155d8", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_mavlink", "color": "#414bd8", "style": "normal"}, {"source": "t_transponder_report", "target": "m_mavlink", "color": "#4b41d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mavlink", "color": "#5541d8", "style": "normal"}, {"source": "t_register_ext_component_reply", "target": "m_mavlink", "color": "#6a41d8", "style": "normal"}, {"source": "t_home_position", "target": "m_mavlink", "color": "#7541d8", "style": "normal"}, {"source": "t_camera_status", "target": "m_mavlink", "color": "#8a41d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mavlink", "color": "#9441d8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_mavlink", "color": "#a941d8", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mavlink", "color": "#b441d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_mavlink", "color": "#be41d8", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_mavlink", "color": "#c941d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mavlink", "color": "#d841d3", "style": "normal"}, {"source": "t_event", "target": "m_mavlink", "color": "#d841c9", "style": "normal"}, {"source": "t_mission", "target": "m_mavlink", "color": "#d841be", "style": "normal"}, {"source": "t_debug_value", "target": "m_mavlink", "color": "#d841a9", "style": "normal"}, {"source": "t_dataman_response", "target": "m_mavlink", "color": "#d8419f", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_mavlink", "color": "#d8416a", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_mavlink", "color": "#d84160", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_att_control", "color": "#41b4d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mc_att_control", "color": "#4155d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_att_control", "color": "#9441d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_att_control", "color": "#5541d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mc_att_control", "color": "#d841d3", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_att_control", "color": "#41d8a9", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_att_control", "color": "#75d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_pos_control", "color": "#41b4d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_mc_pos_control", "color": "#d84194", "style": "normal"}, {"source": "t_vehicle_constraints", "target": "m_mc_pos_control", "color": "#7fd841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_pos_control", "color": "#41d8a9", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_pos_control", "color": "#75d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_rate_control", "color": "#9441d8", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mc_rate_control", "color": "#b441d8", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_mc_rate_control", "color": "#d86a41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_rate_control", "color": "#5541d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_mc_rate_control", "color": "#4bd841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_rate_control", "color": "#41d8a9", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_rate_control", "color": "#75d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_navigator", "color": "#41b4d8", "style": "normal"}, {"source": "t_dataman_response", "target": "m_navigator", "color": "#d8419f", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_navigator", "color": "#9441d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_navigator", "color": "#d8416a", "style": "normal"}, {"source": "t_transponder_report", "target": "m_navigator", "color": "#4b41d8", "style": "normal"}, {"source": "t_wind", "target": "m_navigator", "color": "#d89f41", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_navigator", "color": "#60d841", "style": "normal"}, {"source": "t_mission", "target": "m_navigator", "color": "#d841be", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_navigator", "color": "#be41d8", "style": "normal"}, {"source": "t_home_position", "target": "m_navigator", "color": "#7541d8", "style": "normal"}, {"source": "t_rtl_status", "target": "m_navigator", "color": "#41d875", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_navigator", "color": "#41d8a9", "style": "normal"}, {"source": "t_geofence_status", "target": "m_navigator", "color": "#41bed8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_rc_update", "color": "#a941d8", "style": "normal"}, {"source": "t_input_rc", "target": "m_rc_update", "color": "#41d87f", "style": "normal"}, {"source": "t_rc_parameter_map", "target": "m_rc_update", "color": "#d87f41", "style": "normal"}, {"source": "t_adc_report", "target": "m_sensors", "color": "#41d86a", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_sensors", "color": "#418ad8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_sensors", "color": "#75d841", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_sensors", "color": "#d8d341", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_sensors", "color": "#d8a941", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_sensors", "color": "#c9d841", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_sensors", "color": "#bed841", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_sensors", "color": "#d84155", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_sensors", "color": "#41d841", "style": "normal"}, {"source": "t_sensor_optical_flow", "target": "m_sensors", "color": "#7f41d8", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_sensors", "color": "#d8be41", "style": "normal"}]}