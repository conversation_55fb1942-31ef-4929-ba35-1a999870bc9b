name: <PERSON><PERSON> - <PERSON><PERSON> - Download Guide Translations

# https://github.com/crowdin/github-action/tree/master

on:
  schedule:
    - cron: '0 0 * * 0' # Runs every Sunday at 00:00 UTC
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  synchronize-with-crowdin:
    name: Synchronize with <PERSON><PERSON>
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      max-parallel: 1 # Should be 1 to avoid parallel builds
      matrix:
        lc: [ko, uk, zh-CN] # Target languages https://developer.crowdin.com/language-codes/
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Debug Environment Variables
        run: |
          echo "CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_DOCS_PROJECT_ID }}"
          echo "CROWDIN_PERSONAL_TOKEN: ${{ secrets.PX4BUILDBOT_CROWDIN_PERSONAL_TOKEN }}"
      - name: Matrix
        uses: crowdin/github-action@v2
        with:
          config: 'docs/crowdin_docs.yml'
          upload_sources: false
          upload_translations: false
          download_translations: true
          commit_message: New Crowdin translations - ${{ matrix.lc }}
          localization_branch_name: l10n_crowdin_docs_translations_${{ matrix.lc }}
          crowdin_branch_name: main
          create_pull_request: true
          pull_request_base_branch_name: 'main'
          pull_request_title: New PX4 guide translations (Crowdin) - ${{ matrix.lc }}
          pull_request_body: 'New PX4 guide Crowdin translations by [Crowdin GH Action](https://github.com/crowdin/github-action) for ${{ matrix.lc }}'
          pull_request_labels: 'Documentation 📑'
          pull_request_reviewers: hamishwillee
          download_language: ${{ matrix.lc }}
        env:
          # A classic GitHub Personal Access Token with the 'repo' scope selected (the user should have write access to the repository).
          GITHUB_TOKEN: ${{ secrets.PX4BUILTBOT_PERSONAL_ACCESS_TOKEN }}

          # A numeric ID, found at https://crowdin.com/project/<projectName>/tools/api
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_DOCS_PROJECT_ID }}

          # Visit https://crowdin.com/settings#api-key to create this token
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.PX4BUILDBOT_CROWDIN_PERSONAL_TOKEN }}
