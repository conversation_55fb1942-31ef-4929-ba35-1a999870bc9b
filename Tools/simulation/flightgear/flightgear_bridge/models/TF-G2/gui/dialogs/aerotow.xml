<?xml version="1.0"?>
<!-- Version: 2014-03-15 -->
<PropertyList>
    <name>aerotow_dialog</name>
    <layout>vbox</layout>
    <default-padding>5</default-padding>
    <modal>false</modal>
    <width>500</width>
    <!-- title bar -->
    <group>
        <layout>hbox</layout>
        <empty>
            <stretch>true</stretch>
        </empty>
        <text>
            <label>Aerotow Configuration</label>
        </text>
        <empty>
            <stretch>true</stretch>
        </empty>
        <button>
            <legend/>
            <key>Esc</key>
            <pref-width>16</pref-width>
            <pref-height>16</pref-height>
            <border>2</border>
            <binding>
                <command>dialog-close</command>
            </binding>
        </button>
    </group>
    <hrule/>
    <hrule/>
    <!-- aerotow parameters -->
    <!-- tow length -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Towrope Length</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>9999</label>
                <live>true</live>
                <format>%4.0fm </format>
                <property>/sim/hitches/aerotow/tow/length</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>20m</label>
                </text>
            </group>
            <slider>
                <name>tow_length</name>
                <property>/sim/hitches/aerotow/tow/length</property>
                <min>20</min>
                <max>200</max>
                <step>5</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>200m</label>
                </text>
            </group>
        </group>
    </group>
    <!-- break force -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Weak Link Break Force</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>99999</label>
                <live>true</live>
                <format>%5.0fN </format>
                <property>/sim/hitches/aerotow/tow/brake-force</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>100N</label>
                </text>
            </group>
            <slider>
                <name>break_force</name>
                <property>/sim/hitches/aerotow/tow/brake-force</property>
                <min>100</min>
                <max>10000</max>
                <step>100</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>10000N</label>
                </text>
            </group>
        </group>
    </group>
    <!-- tow characteristic -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Towrope Elastic Constant</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>99999</label>
                <live>true</live>
                <format>%1.0fN</format>
                <property>/sim/hitches/aerotow/tow/elastic-constant</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0N</label>
                </text>
            </group>
            <slider>
                <name>elastic_constant</name>
                <property>/sim/hitches/aerotow/tow/elastic-constant</property>
                <min>0</min>
                <max>15000</max>
                <step>10</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>15000N</label>
                </text>
            </group>
        </group>
    </group>
    <!-- tow thickness -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Towrope Diameter</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>99999</label>
                <live>true</live>
                <format>%2.1fmm</format>
                <property>/sim/hitches/aerotow/rope/rope-diameter-mm</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0.0mm</label>
                </text>
            </group>
            <slider>
                <name>rope_diameter</name>
                <property>/sim/hitches/aerotow/rope/rope-diameter-mm</property>
                <min>0</min>
                <max>50</max>
                <step>0.5</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>50.0mm</label>
                </text>
            </group>
        </group>
    </group>
    <!-- tow weight -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Towrope Weight per Meter</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>9999999</label>
                <live>true</live>
                <format>%1.3fkg/m</format>
                <property>/sim/hitches/aerotow/tow/weight-per-m-kg-m</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0.0kg/m</label>
                </text>
            </group>
            <slider>
                <name>rope_diameter</name>
                <property>/sim/hitches/aerotow/tow/weight-per-m-kg-m</property>
                <min>0</min>
                <max>1</max>
                <step>0.001</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>1.0kg/m</label>
                </text>
            </group>
        </group>
    </group>
    <hrule/>
    <hrule/>
    <!-- bottom line -->
    <group>
        <layout>hbox</layout>
        <empty>
            <stretch>true</stretch>
        </empty>
        <button>
            <legend>Close</legend>
            <binding>
                <command>dialog-close</command>
            </binding>
        </button>
        <empty>
            <stretch>true</stretch>
        </empty>
    </group>
</PropertyList>
