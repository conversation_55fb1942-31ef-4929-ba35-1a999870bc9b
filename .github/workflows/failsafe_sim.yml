name: Failsafe Simulator Build

on:
  push:
    branches:
    - 'main'
    paths-ignore:
      - 'docs/**'
      - '.github/**'
  pull_request:
    branches:
    - '*'
    paths-ignore:
      - 'docs/**'
      - '.github/**'

jobs:
  build:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    strategy:
      fail-fast: false
      matrix:
        check: [
          "failsafe_web",
        ]
    container:
      image: px4io/px4-dev:v1.16.0-rc1-258-g0369abd556
      options: --privileged --ulimit core=-1 --security-opt seccomp=unconfined
    steps:
      - name: Install Node v20.18.0
        uses: actions/setup-node@v4
        with:
          node-version: 20.18.0

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Git ownership workaround
        run: git config --system --add safe.directory '*'

      - name: Install empscripten
        run: |
          git clone https://github.com/emscripten-core/emsdk.git _emscripten_sdk
          cd _emscripten_sdk
          ./emsdk install latest
          ./emsdk activate latest

      - name: Testing [${{ matrix.check }}]
        run: |
          . ./_emscripten_sdk/emsdk_env.sh
          make ${{ matrix.check }}
