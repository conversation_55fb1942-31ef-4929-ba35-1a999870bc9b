material Polaris/Diffuse
{
  receive_shadows off
  technique
  {
    pass
    {
			ambient 1.0 1.0 1.0 1.000000

      texture_unit
      {
        texture Ranger_Diffuse.png
      }

      rtshader_system
      {
        lighting_stage per_pixel
      }
    }
  }
}
material FNR_switch_F
{
  receive_shadows off
  technique
  {
    pass
    {
      ambient 1.0 1.0 1.0 1.000
      texture_unit
      {
        texture FNR_switch_F.png
      }
    }
  }
}
material FNR_switch_R
{
  receive_shadows off
  technique
  {
    pass
    {
      ambient 1.0 1.0 1.0 1.000
      texture_unit
      {
        texture FNR_switch_R.png
      }
    }
  }
}
