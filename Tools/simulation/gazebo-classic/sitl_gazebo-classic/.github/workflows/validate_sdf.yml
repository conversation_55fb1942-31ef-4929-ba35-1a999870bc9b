name: Validate SDF

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - '*'

jobs:
  validate-sdf:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-simulation-focal:2021-05-31
    steps:
    - uses: actions/checkout@v1
    - name: submodule update
      run: git submodule update --init --recursive
    - name: Validate SDF
      shell: bash
      run: source scripts/validate_sdf.bash
