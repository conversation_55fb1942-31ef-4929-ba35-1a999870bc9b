# Complete Vehicles (Multicopter)

This section contains information about fully assembled ready to fly (RTF) or near-RTF vehicles that either run PX4 by default or can/may be usable with PX4.

:::info
This is a small subset of vehicles that can run PX4.
You can find others on [px4.io](https://px4.io/ecosystem/commercial-systems/) and in the [Airframes Reference](../airframes/airframe_reference.md).
:::

## Drone Development Kits/Reference Platforms

This section lists drone kits that are intended as platforms for further development.
They may come either fully assembled or in parts.

- [ModalAI VOXL 2 Starling PX4 Development Drone](../complete_vehicles_mc/modalai_starling.md) - SLAM development drone supercharged by VOXL 2 and PX4.
- [PX4 Vision DevKit](../complete_vehicles_mc/px4_vision_kit.md) - Multicopter used for PX4 computer vision development

## PX4 Preinstalled

This section lists vehicles that are sold fully assembled and ready to fly (RTF), with PX4 installed.

- [Teal One](https://tealdrones.com/teal-one/)
- [ModalA<PERSON> Starling](../complete_vehicles_mc/modalai_starling.md)
- [ModalAI Sentinel](https://www.modalai.com/sentinel)
- [MindRacer 210](../complete_vehicles_mc/mindracer210.md)
- [NanoMind 110](../complete_vehicles_mc/nanomind110.md)
- [Amovlab F410](../complete_vehicles_mc/amov_F410_drone.md)

## PX4 Compatible

This section lists vehicles where you can update the software to run PX4.

- [Holybro Kopis 2](../complete_vehicles_mc/holybro_kopis2.md)
- [Bitcraze Crazyflie 2.1](../complete_vehicles_mc/crazyflie21.md)
- [BetaFPV Beta75X 2S Brushless Whoop](../complete_vehicles_mc/betafpv_beta75x.md) (discontinued)

## 定制 PX4

This section contains consumer vehicles that run a _custom_ version of PX4 (supported by their vendors).
These may or may not be updatable to run "vanilla" PX4.

- [Yuneec Typhoon H Plus](https://us.yuneec.com/typhoon-h-plus/)
- [Yuneec Mantis Q](https://px4.io/portfolio/yuneec-mantis-q/)
- [Yuneec H520](https://px4.io/portfolio/yuneec-h520-hexacopter/)
- [AeroSense Aerobo (AS-MC02-P)](https://px4.io/portfolio/aerosense-aerobo/)

## See Also

- [Kits (MC)](../frames_multicopter/kits.md)
- [DIY Builds](../frames_multicopter/diy_builds.md)
- [Complete Vehicles (VTOL)](../complete_vehicles_vtol/index.md)
- [Complete Vehicles (Fixed-Wing)](../complete_vehicles_fw/index.md)
