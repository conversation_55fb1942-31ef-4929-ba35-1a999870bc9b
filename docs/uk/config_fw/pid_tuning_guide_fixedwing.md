# Посібник з налаштування регулятора швидкості/кутового положення літака

Цей посібник пояснює, як вручну налаштувати петлі PID фіксованого крила.
Призначено для досвідчених користувачів / експертів, оскільки неправильна настройка PID може зірвати ваш літак.

:::info
[Autotune](../config/autotune_fw.md) is recommended for most users, as it is far faster, easier and provides good tuning for most frames.
Рекомендується ручна настройка для кадрів, де автоналаштування не працює, або де важлива дотюнінг.
:::

## Передумови

- Спочатку необхідно налаштувати обрізки (перед настроюванням PID).
  The [Fixed-Wing Trimming Guide](../config_fw/trimming_guide_fixedwing.md) explains how.
- Неправильно встановлені виграші під час налаштування можуть зробити управління станом нестабільним.
  A pilot tuning gains should therefore be able to fly and land the plane in [manual](../flight_modes_fw/manual.md) (override) control.
- Надмірні виграші (і швидкий рух серводвигуна) можуть порушити максимальні сили вашої конструкції повітряного корпусу - збільшуйте виграші обережно.
- Налаштування крену та тангажу слідують тій самій послідовності.
  The only difference is that pitch is more sensitive to trim offsets, so [trimming](../config_fw/trimming_guide_fixedwing.md) has to be done carefully and integrator gains need more attention to compensate this.

## Встановлення базового каркасу повітряного корпусу

Якщо доступний пілот, здатний до ручного польоту, то краще встановити деякі основні властивості системи на ручному випробуванні.
Щоб це зробити, виконайте ці маневри.
Навіть якщо ви не зможете відразу зафіксувати всі кількості на папері, журнал буде дуже корисним для подальшого налаштування.

:::info
All these quantities will be automatically logged.
Вам потрібно робити записи лише у випадку, якщо ви хочете безпосередньо перейти до налаштування без перегляду журнальних файлів.

- Літаєте на рівній висоті зручною швидкістю повітря.
  Нотатка швидкість(приклад: 70% → 0,7 тролей, 15 м/с літаком).
- Піднімайтеся з максимальним режимом газу та достатньою швидкістю повітря протягом 10-30 секунд (приклад: швидкість повітря 12 м/с, піднялася на 100 м за 30 секунд).
- Знижуйте з нульовим режимом газу та розумною швидкістю повітря протягом 10-30 секунд (приклад: швидкість повітря 18 м/с, знизилася на 80 м за 30 секунд).
- Поверніть праворуч з повністю відкоченим джойстиком до 60 градусів кочення, а потім поверніть наліво з повністю відкоченим джойстиком до 60 градусів у протилежному напрямку.
- Круто підніміть на 45 градусів, круто опустіть на 45 градусів.

:::

Цей посібник використовуватиме ці кількості для встановлення деяких коефіцієнтів контролера пізніше.

## Tune Roll

Спочатку налаштуйте вісь кочення, а потім тангаж.
Ось кочення є безпечнішою, оскільки неправильна настройка призводить лише до руху, а не втрати висоти.

### Налаштування коефіцієнта передавальної функції

Щоб налаштувати цей коефіцієнт, спочатку встановіть інші коефіцієнти на їх мінімальні значення (зазвичай 0.005, але перевірте документацію параметрів).

#### Отримані виграші встановлені на мінімальні значення

- [FW_RR_I](../advanced_config/parameter_reference.md#FW_RR_I)
- [FW_RR_P](../advanced_config/parameter_reference.md#FW_RR_P)

#### Отримання для налаштування

- [FW_RR_FF](../advanced_config/parameter_reference.md#FW_RR_FF) - start with a value of 0.4.
  Збільшуйте це значення (подвоюючи його кожен раз), поки літак задовільно не кочується та не досягає встановленої точки.
  Знизити підсилення на 20% в кінці процесу.

### Налаштування коефіцієнта коефіцієнта

- [FW_RR_P](../advanced_config/parameter_reference.md#FW_RR_P) - start with a value of 0.06.
  Збільшуйте це значення (подвоюючи його кожного разу), поки система не почне дрімати / дріжати.
  Потім зменште посилення на 50%.

### Налаштування зсувів обрізання з інтегральним коефіцієнтом підсилення

- [FW_RR_I](../advanced_config/parameter_reference.md#FW_RR_I) - start with a value of 0.01.
  Збільшуйте це значення (подвоюючи його кожен раз), поки не буде відсутності між командованим та фактичним значенням кочення (це, ймовірно, потребуватиме перегляду файлу журналу).

## Tune Pitch

Можливо, вісь крену потребує більшого коефіцієнта інтегратора та правильно встановленого зміщення крену.

### Налаштування коефіцієнта передавальної функції

Для налаштування цього коефіцієнта встановіть інші коефіцієнти на їх мінімальні значення.

#### Отримані виграші встановлені на мінімальні значення

- [FW_PR_I](../advanced_config/parameter_reference.md#FW_PR_I)
- [FW_PR_P](../advanced_config/parameter_reference.md#FW_PR_I)

#### Отримання для налаштування

- [FW_PR_FF](../advanced_config/parameter_reference.md#FW_PR_FF) - start with a value of 0.4.
  Збільшуйте це значення (подвоюючи його кожного разу), поки літак не нахиляється задовільно і не досягає заданої точки.
  Знизити підсилення на 20% в кінці процесу.

### Налаштування коефіцієнта коефіцієнта

- [FW_PR_P](../advanced_config/parameter_reference.md#FW_PR_P) - start with a value of 0.04.
  Збільшуйте це значення (подвоюючи його кожного разу), поки система не почне дрімати / дріжати.
  Потім зменште значення на 50%.

### Налаштування зсувів обрізання з інтегральним коефіцієнтом підсилення

- [FW_PR_I](../advanced_config/parameter_reference.md#FW_PR_I) - start with a value of 0.01.
  Збільшуйте це значення (подвоюючи його кожен раз), поки не буде відсутності між командованим та фактичним значенням крена (це, ймовірно, потребуватиме перегляду файлу журналу).

## Налаштування часової константи зовнішньої петлі

Загальна м'якість / жорсткість керуючого циклу може бути налаштована за допомогою часової константи.
За замовчуванням 0,5 секунди повинно бути достатньо для звичайних фіксованих крил і зазвичай не потребує налаштувань.

- [FW_P_TC](../advanced_config/parameter_reference.md#FW_P_TC) - set to a default of 0.5 seconds, increase to make the Pitch response softer, decrease to make the response harder.
- [FW_R_TC](../advanced_config/parameter_reference.md#FW_R_TC) - set to a default of 0.5 seconds, increase to make the Roll response softer, decrease to make the response harder.

## Інші параметри

Найважливіші параметри охоплені в цьому керівництві.
Additional tuning parameters are documented in the [Parameter Reference](../advanced_config/parameter_reference.md).
