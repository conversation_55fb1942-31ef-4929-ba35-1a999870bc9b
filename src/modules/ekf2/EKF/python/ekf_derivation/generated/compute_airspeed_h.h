// -----------------------------------------------------------------------------
// This file was autogenerated by symforce from template:
//     function/FUNCTION.h.jinja
// Do NOT modify by hand.
// -----------------------------------------------------------------------------

#pragma once

#include <matrix/math.hpp>

namespace sym {

/**
 * This function was autogenerated from a symbolic function. Do not modify by hand.
 *
 * Symbolic function: compute_airspeed_h
 *
 * Args:
 *     state: Matrix25_1
 *     epsilon: Scalar
 *
 * Outputs:
 *     res: Matrix24_1
 */
template <typename Scalar>
matrix::Matrix<Scalar, 24, 1> ComputeAirspeedH(const matrix::Matrix<Scalar, 25, 1>& state,
                                              const Scalar epsilon) {
  // Total ops: 14

  // Input arrays

  // Intermediate terms (5)
  const Scalar _tmp0 = -state(23, 0) + state(5, 0);
  const Scalar _tmp1 = -state(22, 0) + state(4, 0);
  const Scalar _tmp2 = std::pow(Scalar(std::pow(_tmp0, Scalar(2)) + std::pow(_tmp1, Scalar(2)) +
                                       epsilon + std::pow(state(6, 0), Scalar(2))),
                                Scalar(Scalar(-1) / Scalar(2)));
  const Scalar _tmp3 = _tmp1 * _tmp2;
  const Scalar _tmp4 = _tmp0 * _tmp2;

  // Output terms (1)
  matrix::Matrix<Scalar, 24, 1> _res;

  _res.setZero();

  _res(3, 0) = _tmp3;
  _res(4, 0) = _tmp4;
  _res(5, 0) = _tmp2 * state(6, 0);
  _res(21, 0) = -_tmp3;
  _res(22, 0) = -_tmp4;

  return _res;
}  // NOLINT(readability/fn_size)

// NOLINTNEXTLINE(readability/fn_size)
}  // namespace sym
