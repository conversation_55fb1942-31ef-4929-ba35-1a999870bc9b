{# parameters #}
{%- set m = 0.0168 -%}
{%- set l = 0.038 -%}
{%- set w = 0.038 -%}
{%- set h = 0.0085 -%}
{%- set ixx = m/12*(w**2 + h**2) -%}
{%- set iyy = m/12*(l**2 + h**2) -%}
{%- set izz = m/12*(l**2 + w**2) -%}

{%- macro box(l, w, h) -%}
<geometry>
  <box>
    <size>{{l}} {{w}} {{h}}</size>
  </box>
</geometry>
{%- endmacro -%}

<?xml version="1.0" ?>
<sdf version="1.5">

  <!--3DR GPS-->
  <model name="3DR_gps_mag">

    <link name="link">

      <inertial>
        <mass>{{m}}</mass>
        <inertia>
          <ixx>{{ixx}}</ixx>
          <iyy>{{iyy}}</iyy>
          <izz>{{izz}}</izz>
        </inertia>
      </inertial>

      <visual name="visual">
        {{ box(l, w, h)|indent(8) }}
        <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
        </material>
      </visual>

      <collision name="collision">
        {{ box(l, w, h)|indent(8) }}
      </collision>

      <sensor name="ublox-neo-7M" type="gps">
        <pose>0 0 0 0 0 0</pose>
        <update_rate>10.0</update_rate>
        <always_on>true</always_on>
        <gps>
          <position_sensing>
            <horizontal>
              <noise type="gaussian_quantized">
                <mean>0</mean>
                <stddev>1</stddev>
                <bias_mean>3</bias_mean>
                <bias_stddev>1</bias_stddev>
                <precision>0.5</precision>
              </noise>
            </horizontal>
            <vertical>
              <noise type="gaussian_quantized">
                <mean>0</mean>
                <stddev>1</stddev>
                <bias_mean>3</bias_mean>
                <bias_stddev>1</bias_stddev>
                <precision>1.0</precision>
              </noise>
            </vertical>
          </position_sensing>
          <velocity_sensing>
            <horizontal>
              <noise type="gaussian_quantized">
                <mean>0</mean>
                <stddev>0.1</stddev>
                <bias_mean>0.1</bias_mean>
                <bias_stddev>0.1</bias_stddev>
                <precision>0.1</precision>
              </noise>
            </horizontal>
            <vertical>
              <noise type="gaussian_quantized">
                <mean>0</mean>
                <stddev>0.2</stddev>
                <bias_mean>0.2</bias_mean>
                <bias_stddev>0.2</bias_stddev>
                <precision>0.2</precision>
              </noise>
            </vertical>
          </velocity_sensing>
        </gps>
        <plugin name="gps_plugin" filename="libgazebo_gps_plugin.so">
          <robotNamespace></robotNamespace>
          <gpsNoise>true</gpsNoise>
          <gpsXYRandomWalk>2.0</gpsXYRandomWalk>
          <gpsZRandomWalk>4.0</gpsZRandomWalk>
          <gpsXYNoiseDensity>2.0e-4</gpsXYNoiseDensity>
          <gpsZNoiseDensity>4.0e-4</gpsZNoiseDensity>
          <gpsVXYNoiseDensity>0.2</gpsVXYNoiseDensity>
          <gpsVZNoiseDensity>0.4</gpsVZNoiseDensity>
        </plugin>
      </sensor>

      <sensor name="HMC5883L" type="magnetometer">
        <always_on>true</always_on>
        <update_rate>50</update_rate>
        <magnetometer>
          <x>
            <noise type="gaussian">
             <mean>0</mean>
             <stddev>0.0002</stddev>
             <bias_mean>7.5e-06</bias_mean>
             <bias_stddev>8e-07</bias_stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
             <mean>0</mean>
             <stddev>0.0002</stddev>
             <bias_mean>7.5e-06</bias_mean>
             <bias_stddev>8e-07</bias_stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
             <mean>0</mean>
             <stddev>0.0002</stddev>
             <bias_mean>7.5e-06</bias_mean>
             <bias_stddev>8e-07</bias_stddev>
            </noise>
          </z>
        </magnetometer>

      </sensor>

    </link>

  </model>

</sdf>


<!-- vim: set et ft=xml fenc=utf-8 ff=unix sts=0 sw=2 ts=2 : -->
