<?xml version="1.0" encoding="UTF-8"?>
<sdf version="1.9">
  <model name="flat_platform">
    <link name="platform_link">
      <visual name="platform_visual">
        <geometry>
          <box>
            <size>5 5 0.1</size>
          </box>
        </geometry>
        <material>
          <ambient>0.3 0.3 0.3 1</ambient>
          <diffuse>0.3 0.3 0.3 1</diffuse>
          <specular>0.1 0.1 0.1 1</specular>
        </material>
      </visual>
      <collision name="platform_collision">
        <geometry>
          <box>
            <size>5 5 0.1</size>
          </box>
        </geometry>
      </collision>
      <pose>0 0 2 0 0 0</pose>
      <inertial>
          <mass>10000</mass>
          <inertia>
              <ixx>10000</ixx>
              <iyy>10000</iyy>
              <izz>10000</izz>
          </inertia>
      </inertial>
      <sensor name="navsat_sensor" type="navsat">
        <always_on>1</always_on>
        <update_rate>30</update_rate>
      </sensor>
    </link>
    <plugin
      filename="libMovingPlatformController.so"
      name="custom::MovingPlatformController">
      <link_name>platform_link</link_name>
    </plugin>
  </model>
</sdf>
