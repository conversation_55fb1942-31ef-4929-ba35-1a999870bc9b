module_name: ekf2
parameters:
- group: EKF2
  definitions:
    EKF2_ARSP_THR:
      description:
        short: Airspeed fusion threshold
        long: Airspeed data is fused for wind estimation if above this threshold.
          Set to 0 to disable airspeed fusion. For reliable wind estimation both sideslip
          (see EKF2_FUSE_BETA) and airspeed fusion should be enabled. Only applies
          to fixed-wing vehicles (or VTOLs in fixed-wing mode).
      type: float
      default: 0.0
      min: 0.0
      unit: m/s
      decimal: 1
    EKF2_ASP_DELAY:
      description:
        short: Airspeed measurement delay relative to IMU measurements
      type: float
      default: 100
      min: 0
      max: 300
      unit: ms
      reboot_required: true
      decimal: 1
    EKF2_TAS_GATE:
      description:
        short: Gate size for TAS fusion
        long: Sets the number of standard deviations used by the innovation consistency
          test.
      type: float
      default: 5.0
      min: 1.0
      unit: SD
      decimal: 1
    EKF2_EAS_NOISE:
      description:
        short: Measurement noise for airspeed fusion
      type: float
      default: 1.4
      min: 0.5
      max: 5.0
      unit: m/s
      decimal: 1
