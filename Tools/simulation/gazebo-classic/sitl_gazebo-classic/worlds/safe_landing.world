<?xml version="1.0" ?>
<sdf version='1.7'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
      <spot>
        <inner_angle>0</inner_angle>
        <outer_angle>0</outer_angle>
        <falloff>0</falloff>
      </spot>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <contact>
              <collide_bitmask>65535</collide_bitmask>
              <ode/>
            </contact>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
            </friction>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <physics name='default_physics' default='0' type='ode'>
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>10</iters>
          <sor>1.3</sor>
          <use_dynamic_moi_rescaling>0</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
      <magnetic_field>6.0e-6 2.3e-5 -4.2e-5</magnetic_field>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='ramp'>
      <static>1</static>
      <pose>7.71205 7.95431 0.2 0 0.15 0</pose>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 4 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>20 4 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts/asphalt.material</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='oak_tree'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://oak_tree/meshes/oak_tree.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='branch'>
          <geometry>
            <mesh>
              <uri>model://oak_tree/meshes/oak_tree.dae</uri>
              <submesh>
                <name>Branch</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://oak_tree/materials/scripts/</uri>
              <uri>model://oak_tree/materials/textures/</uri>
              <name>OakTree/Branch</name>
            </script>
          </material>
        </visual>
        <visual name='bark'>
          <geometry>
            <mesh>
              <uri>model://oak_tree/meshes/oak_tree.dae</uri>
              <submesh>
                <name>Bark</name>
              </submesh>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://oak_tree/materials/scripts/</uri>
              <uri>model://oak_tree/materials/textures/</uri>
              <name>OakTree/Bark</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
      <pose>-9.08674 7.54965 0 0 -0 0</pose>
    </model>
    <model name='Shelves high'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://shelves_high/meshes/shelves_high_collision.dae</uri>
              <scale>1 1.2 1.2</scale>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://shelves_high/meshes/shelves_high.dae</uri>
              <scale>1 1.2 1.2</scale>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
      <pose>6.64902 -7.94627 0 0 -0 0</pose>
    </model>
    <state world_name='default'>
      <model name='EuroPallet'>
        <pose>-1.72756 -8.87783 0.061865 0.001727 -0.004562 0.246745</pose>
        <link name='link'>
          <pose>-1.72756 -8.87783 0.061865 0.001727 -0.004562 0.246745</pose>
        </link>
      </model>
      <model name='EuroPallet_0'>
        <pose>-1.1 -9.0 0.2 0.00101 0.0174 0.520475</pose>
        <link name='link'>
          <pose>-1.1 -9.0 0.2 0.00101 0.0174 0.520475</pose>
        </link>
      </model>
      <model name='EuroPallet_1'>
        <pose>-2.27651 -7.75114 0.0635 0.0 0.0 0.008197</pose>
        <link name='link'>
          <pose>-2.27651 -7.75114 0.0635 0.0 0.0 0.008197</pose>
        </link>
      </model>
      <model name='EuroPallet_2'>
        <pose>-1.09738 -7.28374 0.062318 0.00028 -0.002704 -0.7038</pose>
        <link name='link'>
          <pose>-1.09738 -7.28374 0.062318 0.00028 -0.002704 -0.7038</pose>
        </link>
      </model>
      <model name='EuroPallet_3'>
        <pose>-2.0333 -7.09947 0.3 3.07019 -0.179045 -0.720548</pose>
        <link name='link'>
          <pose>-2.0333 -7.09947 0.3 3.07019 -0.179045 -0.720548</pose>
        </link>
      </model>
      <model name='Shelves high'>
        <pose>6.64902 -7.94627 0 0 -0 0</pose>
        <link name='link'>
          <pose>6.64902 -7.94627 0 0 -0 0</pose>
        </link>
      </model>
      <model name='ground_plane'>
        <pose>0 0 0 0 -0 0</pose>
        <link name='link'>
          <pose>0 0 0 0 -0 0</pose>
        </link>
      </model>
      <model name='oak_tree'>
        <pose>-9.08674 7.54965 0 0 -0 0</pose>
        <link name='link'>
          <pose>-9.08674 7.54965 0 0 -0 0</pose>
        </link>
      </model>
      <model name='ramp'>
        <pose>7.71205 7.95431 0.2 0 0.15 0</pose>
        <link name='link'>
          <pose>7.71205 7.95431 0.2 0 0.15 0</pose>
        </link>
      </model>
      <model name='unit_box'>
        <pose>6.9155 -2.903 0.5 0 0 0</pose>
        <link name='link'>
          <pose>6.9155 -2.903 0.5 0 0 0</pose>
        </link>
      </model>
      <model name='unit_cylinder'>
        <pose>5.10526 -1.94042 0.5 0 0 0</pose>
        <link name='link'>
          <pose>5.10526 -1.94042 0.5 0 0 0</pose>
        </link>
      </model>
      <model name='unit_cylinder_0'>
        <pose>7.29937 -0.680724 0.5 0 0 0</pose>
        <link name='link'>
          <pose>7.29937 -0.680724 0.5 0 0 0</pose>
        </link>
      </model>
    </state>
    <model name='unit_cylinder'>
      <pose>5.10526 -1.94042 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_box'>
      <pose>6.9155 -2.903 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.166667</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.166667</iyy>
            <iyz>0</iyz>
            <izz>0.166667</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='unit_cylinder_0'>
      <pose>7.29937 -0.680722 0.5 0 -0 0</pose>
      <link name='link'>
        <inertial>
          <mass>1</mass>
          <inertia>
            <ixx>0.145833</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>0.145833</iyy>
            <iyz>0</iyz>
            <izz>0.125</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='collision'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <cylinder>
              <radius>0.5</radius>
              <length>1</length>
            </cylinder>
          </geometry>
          <material>
            <script>
              <name>Gazebo/Grey</name>
              <uri>file://media/materials/scripts/gazebo.material</uri>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='EuroPallet'>
      <pose>-1.89404 -8.80524 0.0635 0 -0 0</pose>
      <static>0</static>
      <link name='link'>
        <inertial>
          <mass>25</mass>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='top'>
          <pose>0 0 0.0625 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.8 0.042</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_1'>
          <pose>0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_2'>
          <pose>0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_3'>
          <pose>0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_4'>
          <pose>0 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_5'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_6'>
          <pose>0 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_7'>
          <pose>-0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_8'>
          <pose>-0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_9'>
          <pose>-0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_1'>
          <pose>0 -0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_2'>
          <pose>0 0 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.145 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_3'>
          <pose>0 0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://europallet/meshes/europallet.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='EuroPallet_0'>
      <pose>-1.39724 -8.51679 0.0635 0 -0 0</pose>
      <static>0</static>
      <link name='link'>
        <inertial>
          <mass>25</mass>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='top'>
          <pose>0 0 0.0625 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.8 0.042</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_1'>
          <pose>0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_2'>
          <pose>0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_3'>
          <pose>0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_4'>
          <pose>0 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_5'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_6'>
          <pose>0 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_7'>
          <pose>-0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_8'>
          <pose>-0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_9'>
          <pose>-0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_1'>
          <pose>0 -0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_2'>
          <pose>0 0 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.145 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_3'>
          <pose>0 0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://europallet/meshes/europallet.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='EuroPallet_1'>
      <pose>-2.07711 -8.10341 0.0635 0 -0 0</pose>
      <static>0</static>
      <link name='link'>
        <inertial>
          <mass>25</mass>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='top'>
          <pose>0 0 0.0625 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.8 0.042</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_1'>
          <pose>0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_2'>
          <pose>0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_3'>
          <pose>0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_4'>
          <pose>0 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_5'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_6'>
          <pose>0 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_7'>
          <pose>-0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_8'>
          <pose>-0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_9'>
          <pose>-0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_1'>
          <pose>0 -0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_2'>
          <pose>0 0 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.145 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_3'>
          <pose>0 0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://europallet/meshes/europallet.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='EuroPallet_2'>
      <pose>-1.43979 -7.27333 0.0635 0 -0 0</pose>
      <static>0</static>
      <link name='link'>
        <inertial>
          <mass>25</mass>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='top'>
          <pose>0 0 0.0625 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.8 0.042</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_1'>
          <pose>0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_2'>
          <pose>0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_3'>
          <pose>0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_4'>
          <pose>0 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_5'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_6'>
          <pose>0 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_7'>
          <pose>-0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_8'>
          <pose>-0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_9'>
          <pose>-0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_1'>
          <pose>0 -0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_2'>
          <pose>0 0 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.145 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_3'>
          <pose>0 0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://europallet/meshes/europallet.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <model name='EuroPallet_3'>
      <pose>-2.1889 -6.95929 0.0635 0 -0 0</pose>
      <static>0</static>
      <link name='link'>
        <inertial>
          <mass>25</mass>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <pose>0 0 0 0 -0 0</pose>
        </inertial>
        <collision name='top'>
          <pose>0 0 0.0625 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.8 0.042</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_1'>
          <pose>0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_2'>
          <pose>0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_3'>
          <pose>0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_4'>
          <pose>0 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_5'>
          <pose>0 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_6'>
          <pose>0 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_7'>
          <pose>-0.5275 -0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_8'>
          <pose>-0.5275 0 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.145 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='colum_9'>
          <pose>-0.5275 0.35 0 0 -0 0</pose>
          <geometry>
            <box>
              <size>0.145 0.1 0.085</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_1'>
          <pose>0 -0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_2'>
          <pose>0 0 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.145 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <collision name='bottom_3'>
          <pose>0 0.35 -0.053 0 -0 0</pose>
          <geometry>
            <box>
              <size>1.2 0.1 0.021</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <contact>
              <ode/>
            </contact>
            <bounce/>
            <friction>
              <ode/>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://europallet/meshes/europallet.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>25.8348 12.9922 17.3934 0 0.463643 -2.62299</pose>
        <view_controller>orbit</view_controller>
        <projection_type>perspective</projection_type>
      </camera>
    </gui>
  </world>
</sdf>
