<?xml version="1.0"?>
<?xml-stylesheet href="http://jsbsim.sourceforge.net/JSBSim.xsl" type="text/xsl"?>
<fdm_config name="rascal" version="2.0" release="BETA"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:noNamespaceSchemaLocation="http://jsbsim.sourceforge.net/JSBSim.xsd">

    <fileheader>
        <author> Author Name </author>
        <filecreationdate> Creation Date </filecreationdate>
        <version> Version </version>
        <description> Models a Malolo </description>
    </fileheader>

    <metrics>
        <wingarea unit="FT2"> 10.57 </wingarea>
        <wingspan unit="FT"> 9.17 </wingspan>
        <chord unit="FT"> 1.15 </chord>
        <htailarea unit="FT2"> 1.69 </htailarea>
        <htailarm unit="FT"> 3.28 </htailarm>
        <vtailarea unit="FT2"> 1.06 </vtailarea>
        <vtailarm unit="FT"> 0 </vtailarm>
        <location name="AERORP" unit="IN">
            <x> 37.4 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
        <location name="EYEPOINT" unit="IN">
            <x> 20 </x>
            <y> 0 </y>
            <z> 5 </z>
        </location>
        <location name="VRP" unit="IN">
            <x> 0 </x>
            <y> 0 </y>
            <z> 0 </z>
        </location>
    </metrics>

    <mass_balance>
        <ixx unit="SLUG*FT2"> 1 </ixx>
        <iyy unit="SLUG*FT2"> 1 </iyy>
        <izz unit="SLUG*FT2"> 2 </izz>
        <ixy unit="SLUG*FT2"> 0 </ixy>
        <ixz unit="SLUG*FT2"> 0 </ixz>
        <iyz unit="SLUG*FT2"> 0 </iyz>
        <emptywt unit="LBS"> 12 </emptywt>
        <location name="CG" unit="IN">
            <x> 36.4 </x>
            <y> 0 </y>
            <z> 4 </z>
        </location>
        <pointmass name="payload">
            <weight unit="LBS"> 1 </weight>
            <location name="payload" unit="IN">
                <x> 0 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
        </pointmass>
    </mass_balance>

    <ground_reactions>
        <contact type="BOGEY" name="LEFT_MLG">
            <location unit="IN">
                <x> 40.1 </x>
                <y> -9.9 </y>
                <z> -10.1 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 120 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 20 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> LEFT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="RIGHT_MLG">
            <location unit="IN">
                <x> 40.1 </x>
                <y> 9.9 </y>
                <z> -10.1 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 120 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 20 </damping_coeff>
            <max_steer unit="DEG"> 0.0 </max_steer>
            <brake_group> RIGHT </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="TAIL_LG">
            <location unit="IN">
                <x> 68.9 </x>
                <y> 0 </y>
                <z>-4.3 </z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 24 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 20 </damping_coeff>
            <max_steer unit="DEG"> 360.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>
        <contact type="BOGEY" name="NOSE_LG">
            <location unit="IN">
                <x>10</x>
                <y>0</y>
                <z>-8.3</z>
            </location>
            <static_friction> 0.8 </static_friction>
            <dynamic_friction> 0.5 </dynamic_friction>
            <rolling_friction> 0.02 </rolling_friction>
            <spring_coeff unit="LBS/FT"> 24 </spring_coeff>
            <damping_coeff unit="LBS/FT/SEC"> 20 </damping_coeff>
            <max_steer unit="DEG"> 360.0 </max_steer>
            <brake_group> NONE </brake_group>
            <retractable>0</retractable>
        </contact>

    </ground_reactions>

    <propulsion>
        <engine file="Zenoah_G-26A">
            <location unit="IN">
                <x> 36 </x>
                <y> 0 </y>
                <z> 0 </z>
            </location>
            <orient unit="DEG">
                <roll> 0.0 </roll>
                <pitch> 0 </pitch>
                <yaw> 0 </yaw>
            </orient>
            <feed>0</feed>
            <thruster file="18x8">
                <location unit="IN">
                    <x> 1 </x>
                    <y> 0 </y>
                    <z> 0 </z>
                </location>
                <orient unit="DEG">
                    <roll> 0.0 </roll>
                    <pitch> 0.0 </pitch>
                    <yaw> 0.0 </yaw>
                </orient>
                <p_factor>1.0</p_factor>
            </thruster>
        </engine>
        <tank type="FUEL">    <!-- Tank number 0 --> 
            <location unit="IN">
                <x> 36.36 </x>
                <y> 0 </y>
                <z> -1.89375 </z>
            </location>
            <capacity unit="LBS"> 1.5 </capacity>
            <contents unit="LBS"> 1.5 </contents>
        </tank>
    </propulsion>

    <flight_control name="FCS: rascal">
     <channel name="All">

        <summer name="Pitch Trim Sum">
            <input>fcs/elevator-cmd-norm</input>
            <input>fcs/pitch-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Elevator Control">
            <input>fcs/pitch-trim-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.3</max>
            </range>
            <output>fcs/elevator-pos-rad</output>
        </aerosurface_scale>

        <aerosurface_scale name="Elevator Normalized">
            <input>fcs/elevator-pos-rad</input>
            <domain>
                <min>-0.3</min>
                <max> 0.3</max>
            </domain>
            <range>
                <min>-1</min>
                <max> 1</max>
            </range>
            <output>fcs/elevator-pos-norm</output>
        </aerosurface_scale>

        <summer name="Roll Trim Sum">
            <input>fcs/aileron-cmd-norm</input>
            <input>fcs/roll-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Left Aileron Control">
            <input>fcs/roll-trim-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/left-aileron-pos-rad</output>
        </aerosurface_scale>

        <aerosurface_scale name="Right Aileron Control">
            <input>-fcs/roll-trim-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/right-aileron-pos-rad</output>
        </aerosurface_scale>

        <aerosurface_scale name="Left aileron Normalized">
            <input>fcs/left-aileron-pos-rad</input>
            <domain>
                <min>-0.35</min>
                <max> 0.35</max>
            </domain>
            <range>
                <min>-1</min>
                <max> 1</max>
            </range>
            <output>fcs/left-aileron-pos-norm</output>
        </aerosurface_scale>

        <aerosurface_scale name="Right aileron Normalized">
            <input>fcs/right-aileron-pos-rad</input>
            <domain>
                <min>-0.35</min>
                <max> 0.35</max>
            </domain>
            <range>
                <min>-1</min>
                <max> 1</max>
            </range>
            <output>fcs/right-aileron-pos-norm</output>
        </aerosurface_scale>

        <summer name="Rudder Command Sum">
            <input>fcs/rudder-cmd-norm</input>
            <input>fcs/yaw-trim-cmd-norm</input>
            <clipto>
                <min>-1</min>
                <max>1</max>
            </clipto>
        </summer>

        <aerosurface_scale name="Rudder Control">
            <input>fcs/rudder-command-sum</input>
            <range>
                <min>-0.35</min>
                <max>0.35</max>
            </range>
            <output>fcs/rudder-pos-rad</output>
        </aerosurface_scale>

        <aerosurface_scale name="Rudder Normalized">
            <input>fcs/rudder-pos-rad</input>
            <domain>
                <min>-0.35</min>
                <max> 0.35</max>
            </domain>
            <range>
                <min>-1</min>
                <max> 1</max>
            </range>
            <output>fcs/rudder-pos-norm</output>
        </aerosurface_scale>
     </channel>
    </flight_control>
    <aerodynamics>

        <axis name="DRAG">
            <function name="aero/coefficient/CD0">
                <description>Drag_at_zero_lift</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -1.5700	1.5000	
                              -0.2600	0.0560	
                              0.0000	0.0280	
                              0.2600	0.0560	
                              1.5700	1.5000	
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDi">
                <description>Induced_drag</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/cl-squared</property>
                    <value>0.0400</value>
                </product>
            </function>
            <function name="aero/coefficient/CDbeta">
                <description>Drag_due_to_sideslip</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/beta-rad</independentVar>
                          <tableData>
                              -1.5700	1.2300	
                              -0.2600	0.0500	
                              0.0000	0.0000	
                              0.2600	0.0500	
                              1.5700	1.2300	
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CDde">
                <description>Drag_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-norm</property>
                    <value>0.0300</value>
                </product>
            </function>
        </axis>

        <axis name="SIDE">
            <function name="aero/coefficient/CYb">
                <description>Side_force_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>aero/beta-rad</property>
                    <value>-1.0000</value>
                </product>
            </function>
        </axis>

        <axis name="LIFT">
            <function name="aero/coefficient/CLalpha">
                <description>Lift_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                      <table>
                          <independentVar>aero/alpha-rad</independentVar>
                          <tableData>
                              -0.2000	-0.7500	
                              0.0000	0.2500	
                              0.2300	1.4000	
                              0.6000	0.7100	
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/CLde">
                <description>Lift_due_to_Elevator_Deflection</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>fcs/elevator-pos-rad</property>
                    <value>0.2000</value>
                </product>
            </function>
        </axis>

        <axis name="ROLL">
            <function name="aero/coefficient/Clb">
                <description>Roll_moment_due_to_beta</description>
                <!-- aka dihedral effect -->
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>-0.1000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clp">
                <description>Roll_moment_due_to_roll_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/p-aero-rad_sec</property>
                    <value>-0.4000</value>
                </product>
            </function>
            <function name="aero/coefficient/Clr">
                <description>Roll_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Clda">
                <description>Roll_moment_due_to_aileron</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	0.1300	
                              2.0000	0.0570	
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cldr">
                <description>Roll_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>0.0100</value>
                </product>
            </function>
        </axis>

        <axis name="PITCH">
            <function name="aero/coefficient/Cmalpha">
                <description>Pitch_moment_due_to_alpha</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/alpha-rad</property>
                    <value>-0.5000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmde">
                <description>Pitch_moment_due_to_elevator</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>fcs/elevator-pos-rad</property>
                      <table>
                          <independentVar>velocities/mach</independentVar>
                          <tableData>
                              0.0000	-0.5000	<!-- was -1.1 -->
                              2.0000	-0.2750	
                          </tableData>
                      </table>
                </product>
            </function>
            <function name="aero/coefficient/Cmq">
                <description>Pitch_moment_due_to_pitch_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>velocities/q-aero-rad_sec</property>
                    <value>-12.0000</value>
                </product>
            </function>
            <function name="aero/coefficient/Cmadot">
                <description>Pitch_moment_due_to_alpha_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/cbarw-ft</property>
                    <property>aero/ci2vel</property>
                    <property>aero/alphadot-rad_sec</property>
                    <value>-7.0000</value>
                </product>
            </function>
        </axis>

        <axis name="YAW">
            <function name="aero/coefficient/Cnb">
                <description>Yaw_moment_due_to_beta</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/beta-rad</property>
                    <value>0.1200</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnr">
                <description>Yaw_moment_due_to_yaw_rate</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>aero/bi2vel</property>
                    <property>velocities/r-aero-rad_sec</property>
                    <value>-0.1500</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndr">
                <description>Yaw_moment_due_to_rudder</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/rudder-pos-rad</property>
                    <value>-0.0500</value>
                </product>
            </function>
            <function name="aero/coefficient/Cnda">
                <description>Adverse_yaw</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <property>fcs/left-aileron-pos-rad</property>
                    <value>-0.0300</value>
                </product>
            </function>
            <function name="aero/coefficient/Cndi">
                <description>Yaw_moment_due_to_tail_incidence</description>
                <product>
                    <property>aero/qbar-psf</property>
                    <property>metrics/Sw-sqft</property>
                    <property>metrics/bw-ft</property>
                    <value>0.0007</value>
                </product>
            </function>
        </axis>
    </aerodynamics>
</fdm_config>
