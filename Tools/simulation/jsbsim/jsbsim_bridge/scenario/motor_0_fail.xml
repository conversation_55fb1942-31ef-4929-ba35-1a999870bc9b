<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="http://jsbsim.sf.net/JSBSimScript.xsl"?>
<runscript xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://jsbsim.sf.net/JSBSimScript.xsd"
    name="Testing Template">

  <description>
      Template for generic motor fail test.
  </description>

  <use aircraft="quadrotor_x"/>

  <run start="0" end="10000000" dt="0.004">

    <event name="Fail Motor 0">
      <description> Demonstrate Motor Failure Trigger </description>
      <condition>
          position/h-sl-meters ge 425
      </condition>
      <set name="fcs/motor_health[0]" value="0.0" action="FG_RAMP" tc="5.0"/>
      <notify>
        <property>simulation/sim-time-sec</property>
      </notify>
    </event>

  </run>

</runscript>