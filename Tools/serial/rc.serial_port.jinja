{# jinja template to generate the serial port rc.uart_port script. #}

# serial port script generated with generate_serial_config.py

set SERIAL_DEV none
{% for serial_device in serial_devices -%}
if param compare "$PRT" {{ serial_device.index }}; then
	if [ "x$PRT_{{ serial_device.tag }}_" = "x" ]; then
		set SERIAL_DEV {{ serial_device.device }}
		set BAUD_PARAM SER_{{ serial_device.tag }}_BAUD
		set PRT_{{ serial_device.tag }}_ 1
{% if not constrained_flash -%}
	else
		echo "Conflicting config for {{ serial_device.device }}"
{% endif -%}
	fi
fi

{% endfor %}

{% for config in ethernet_configuration -%}
if param compare "$PRT" {{ config.index }}; then
	set SERIAL_DEV ethernet
fi
{% endfor %}
