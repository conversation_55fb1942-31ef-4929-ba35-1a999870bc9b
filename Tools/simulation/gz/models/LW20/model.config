<?xml version="1.0"?>
<!--
IMPORTANT
This model just adds a Mesh, but no sensor
This is because the orientation of the gz message is the pose in respect the sensor link, and not in respect of the base_link
in GZBridge we can only read out the pose of the sensor link, so to be able to differentiate different sensor orientations, the sensor needs to be in the parent model.
-->
<model>
  <name>LW20</name>
  <version>1.0</version>
  <sdf version="1.6">model.sdf</sdf>

  <author>
    <name><PERSON></name>
    <email><EMAIL></email>
  </author>

  <description>
    A Lightware LW20/C Lidar Model
  </description>
</model>
