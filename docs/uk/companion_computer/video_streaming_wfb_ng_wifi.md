# Потокова передача відео через канал зв'язку WiFi в режимі Raw (WFB-ng)

Цей посібник показує, як налаштувати [супутній комп'ютер](../companion_computer/README.md) з камерою Logitech C920 або RaspberryPi так, щоб відеопотік передавався з БЛА на земний комп'ютер і відображався в _QGroundControl_.
Для налаштування використовується WiFi у режимі непідключеного (транслювання) та програмне забезпечення проекту [WFB-ng](https://github.com/svpcom/wfb-ng).

Канал також може бути використаний як двосторонній [телеметричний](../telemetry/README.md) зв'язок та тунель TCP/IP для керування дроном під час польоту.
Якщо ви керуєте дроном вручну за допомогою джойстика з QGroundControl (яке використовує MAVLink), то ви можете використовувати WFB-ng як єдине з'єднання для всіх комунікацій з дроном (відео, телеметрія MAVLink, дистанційне керування за допомогою джойстика).

:::warning
Перш ніж використовувати _WFB-ng_, перевірте, чи дозволяють регулятори такий тип використання WiFi в вашій країні.
:::

## Загальний огляд WFB-ng

Проект _WFB-ng_ надає транспорт даних, який використовує низькорівневі пакети WiFi для уникнення обмежень відстані та затримки звичайного стеку IEEE 802.11.

Основні переваги _WFB-ng_ включають:

- Низька затримка відеозв'язку.
- Двосторонній телеметричний зв'язок (MAVLink).
- TCP/IP тунель.
- Автоматичне різноманіття передавача - використовуйте кілька карт на землі, щоб уникнути відслідковувача антен.
- Повне шифрування та аутентифікація зв'язку (з використанням [libsodium](https://download.libsodium.org/doc/)).
- Агрегація пакетів MAVLink (упаковка невеликих пакетів у партії перед передачею).
- Покращений [OSD](https://github.com/svpcom/wfb-ng-osd) для Raspberry PI або загального лінуксового робочого стола з gstreamer.

Додаткова інформація наведена в [FAQ](#faq) нижче.

## Апаратне забезпечення(Hardware)

### Налаштування транспорту

Налаштування транспортного засобу складається з такого:

- Raspberry PI 3B/3B+/ZeroW

- Камера.
  Були протестовані наступні варіанти:

  - [Камера Raspberry Pi](https://www.raspberrypi.org/products/camera-module-v2/), підключена через CSI.
  - [Камера Logitech C920](https://www.logitech.com/en-us/product/hd-pro-webcam-c920?crid=34), підключена через USB

- Модуль WiFi [ALPHA AWUS036ACH](https://www.alfa.com.tw/products_detail/1.htm) або будь-яка інша карта на основі **RTL8812au**.

### Наземна станція

- Наземний комп'ютер на станції.
  Ці варіанти були перевірені:

  - Будь-який Linux комп'ютер з USB-портом (протестований на Ubuntu 18.04 x86-64)
  - Комп’ютер із будь-якою ОС із керуванням QGround та Raspberry PI, під’єднаний через Ethernet (RasPi забезпечує з’єднання Wi-Fi).

- Модуль WiFi [ALPHA AWUS036ACH](https://www.alfa.com.tw/products_detail/1.htm) або будь-яка інша карта на основі **RTL8812au**.
  Див. вікі [WFB-ng > апаратне забезпечення WiFi](https://github.com/svpcom/wfb-ng/wiki/WiFi-hardware) для отримання додаткової інформації про підтримувані модулі.

## Модифікація апаратного забезпечення

Alpha AWUS036ACH - це карта середньої потужності, яка використовує багато струму під час передачі.
Якщо ви живите її від звичайного USB2, то на більшості **ARM-плат** вона скине порт.
Якщо ви підключите її до порту **USB3** за допомогою **нативного кабелю USB3** до ноутбука на **Linux**, ви можете використовувати її без модифікацій.

Для **Raspberry PI** (UAV або земля) її необхідно підключити безпосередньо до 5V BEC (або адаптера високої потужності для земельного pi) одним із двох способів:

- Зробіть власний USB-кабель ([відірвіть провід `+5V` від USB-штепселя і підключіть його до BEC])(https://electronics.stackexchange.com/questions/218500/usb-charge-and-data-separate-cables)
- Відірвіть провід `+5V` на платі поруч із USB-портом і підключіть його до BEC (не робіть цього, якщо сумніваєтеся - використовуйте власний кабель).

Вам також потрібно додати **конденсатор низького опору** з ємністю 470 мкФ (як у ESC) між **+5В карти та землею**, щоб фільтрувати перепади напруги.
Ви повинні інтегрувати конденсатор з власним USB-кабелем.
Без конденсатора ви можете отримати втрату пакетів або їх порушення. Будьте обережні з петлею маси при використанні декількох земляних проводів.
Будьте обережні з [петлею маси](https://en.wikipedia.org/wiki/Ground_loop_%28electricity%29) при використанні декількох земляних проводів.

:::info
Якщо ви використовуєте спеціальні "дуже" потужні карти з Taobao/Aliexpress, то ВИ МАЄТЕ живити їх так, як описано вище в БУДЬ-ЯКОМУ випадку.
:::

### Конфігурація UAV

1. Завантажте образ Raspberry PI з [останнього випуску wfb-ng](https://github.com/svpcom/wfb-ng/releases/)
2. Виконайте прошивку на Raspberry PI **UAV**
3. Перезавантажте його і підключіться через ssh зі стандартними обліковими даними (pi/raspberry).
4. Виконайте дії для ролі **повітря**, як показано в motd.
5. Налаштуйте камерний канал. Відкрийте `/etc/systemd/system/fpv-camera.service` і розкоментуйте конвеєр відповідно до вашої камери (камера PI або камера Logitech)
6. Відкрийте `/etc/wifibroadcast.cfg` і налаштуйте канал WiFi відповідно до налаштувань вашої антени (або використовуйте замовчуваний #165 для 5.8GHz)
7. Налаштуйте PX4 на вивід потоку телеметрії зі швидкістю 1500 Кбіт/с (інші швидкості UART не добре відповідають дільникам частоти RPI).
  Підключіть UART Pixhawk до UART Raspberry PI.
  У розділі `/etc/wifibroadcast.cfg` файлу розкоментуйте `peer = 'serial:ttyS0:1500000'` секцію.

### Використання ноутбука Linux як GCS (важче, ніж використання RasPi)

1. На **наземному** Linux комп'ютері розробки:

  ```sh
  sudo apt install libpcap-dev libsodium-dev python3-all python3-twisted
  git clone -b stable https://github.com/svpcom/wfb-ng.git
  cd wfb-ng && make deb && sudo apt install ./deb_dist/wfb-ng*.deb
  ```

2. Слідуйте інструкції з [Setup HOWTO](https://github.com/svpcom/wfb-ng/wiki/Setup-HOWTO) для завершення встановлення

3. Не забудьте скопіювати `/etc/gs.key` з боку **UAV** на **бік землі**, щоб зв'язати два налаштування.

4. Також не забудьте використовувати той самий канал частоти, що й на боці UAV.

### Використання Raspberry PI як БЗК (легше)

Якщо у вас є Windows або OSX, або ви не хочете налаштовувати WFB-ng на свій ноутбук з Linux, тоді ви можете використовувати той самий готовий образ і ще один Raspberry Pi:

1. Запишіть образ на **ground** Raspberry Pi.
2. Перезавантажте його і підключіться через SSH за стандартними обліковими даними (pi/raspberry).
3. Виконайте дії для ролі **землі**, як показано в motd, але пропустіть налаштування служби `fpv-video` та `osd`.
4. Підключіть ваш ноутбук та земельний RasPi через Ethernet та налаштуйте IP-адреси
5. Відредагуйте `/etc/wifibroadcast.cfg` і встановіть IP-адресу ноутбука в розділах `[gs_mavlink]` та `[gs_video]` (замінивши `127.0.0.1`).

### Налаштування QGroundControl

1. Запустіть _QGroundControl_ і встановіть `RTP h264` на порту 5600 як вихідне джерело відео
2. Використовуйте налаштування за замовчуванням (udp на порту 14550) як джерело mavlink

## Налаштування радіо

З настройками WFB за замовчуванням використовуйте радіоканал 165 (5825 МГц), ширину 20 МГц, MCS #1 (QPSK 1/2) з довгим GI.
Це забезпечує приблизно 7 мбіт/с **ефективної** швидкості (тобто використовуваної швидкості після FEC та кодування пакетів) в **обох напрямках** разом, оскільки WiFi є напівдуплексом.
Таким чином, він підходить для потокового відео 720p@49fps (4 мбіт/с) + два потоки телеметрії з повною швидкістю (вгору та вниз).
Якщо вам потрібна вища пропускна здатність, ви можете використовувати індекси MCS (наприклад, 2 або більше)

## Антени та інше

У простих випадках ви можете використовувати всенапрямлені антени з лінійною (які комплектуються з WiFi картами) або круговою листковою ([кругово-поляризована антена Coverleaf](http://www.antenna-theory.com/antennas/cloverleaf.php)) поляризацією.
Якщо ви хочете налаштувати зв'язок на велику відстань, ви можете використовувати кілька WiFi адаптерів з напрямними та всенапрямленими антенами. TX/RX Підтримується різноманіття передачі/прийому для кількох адаптерів з коробки (просто додайте кілька мережевих інтерфейсів до `/etc/default/wifibroadcast`).
Якщо ваш WiFi адаптер має дві антени (наприклад, Alfa AWU036ACH), різноманіття передачі втілено через [STBC](https://en.wikipedia.org/wiki/Space%E2%80%93time_block_code).
Карти з 4 портами (наприклад, Alfa AWUS1900) наразі не підтримуються.

## Часто Запитувані Питання

**З:** _Який тип даних можна передавати за допомогою wfb-ng?_

**A:** Будь-який UDP з розміром пакета <= 1445.
Наприклад, x264 в середині RTP або MAVLink.

**Q:** _Які гарантії передачі?_

**A:** Wifibroadcast використовує FEC (передню корекцію помилок).
Ви можете налаштувати його як TX так і RX одночасно!), щоб відповідати вашим потребам to fit your needs.

**Q** _Яка максимальна відстань, на яку я можу летіти і все ще зберігати зв'язок?_

**A**Це залежить від ваших антен та WiFi карт.
З Alfa AWU036ACH і направленою антеною 20dBi на землі можливий польот на відстань приблизно 20 км.

:::warning
Не використовуйте діапазон, на якому працює RC TX!
Або налаштуйте RTL належним чином, щоб уникнути втрати моделі.
:::

**Q:** _Чи підтримується лише Raspberry PI?_

**A:** WFB-ng не зв'язаний з жодним GPU - він працює з UDP-пакетами.
Але для отримання потоку RTP вам потрібен відеокодер (який кодує вихідні дані з камери у потік x264) або ви повинні використовувати камеру з апаратним відеокодеком, таку як Logitech C920 або камери спостереження з Ethernet.

#### Які ARM-плати рекомендуються для БПЛА?

- RPI3b/3b+/ZeroW.
  Існують готові образи, але вони підтримують лише відео h264 для камер CSI.
- Jetson Nano.
  Він підтримує h264 і h265, але вам потрібно налаштувати його самостійно за [допомогою Setup HOWTO.](https://github.com/svpcom/wfb-ng/wiki/Setup-HOWTO)

Ви можете використовувати будь-яку іншу ARM-плату з Linux, але вам потрібно використовувати камеру Ethernet або USB з вбудованими апаратними відеокодеками (наприклад, Logitech C920).

## Теорія

WFB-ng переводить WiFi-карти у режим монітору. Цей режим дозволяє надсилати та отримувати довільні пакети без асоціації та очікування підтвердження (ACK).
[Аналіз можливостей ін'єкції та доступу до медіа апаратного забезпечення IEEE 802.11 в режимі монітору](https://github.com/svpcom/wfb-ng/blob/master/doc/Analysis%20of%20Injection%20Capabilities%20and%20Media%20Access%20of%20IEEE%20802.11%20Hardware%20in%20Monitor%20Mode.pdf) [802.11 часів](https://github.com/ewa/802.11-data)
