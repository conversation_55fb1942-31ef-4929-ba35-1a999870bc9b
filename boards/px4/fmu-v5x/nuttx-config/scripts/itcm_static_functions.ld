/* Static */
*(.text.hrt_absolute_time)
*(.text.arm_ack_irq)
*(.text.arm_doirq)
*(.text.arm_svcall)
*(.text.arm_switchcontext)
*(.text.clock_timer)
*(.text.exception_common)
*(.text.hrt_call_enter)
*(.text.hrt_tim_isr)
*(.text.stm32_configwaitints)
*(.text.stm32_dmainterrupt)
*(.text.stm32_dmaresidual)
*(.text.stm32_dmasetup)
*(.text.stm32_dmastart)
*(.text.stm32_endwait)
*(.text.stm32_endtransfer)
*(.text.stm32_i2c_isr)
*(.text.stm32_i2c_transfer)
*(.text.stm32_interrupt)
*(.text.stm32_interrupt_work)
*(.text.stm32_ioctl)
*(.text.stm32_receive)
*(.text.stm32_sdmmc_interrupt)
*(.text.stm32_txpoll)
*(.text.stm32_usbinterrupt)
*(.text.irq_dispatch)
*(.text.ioctl)
*(.text.memcpy)
*(.text.memset)
*(.text.nxsched_add_blocked)
*(.text.nxsched_add_prioritized)
*(.text.nxsched_add_readytorun)
*(.text.nxsched_get_files)
*(.text.nxsched_get_tcb)
*(.text.nxsched_merge_pending)
*(.text.nxsched_process_timer)
*(.text.nxsched_remove_blocked)
*(.text.nxsched_remove_readytorun)
*(.text.nxsched_resume_scheduler)
*(.text.nxsched_suspend_scheduler)
*(.text.nxsem_add_holder)
*(.text.nxsem_add_holder_tcb)
*(.text.nxsem_clockwait)
*(.text.nxsem_foreachholder)
*(.text.nxsem_freecount0holder)
*(.text.nxsem_freeholder)
*(.text.nxsem_post)
*(.text.nxsem_release_holder)
*(.text.nxsem_restore_baseprio)
*(.text.nxsem_tickwait)
*(.text.nxsem_timeout)
*(.text.nxsem_trywait)
*(.text.nxsem_wait)
*(.text.nxsem_wait_uninterruptible)
*(.text.nxsig_timedwait)
*(.text.perf_set_elapsed)
*(.text.sched_lock)
*(.text.sched_note_resume)
*(.text.sched_note_suspend)
*(.text.sched_unlock)
*(.text.spi_exchange)
*(.text.spi_exchange_nodma)
*(.text.spi_send)
*(.text.strcmp)
*(.text.sq_addafter)
*(.text.sq_addlast)
*(.text.sq_rem)
*(.text.sq_remafter)
*(.text.sq_remfirst)
*(.text.uart_connected)
*(.text.up_block_task)
*(.text.up_dma_receive)
*(.text.up_dma_send)
*(.text.up_dma_rxcallback)
*(.text.up_dma_txcallback)
*(.text.up_rxint)
*(.text.up_txint)
*(.text.up_unblock_task)
*(.text.wd_timer)
*(.text.wd_start)
*(.text._do_memcpy)
