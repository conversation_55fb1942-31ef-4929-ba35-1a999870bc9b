/* Static */
*(.text.arm_ack_irq)
*(.text.arm_doirq)
*(.text.arm_svcall)
*(.text.arm_switchcontext)
*(.text.clock_timer)
*(.text.exception_common)
*(.text.flexio_irq_handler)
*(.text.hrt_absolute_time)
*(.text.hrt_call_enter)
*(.text.hrt_tim_isr)
*(.text.imxrt_configwaitints)
*(.text.imxrt_dma_callback)
*(.text.imxrt_dmach_interrupt)
*(.text.imxrt_dmach_xfrsetup)
*(.text.imxrt_dmaterminate)
*(.text.imxrt_dispatch)
*(.text.imxrt_edma_interrupt)
*(.text.imxrt_endwait)
*(.text.imxrt_enet_interrupt)
*(.text.imxrt_enet_interrupt_work)
*(.text.imxrt_interrupt)
*(.text.imxrt_lpi2c_isr)
*(.text.imxrt_lpspi_exchange)
*(.text.imxrt_recvdma)
*(.text.imxrt_tcd_free)
*(.text.imxrt_timerisr)
*(.text.imxrt_transmit)
*(.text.imxrt_txdone)
*(.text.imxrt_txtimeout_work)
*(.text.imxrt_txtimeout_expiry)
*(.text.imxrt_txpoll)
*(.text.imxrt_txringfull)
*(.text.imxrt_txavail_work)
*(.text.imxrt_txavail)
*(.text.imxrt_usbinterrupt)
*(.text.irq_dispatch)
*(.text.ioctl)
*(.text.memcpy)
*(.text.memset)
*(.text.nxsched_add_blocked)
*(.text.nxsched_add_prioritized)
*(.text.nxsched_add_readytorun)
*(.text.nxsched_get_files)
*(.text.nxsched_get_tcb)
*(.text.nxsched_merge_pending)
*(.text.nxsched_process_timer)
*(.text.nxsched_remove_blocked)
*(.text.nxsched_remove_readytorun)
*(.text.nxsched_resume_scheduler)
*(.text.nxsched_suspend_scheduler)
*(.text.nxsem_add_holder)
*(.text.nxsem_add_holder_tcb)
*(.text.nxsem_clockwait)
*(.text.nxsem_foreachholder)
*(.text.nxsem_freecount0holder)
*(.text.nxsem_freeholder)
*(.text.nxsem_post)
*(.text.nxsem_release_holder)
*(.text.nxsem_restore_baseprio)
*(.text.nxsem_tickwait)
*(.text.nxsem_timeout)
*(.text.nxsem_trywait)
*(.text.nxsem_wait)
*(.text.nxsem_wait_uninterruptible)
*(.text.nxsig_timedwait)
*(.text.sched_lock)
*(.text.sched_note_resume)
*(.text.sched_note_suspend)
*(.text.sched_unlock)
*(.text.strcmp)
*(.text.sq_addafter)
*(.text.sq_addlast)
*(.text.sq_rem)
*(.text.sq_remafter)
*(.text.sq_remfirst)
*(.text.uart_connected)
*(.text.up_block_task)
*(.text.up_unblock_task)
*(.text.wd_timer)
*(.text.wd_start)
*(.text.work_thread)
*(.text.work_queue)
*(.text._do_memcpy)

/* Tropic Eth tune */
*(.text.devif_poll)
*(.text.devif_poll_tcp_connections)
*(.text.tcp_poll)
*(.text.devif_poll_udp_connections)
*(.text.udp_nextconn)
*(.text.udp_poll)
*(.text.udp_ipv4_select)
*(.text.udp_callback)
*(.text.udp_datahandler)
*(.text.udp_send)
*(.text.udp_active)
*(.text.udp_ipv4_active)
*(.text.psock_udp_sendto)
*(.text.sendto_eventhandler)
*(.text.net_dataevent)
*(.text.devif_conn_event)
*(.text.devif_event_trigger)
*(.text.devif_poll_icmp)
*(.text.icmp_poll)
*(.text.arp_out)
*(.text.arp_find)
*(.text.arp_format)
*(.text.net_ipv4addr_hdrcmp) /* itcm-check-ignore */
*(.text.net_ipv4addr_copy) /* itcm-check-ignore */
*(.text.net_ipv4addr_broadcast) /* itcm-check-ignore */
*(.text.wd_start)
*(.text.arp_arpin)
*(.text.ipv4_input)
*(.text.work_thread)
*(.text.work_queue)

/* Flash Storage */
*(.text.imxrt_flexspi_transfer_blocking)
*(.text.imxrt_flexspi_transfer_blocking_private)
*(.text.imxrt_flexspi_write_blocking)
*(.text.imxrt_flexspi_read_blocking)
*(.text.imxrt_flexspi_check_and_clear_error)
*(.text.imxrt_flexspi_get_bus_idle_status)
*(.text.imxrt_flexspi_configure_prefetch)
*(.text.imxrt_flexspi_configure_prefetch_private)
*(.text.imxrt_flexspi_storage_write_enable)
*(.text.imxrt_flexspi_storage_wait_bus_busy)
*(.text.imxrt_flexspi_storage_read_status)
*(.text.imxrt_flexspi_storage_erase)
*(.text.imxrt_flexspi_storage_bwrite)
*(.text.imxrt_flexspi_storage_page_program)
