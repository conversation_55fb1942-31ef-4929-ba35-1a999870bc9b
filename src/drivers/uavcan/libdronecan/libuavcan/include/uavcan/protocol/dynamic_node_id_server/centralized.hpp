/*
 * Copyright (C) 2015 <PERSON> <<EMAIL>>
 */

#ifndef UAVCAN_PROTOCOL_DYNAMIC_NODE_ID_SERVER_CENTRALIZED_HPP_INCLUDED
#define UAVCAN_PROTOCOL_DYNAMIC_NODE_ID_SERVER_CENTRALIZED_HPP_INCLUDED

#include <uavcan/protocol/dynamic_node_id_server/centralized/server.hpp>

namespace uavcan
{
namespace dynamic_node_id_server
{

typedef centralized::Server CentralizedServer;

}
}

#endif // UAVCAN_PROTOCOL_DYNAMIC_NODE_ID_SERVER_CENTRALIZED_HPP_INCLUDED
