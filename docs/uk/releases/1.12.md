# Реліз 1.12

- [Release 1.12](#release-1-12)
  - [Pre Releases](#pre-releases)
  - [Changes](#changes)
    - [Common](#common)
    - [Sensors](#sensors)
    - [Hardware](#hardware)
    - [MAVLink](#mavlink)
    - [Commander](#commander)
    - [Multicopter](#multicopter)
    - [VTOL](#vtol)
    - [Control](#control)
    - [GPS](#gps)
    - [NuttX](#nuttx)
    - [UAVCAN](#uavcan)

## Попередній реліз

- [Beta 4](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta4)
- [Beta 3](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta3)
- [Beta 2](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta2)
- [Beta 1](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta1)

## Зміни

### Загальні

- **RTL Trigger based on remaining flight range ([PR#16399](https://github.com/PX4/PX4-Autopilot/pull/16399))**
  - Обчислює час до дому, у режимі RTL, враховуючи швидкість руху автомобіля, швидкість вітру та відстань/напрямок до пункту призначення
- **Pre-emptive geofence breach ([PR#16400](https://github.com/PX4/PX4-Autopilot/pull/16400))**
  - Triggers a breach if the _predicted_ current trajectory will result in a breach, allowing the vehicle to be re-routed to a safe hold position.
- **Airframe Scripts**
  - Синтаксис для встановлення значень за замовчуванням було змінено, і власні сценарії потребують оновлення
  - See [PR#16796](https://github.com/PX4/PX4-Autopilot/pull/16796/files#diff-dcf2f5536f47f260e5e0ff3b3fd22eaef6b6c510126463d70affa0eb7bd4d3ddL20) for an example.
- Безпека (перемикач) за замовчуванням вимкнена (двигуни розброньовані, але сервоприводи / закрилки можуть рухатися)
- Безпековий перемикач є защіпним: коли він вимкнутий, він залишиться вимкнутим
- Засіб виявлення землі: Розширити виявлення землі для використання відстані до ґрунту, якщо датчик відстані присутній
- Додана підтримка для IRC Ghost включаючи телеметрію

### Датчики

- Калібрування магнітомера швидше і надійніше
  - нові коефіцієнти калібрування для м'якого заліза
  - автоматично визначати обертання зовнішніх датчиків
- Оптимізований датчик керування швидкістю потоку даних (мінімальна затримка від кінця до кінця внутрішньої петлі)

### Апаратне забезпечення(Hardware)

Випуск включає новий апаратний засіб для наступних плат, периферійних пристроїв та аксесуарів:

- Pixhawk FMUv6U (Read more about this spec on the [Pixhawk GitHub Repository](https://github.com/pixhawk/Pixhawk-Standards))
- Pixhawk FMUv6X (Read more about this spec on the [Pixhawk GitHub Repository](https://github.com/pixhawk/Pixhawk-Standards))
- CUAV X7 / X7Pro ([Read more about this product on the manufacturers site](http://www.cuav.net/en/x7en/)]
- CUAV Nora ([Read more about this product on the manufacturers site](http://www.cuav.net/en/nora/))
- CUAV CAN GPS ([Read more about this product on the manufacturers site](http://www.cuav.net/en/neo-3-2/))
- SP Racing H7 Extreme ([Read more about this product on the manufacturers site](http://seriouslypro.com/spracingh7extreme))
- Bitcraze Crazyflie v2.1 ([Read more about this product on the manufacturers site](https://www.bitcraze.io/products/crazyflie-2-1/))
- ARK CAN Flow ([Read more about this product on the manufacturers site](https://arkelectron.com/product/ark-flow/))
- mRo Ctrl Zero H7 (Experimental) ([Read more about this product on the manufacturers site](https://store.mrobotics.io/mRo-Control-Zero-F7-p/mro-ctrl-zero-f7.htm))

Вони видалені:

- Вилучення з припиненням виробництва Intel AeroFC

### MAVLink

- **MAVLink Ethernet configuration ([PR#14460](https://github.com/PX4/PX4-Autopilot/pull/14460))**
  - Налаштування каналу Ethernet MAVLink, такі як UDP-порт, віддалений порт та режим мовлення тепер можуть бути змінені динамічно за допомогою параметрів.
- **Support for querying `COMPONENT_INFORMATION` ([PR#16039](https://github.com/PX4/PX4-Autopilot/pull/16039))**
  - Метадані параметра тепер автоматично синхронізуються в поєднанні з щоденним QGC.
  - Це нове повідомлення дозволяє будь-якій системі MAVLink запитувати різноманітну ієрархічну інформацію від автопілота, тобто розуміти, які команди підтримуються в місіях або отримувати метадані параметрів. This message was introduced primarily to help GCS better understand autopilots (RFC: [mavlink#1339](https://github.com/mavlink/mavlink/issues/1339))

### Командер

- **Commander: use control mode flags and cleanup arm/disarm ([PR#16266](https://github.com/PX4/PX4-Autopilot/pull/16266))**
  - Consolidate scattered arming requirements in arm_disarm(), and, keeps the `vehicle_control_mode` last state in commander
- **Commander: Separate out manual control setpoint processing ([PR#16878](https://github.com/PX4/PX4-Autopilot/pull/16878))**
  - Adds a new class `ManualControl` for handling `manual_control_setpoint` and handles RC loss, RC override, and RC arming/disarming

### Мультикоптер

- **More intuitive stick feel in Position mode**

  - Горизонтальний вхід палиці відображений на точках прискорення замість встановлених точок швидкості
  - Видаляє неочікувані зміни нахилу при досягненні швидкості руху
  - Інтуїтивне переміщення, наприклад, при посадці
  - Opt out possible using [MPC_POS_MODE](../advanced_config/parameter_reference.md#MPC_POS_MODE)
  - Development: [First attempt](https://github.com/PX4/PX4-Autopilot/pull/12072), [Introduction](https://github.com/PX4/PX4-Autopilot/pull/16052), [Improvements](https://github.com/PX4/PX4-Autopilot/pull/16320), [Bugfix zero oscillation](https://github.com/PX4/PX4-Autopilot/pull/16786), [Bugfix position unlock](https://github.com/PX4/PX4-Autopilot/pull/16791), [Bugfix invalid setpoint](https://github.com/PX4/PX4-Autopilot/pull/17078), [Bugfix high velocity pre takeoff](https://github.com/PX4/PX4-Autopilot/pull/17437)

- **Hover thrust independent velocity control gains**

  - Parameters `MPC_{XY/Z}_VEL_{P/I/D}` were replaced with `MPC_{XY/Z}_VEL_{P/I/D}_ACC`, see:
    [MPC_XY_VEL_P_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_P_ACC), [MPC_XY_VEL_I_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_I_ACC), [MPC_XY_VEL_D_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_D_ACC), [MPC_Z_VEL_P_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_P_ACC), [MPC_Z_VEL_I_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_I_ACC), [MPC_Z_VEL_D_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_D_ACC)

:::warning
Прибутки отримали нове значення

    - Шкала відхилення від швидкості у $м/с$ до виходу прискорення у $м/с^2$
    - Існуючі виграші потрібно приблизно перескалювати множником: $гравітаційна \_ стала / тяга \_ струму$
    - Automatic parameter transition assumes 50% hover thrust: `~10m/s^2 / 50% = 20 m/s^2`.
      See [question](https://github.com/PX4/PX4-Autopilot/pull/14823#issuecomment-791357646)

:::

  - Development: [Logic introduction](https://github.com/PX4/PX4-Autopilot/pull/14749), [Parameter replacement](https://github.com/PX4/PX4-Autopilot/pull/14823)

- **Improve Rounded Turns ([PR#16376](https://github.com/PX4/PX4-Autopilot/pull/16376))**
  - Створює більш округлений поворот при точках маршруту в місіях мультиротора (використовуючи логіку навігації у стилі L1 на кутах)
  - See [Mission Mode > Inter-waypoint Trajectory](../flight_modes_fw/mission.md#rounded-turns-inter-waypoint-trajectory) and [Mission > Setting Acceptance/Turning Radius](../flying/missions.md#setting-acceptance-turning-radius)

- **Removal of Rattitude flight mode ([PR#17019](https://github.com/PX4/PX4-Autopilot/pull/17019))**
  - Сповістіть нас, якщо ви хочете повернути його.

### VTOL

- **RTL improvements ([PR#16377](https://github.com/PX4/PX4-Autopilot/pull/16377))**
  - Підвищує надійність RTL безпекових заходів, враховуючи багато крайніх випадків при спробі посадки, залежно від поточного режиму транспортного засобу (Багатокоптерний проти Фіксований крило)
- Значні вдосконалення ТЕКС фіксованого крила / VTOL

### Управління

- **Dynamic Notch Filter updated with Gyro FFT ([PR#16385](https://github.com/PX4/PX4-Autopilot/pull/16385))**
  - Додає динамічне фільтрування виѐмок до даних керування гіроскопом, що призводить до набагато плавнішого керування
- **Multi-EKF enabled by default** on stm32f7 and stm32h7 boards

### GPS

- The GPS protocol now defaults to u-blox for faster startup, and [GPS_x_PROTOCOL](../advanced_config/parameter_reference.md#GPS_1_PROTOCOL) needs to be changed if another GPS is used.

### NuttX

Nuttx was upgraded from [8.2+ to NuttX 10.10.0+](https://github.com/apache/incubator-nuttx/compare/nuttx-8.2..nuttx-10.0.1) (@ [904a602c74dc08a100b5c2bd490807de19e73e10](https://github.com/apache/incubator-nuttx/commit/904a602c74dc08a100b5c2bd490807de19e73e10))

- **SDCARD performance:** Results in better performance on H7 Targets
  - [**BACKPORT**] stm32:SDIO:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] stm32h7:SDMMC:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] stm32f7:SDMMC:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] Fixes race condition in event wait logic of SDMMC driver.
  - [**BACKPORT**] mmcsd:Stuck in 1-bit mode, Removed CONFIG_ARCH_HAVE_SDIO_DELAYED_INVLDT

- **Ethernet stability:**
  - [**BACKPORT**] stm32x7:Ethernet Fixed hardfaults, from too big frames
  - [**BACKPORT**] stm32:Ethernet Fix too big frames

- **Boot up stability** V5-V6X ensuring the LSE (RTC) oscillator is started

  - [**BACKPORT**] stm32h7:lse fix Kconfig help text
  - [**BACKPORT**] stm32f7:lse Use Kconfig values directly
  - [**BACKPORT**] stm32h7:Add DBGMCU
  - [**BACKPORT**] stm32f7:Add option to auto select LSE CAPABILITY
  - [**BACKPORT**] stm32h7:Add option to auto select LSE CAPABILITY

    ::: info
    This Knob will cycle through the correct\*
    values from low to high. To avoid damaging
    the crystal. We want to use the lowest setting
    that gets the OSC running. See app note AN2867
    \*It will take into account the rev of the silicon
    and use the correct code points to achieve the drive
    strength. See Eratta ES0392 Rev 7 2.2.14 LSE oscillator
    driving capability selection bits are swapped.

:::

- **Driver changes**

  - [**BACKPORT**] drivers/serial: fix Rx interrupt enable for cdcacm

  - [**BACKPORT**] binnfmt:Fix return before close ELF fd

  - [**BACKPORT**] stm32f7:Allow for reuse of the OTG_ID GPIO

  - [**BACKPORT**] stm32f7:SDMMC fix reset of do_gpio

  - [**BACKPORT**] stm32h7: serial: use dma tx semaphore as resource holder

  - [**BACKPORT**] stm32h7:SDMMC fix reset of do_gpio

  - [**BACKPORT**] stm32h7:Serial Add RX and TX DMA

  - [**BACKPORT**] stm32h7:Allow for reuse of the OTG_ID GPIO

  - [**BACKPORT**] Kinetis:kinetis:Replace DMA

  - [**BACKPORT**] kinetis:Serial use eDMA

  - [**BACKPORT**] kinetis:SPI use eDMA

  - [**BACKPORT**] Kinetis:Serail No DMA Poll needed

  - [**BACKPORT**] libc/stdio: Preallocate the stdin, stdout and stderr
    For targets without consoles.

- **FlexCan fixes**
  - [**BACKPORT**][flexcan] Correct reset state for CTRL1 register
  - [**BACKPORT**][flexcan] Fix TX drop #2792 and correctly set CAN timings to non-zeroed registers
  - [**BACKPORT**] FlexCAN Fix TX abort process

- **Support for CAN bootloader**
  - [**BACKPORT**] s32k1xx:Support ramfunc

- **STM32F412 cleanup**

- [**BACKPORT**] stm32f412:Corrected Pin count

- [**BACKPORT**] stm32f412:Replaced Kludged pinmap with one for SoC

- [**BACKPORT**] stm32412: Fixes pinmap CAN1

- **Security patches**

- [**BACKPORT**] tcp: Remove incomplete support for TCP reassembly

- [**BACKPORT**] net/tcp/tcp_input.c: Correct bad check of urgent data length

- [**BACKPORT**] libc: Add additional checks to malloc realloc and memalign

- **IMXRT fixes**

- Додавання Single wire та правильних налаштувань парності до IMXRT для підтримки sbus та іншого.

- [**BACKPORT**] imxrt:serial support single-wire mode

- [**BACKPORT**] imxrt:imxrt_lowputc Fixed parity settings.

- **STM32H7 improvements**

- [**BACKPORT**] stm32h7:SPI Fix 16 bit SPI mode

- [**BACKPORT**] stm32h7:DMA BDMA does not auto disabled on completion

- [**BACKPORT**] Fix HEAP clobbering static data in SRAM4

- [**BACKPORT**] stm32h7:SDMMC fix reset of do_gpio

### UAVCAN

- UAVCANv0: Хоча базові функції, такі як оновлення прошивки та синхронізація параметрів вузлів CAN, були впроваджені більше 5 років тому, ми оновили підтримку, тепер, нарешті, пристрої є на ринку. Типові модулі CAN GPS, швидкості повітря та потужності підтримуються
- UAVCANv0 Вузол: PX4 підтримував будівництво вузлів протягом багатьох років - тепер ми підтримуємо будівництво конкретних цілей, таких як одиниці GPS CUAV
- UAVCANv1: Початковий альфа-варіант повної реалізації від кінця до кінця
