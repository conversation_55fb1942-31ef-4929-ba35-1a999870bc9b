<?xml version="1.0" ?>
<!-- Copyright (c) 2016 The UUV Simulator Authors.
     All rights reserved.
     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at
       http://www.apache.org/licenses/LICENSE-2.0
     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<sdf version="1.4">
  <world name="oceans">
    <scene>
      <ambient>0.01 0.01 0.01 1.0</ambient>
      <sky>
        <clouds>
          <speed>12</speed>
        </clouds>
      </sky>
      <shadows>1</shadows>
    </scene>

    <!-- Origin placed somewhere in the middle of the North Sea  -->
    <spherical_coordinates>
      <latitude_deg>56.71897669633431</latitude_deg>
      <longitude_deg>3.515625</longitude_deg>
    </spherical_coordinates>

    <!-- Global light source -->
    <light type="directional" name="sun1">
      <pose>50 0 150 0 0 0</pose>
      <diffuse>1 1 1 1</diffuse>
      <specular>.1 .1 .1 1</specular>
      <direction>0.3 0.3 -1</direction>
      <cast_shadows>false</cast_shadows>
    </light>

    <!-- Global light source -->
    <light type="directional" name="sun_diffuse">
      <pose>-50 0 -150 0 0 0</pose>
      <diffuse>0.6 0.6 0.6 1</diffuse>
      <specular>0 0 0 1</specular>
      <direction>-0.3 -0.3 -1</direction>
      <cast_shadows>false</cast_shadows>
    </light>

    <!-- Bounding box with sea surface -->
    <include>
      <uri>model://ocean</uri>
      <pose>0 0 0 0 0 0</pose>
    </include>
    <physics name='default_physics' default='0' type='ode'>
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>10</iters>
          <sor>1.3</sor>
          <use_dynamic_moi_rescaling>0</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
      <magnetic_field>6.0e-6 2.3e-5 -4.2e-5</magnetic_field>
    </physics>
  </world>
</sdf>
