*(.text._ZN4uORB7Manager27orb_add_internal_subscriberE6ORB_IDhPj)
*(.text._ZN4uORB7Manager13orb_data_copyEPvS1_Rjb)
*(.text._ZN7Mavlink16update_rate_multEv)
*(.text._ZN4uORB12DeviceMaster19getDeviceNodeLockedEPK12orb_metadatah)
*(.text._ZN3Ekf20controlGravityFusionERKN9estimator9imuSampleE)
*(.text._ZN7sensors22VehicleAngularVelocity21FilterAngularVelocityEiPfi)
*(.text._ZN39ControlAllocationSequentialDesaturation23computeDesaturationGainERKN6matrix6VectorIfLj16EEES4_)
*(.text._Z32param_get_default_value_internaltPv)
*(.text._ZL19param_get_cplusplustPf.isra.0)
*(.text.param_get_index)
*(.text.param_get_system_default_value)
*(.text.param_get)
*(.text.param_get_default_value)
*(.text._Z12get_orb_meta6ORB_ID)
*(.text._ZN3Ekf17measurementUpdateERN6matrix6VectorIfLj24EEERKS2_ff)
