#! /usr/bin/env python3
"""
Class to generate Markdown documentation pages from parsed module doc strings
"""

from xml.sax.saxutils import escape
import codecs
import os

class MarkdownOutput():
    def __init__(self, module_groups):

        self._outputs = {}

        result = """
# Modules & Commands Reference

The following pages document the PX4 modules, drivers and commands.
They describe the provided functionality, high-level implementation overview and how
to use the command-line interface.

::: info
**This is auto-generated from the source code** and contains the most recent modules documentation.
:::

It is not a complete list and NuttX provides some additional commands
as well (such as `free`). Use `help` on the console to get a list of all
available commands, and in most cases `command help` will print the usage.

Since this is generated from source, errors must be reported/fixed
in the [PX4-Autopilot](https://github.com/PX4/PX4-Autopilot) repository.
The documentation pages can be generated by running the following command from
the root of the PX4-Autopilot directory:

```
make module_documentation
```
The generated files will be written to the `modules` directory.

## Categories
"""
        for category in sorted(module_groups):
            result += f"- [{category.capitalize()}](modules_{category}.md)\n"

        self._outputs['main'] = result

        for category in sorted(module_groups):
            result = f"# Modules Reference: {category.capitalize()}\n\n"
            subcategories = module_groups[category]

            if len(subcategories) > 1:
                result += 'Subcategories:\n\n'
                for subcategory in sorted(subcategories): 
                    if subcategory == '':
                        continue
                    subcategory_label = subcategory.replace('_', ' ').title()
                    subcategory_file_name = category+'_'+subcategory
                    result += f'- [{subcategory_label}](modules_{subcategory_file_name}.md)\n'

                    # add a sub-page for the subcategory
                    result_subpage = f'# Modules Reference: {subcategory_label} ({category.capitalize()})\n'
                    result_subpage += self._ProcessModules(subcategories[subcategory])
                    self._outputs[subcategory_file_name] = result_subpage

            result += '\n' + self._ProcessModules(subcategories[''])
            self._outputs[category] = result

    def _ProcessModules(self, module_list):
        result = ''
        for module in module_list:
            result += f"\n## {module.name()}\n\n"
            result += f"Source: [{module.scope()}](https://github.com/PX4/PX4-Autopilot/tree/main/src/{module.scope()})\n\n"
            doc = module.documentation()
            if len(doc) > 0:
                result += f"{doc}\n"
            usage_string = module.usage_string()
            if len(usage_string) > 0:
                result += f'### Usage {{#{module.name()}_usage}}\n\n```\n{usage_string}\n```\n'
        return result

    def Save(self, dirname):
        for output_name in self._outputs:
            output = self._outputs[output_name]
            with codecs.open(os.path.join(dirname, 'modules_'+output_name+'.md'), 'w', 'utf-8') as f:
                f.write(output)
