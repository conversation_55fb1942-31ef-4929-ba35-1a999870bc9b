# 플랫폼 테스트 및 지속적인 통합

PX4 is extensively tested using unit and integration tests run via continuous integration.
Live flight testing is also performed by the development team and the broader community.

Test topics include:

- [Test Flights](../test_and_ci/test_flights.md) - How to make test flights (e.g. to [test PRs](../contribute/code.md#pull-requests))
- [Unit Tests](../test_and_ci/unit_tests.md)
- [Continuous Integration (CI)](../test_and_ci/continous_integration.md)
- [Integration Testing](../test_and_ci/integration_testing.md)
    - [MAVSDK Integration Testing](../test_and_ci/integration_testing_mavsdk.md)
    - [PX4 ROS2 Interface Library Integration Testing](../test_and_ci/integration_testing_px4_ros2_interface.md)
    - [ROS 1 Integration Testing](../test_and_ci/integration_testing_ros1_mavros.md) (Deprecated)
- [Docker](../test_and_ci/docker.md)
- [Maintenance](../test_and_ci/maintenance.md)
