<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1232px" height="1960px" viewBox="-0.5 -0.5 1232 1960" content="&lt;mxfile modified=&quot;2023-11-29T03:50:33.945Z&quot; host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36&quot; etag=&quot;oKCpdc7CmozJjSXrntj-&quot; version=&quot;22.1.4&quot; type=&quot;google&quot; pages=&quot;2&quot;&gt;&lt;diagram id=&quot;12e1b939-464a-85fe-373e-61e167be1490&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;1d30bf8a-9295-1215-42b3-bd1b40e413e7&quot; name=&quot;Backup&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="790" width="1230" height="1170" fill="#e6e6e6" stroke="none" pointer-events="all"/><rect x="250" y="0" width="710" height="223" fill="#e6e6e6" stroke="none" pointer-events="all"/><rect x="253" y="4" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 19px; margin-left: 254px;"><div data-drawio-colors="color: #919191; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(145, 145, 145); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Storage</div></div></div></foreignObject><text x="303" y="26" fill="#919191" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Storage</text></switch></g><rect x="0" y="291" width="250" height="449" fill="#e6e6e6" stroke="none" pointer-events="all"/><rect x="10" y="294" width="133" height="59" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 131px; height: 1px; padding-top: 324px; margin-left: 11px;"><div data-drawio-colors="color: #919191; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(145, 145, 145); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;"><div align="left">External <br /></div><div align="left">Connectivity</div></div></div></div></foreignObject><text x="77" y="330" fill="#919191" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">External...</text></switch></g><rect x="712" y="290" width="520" height="450" fill="#e6e6e6" stroke="none" pointer-events="all"/><rect x="716" y="294" width="88" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 86px; height: 1px; padding-top: 309px; margin-left: 717px;"><div data-drawio-colors="color: #919191; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(145, 145, 145); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Drivers</div></div></div></foreignObject><text x="760" y="316" fill="#919191" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Drivers</text></switch></g><rect x="3" y="794" width="150" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 809px; margin-left: 4px;"><div data-drawio-colors="color: #919191; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(145, 145, 145); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Flight Control</div></div></div></foreignObject><text x="78" y="816" fill="#919191" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Flight Control</text></switch></g><rect x="472" y="850" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="723" y="38.83" width="230" height="162" rx="24.3" ry="24.3" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="410" y="455" width="220" height="100" rx="15" ry="15" fill="#417a6b" stroke="none" pointer-events="all"/><rect x="411" y="450" width="219" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 217px; height: 1px; padding-top: 480px; margin-left: 412px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Message Bus</div></div></div></foreignObject><text x="521" y="487" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Message Bus</text></switch></g><path d="M 460.2 454.8 L 460.2 445 L 459.8 405.2" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 448.8 405.31 L 460.02 427.2 L 470.8 405.09" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 580.2 454.8 L 579.8 405.2" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 568.8 405.29 L 579.98 427.2 L 590.8 405.11" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="9" y="363" width="233" height="164" rx="24.6" ry="24.6" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="10" y="360" width="230" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 390px; margin-left: 11px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">MAVLink</div></div></div></foreignObject><text x="125" y="397" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">MAVLink</text></switch></g><path d="M 24 415 L 226 415" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="10" y="420" width="230" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 440px; margin-left: 11px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">mavlink</div></div></div></foreignObject><text x="125" y="445" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">mavlink</text></switch></g><rect x="10" y="460" width="230" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 490px; margin-left: 11px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">via UART / UDP</div></div></div></foreignObject><text x="125" y="495" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">via UART / UDP</text></switch></g><rect x="727" y="33" width="220" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 63px; margin-left: 728px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Logging</div></div></div></foreignObject><text x="837" y="70" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Logging</text></switch></g><path d="M 737 88 L 939 88" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="729" y="93" width="218" height="40" fill="none" stroke="none" transform="rotate(90,838,113)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 113px; margin-left: 819px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">logger</div></div></div></foreignObject><text x="838" y="118" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">logg...</text></switch></g><rect x="727" y="133" width="220" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 163px; margin-left: 728px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">To SD Card or via MAVLink</div></div></div></foreignObject><text x="837" y="168" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">To SD Card or via MAVLink</text></switch></g><path d="M 488 126.14 C 497.6 130.75 514.87 134.81 534.29 136.89 C 568.45 140.6 604.92 140.5 637.55 137.24 C 657.76 135.23 675.37 131.55 687 126.14 L 687 160.44 C 685.48 169.91 671.7 174.9 656.8 178.23 C 623.04 185.13 565.06 186 523.9 179.38 C 506.8 176.25 489.47 171.02 488 160.41 Z M 488 74.29 C 497.6 78.94 514.87 82.96 534.29 85.08 C 568.45 88.76 604.92 88.69 637.55 85.43 C 657.76 83.38 675.37 79.74 687 74.33 L 687 108.59 C 685.48 118.06 671.7 123.05 656.8 126.42 C 623.04 133.29 565.06 134.15 523.9 127.56 C 506.8 124.44 489.47 119.2 488 108.59 Z M 488 60.59 C 488 51.54 518.88 42.08 585.61 41 C 651.83 41 687 51.37 687 60.56 C 687 70.72 644.38 80.02 592.04 80.02 C 538.36 80.47 488 72.21 488 60.59 Z" fill="#b3b2b2" stroke="none" pointer-events="all"/><rect x="489" y="37" width="198" height="42" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 196px; height: 1px; padding-top: 58px; margin-left: 490px;"><div data-drawio-colors="color: #5C5C5C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(92, 92, 92); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;"><font face="Helvetica" style="font-size: 22px;">Parameters<br /></font></div></div></div></foreignObject><text x="588" y="65" fill="#5C5C5C" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Parameters&#xa;</text></switch></g><path d="M 588 239.23 L 588 239.7 L 587.75 209.77" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 588 245.98 L 583.5 236.98 L 588 239.23 L 592.5 236.98 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 587.69 203.02 L 592.27 211.98 L 587.75 209.77 L 583.27 212.06 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 527.3 240.23 L 527.3 240.7 L 526.83 211.1" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 527.32 246.98 L 522.79 237.99 L 527.3 240.23 L 531.79 237.96 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 526.72 204.35 L 531.36 213.28 L 526.83 211.1 L 522.36 213.42 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 647.3 239.23 L 647.3 239.7 L 646.83 210.1" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 647.32 245.98 L 642.79 236.99 L 647.3 239.23 L 651.79 236.96 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 646.72 203.35 L 651.36 212.28 L 646.83 210.1 L 642.36 212.42 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 837 250.67 L 837 240.7 L 837 210.77" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 837 204.02 L 841.5 213.02 L 837 210.77 L 832.5 213.02 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 777 251 L 777 241 L 776.75 210.77" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 776.69 204.02 L 781.27 212.98 L 776.75 210.77 L 772.27 213.06 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 897 251 L 897 240.7 L 896.75 210.77" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 896.69 204.02 L 901.27 212.98 L 896.75 210.77 L 892.27 213.06 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="478" y="840" width="222" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 220px; height: 1px; padding-top: 870px; margin-left: 479px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Autonomous Flight</div></div></div></foreignObject><text x="589" y="877" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Autonomous Flight</text></switch></g><path d="M 487 895 L 689 895" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="476" y="900" width="224" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 222px; height: 1px; padding-top: 920px; margin-left: 477px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">navigator</div></div></div></foreignObject><text x="588" y="925" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">navigator</text></switch></g><path d="M 339 200 L 340 200 L 340 760 L 589 760 L 589 839.92" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 589 846.67 L 584.5 837.67 L 589 839.92 L 593.5 837.67 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="10" y="542" width="230" height="162" rx="24.3" ry="24.3" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="10" y="538" width="230" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 568px; margin-left: 11px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">uXRCE-DDS</div></div></div></foreignObject><text x="125" y="575" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">uXRCE-DDS</text></switch></g><path d="M 23 593 L 225 593" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="10" y="599" width="230" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 619px; margin-left: 11px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">uxrce_dds_client</div></div></div></foreignObject><text x="125" y="624" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">uxrce_dds_client</text></switch></g><rect x="10" y="638" width="230" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 668px; margin-left: 11px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">via UART / UDP</div></div></div></foreignObject><text x="125" y="673" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">via UART / UDP</text></switch></g><rect x="476" y="1540" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="484" y="1630" width="218" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 1660px; margin-left: 485px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Mixing</div></div></div></foreignObject><text x="593" y="1665" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">Mixing</text></switch></g><rect x="720" y="340" width="233" height="95" rx="14.25" ry="14.25" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="721" y="330" width="230" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 360px; margin-left: 722px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Camera Control</div></div></div></foreignObject><text x="836" y="367" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Camera Control</text></switch></g><path d="M 735 385 L 937 385" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="991" y="340" width="233" height="95" rx="14.25" ry="14.25" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="996" y="330" width="224" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 222px; height: 1px; padding-top: 360px; margin-left: 997px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Gimbal / Mount</div></div></div></foreignObject><text x="1108" y="367" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Gimbal / Mount</text></switch></g><path d="M 1006 385 L 1208 385" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 982 979.92 L 982 920 L 1107.3 920 L 1107.34 723.65" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 982 986.67 L 977.5 977.67 L 982 979.92 L 986.5 977.67 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 950px; margin-left: 992px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;">Raw IMU data,<br />Airspeed</div></div></div></foreignObject><text x="992" y="955" fill="#7F7F7F" font-family="monospace" font-size="18px">Raw IMU data,...</text></switch></g><path d="M 717.34 1370.82 L 790 1370.8 L 790 920 L 990 920" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 710.59 1370.82 L 719.59 1366.32 L 717.34 1370.82 L 719.59 1375.32 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1390px; margin-left: 722px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;">Gyro data</div></div></div></foreignObject><text x="722" y="1395" fill="#7F7F7F" font-family="monospace" font-size="18px">Gyro data</text></switch></g><path d="M 589.11 1062.92 L 589.1 1033 L 589 1000" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 589.12 1069.67 L 584.61 1060.67 L 589.11 1062.92 L 593.61 1060.66 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1033px; margin-left: 601px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;">Position<br />Setpoints</div></div></div></foreignObject><text x="601" y="1038" fill="#7F7F7F" font-family="monospace" font-size="18px">Position...</text></switch></g><path d="M 418 505 L 620 505" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="409" y="510" width="220" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 530px; margin-left: 410px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;"><font face="monospace" style="font-size: 18px">uORB</font></div></div></div></foreignObject><text x="519" y="535" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">uORB</text></switch></g><path d="M 520 604.67 L 520 595 L 519.81 555" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 531 604.67 L 520 582.67 L 509 604.67" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 360.5 529.83 L 381 529.8 L 410.5 529.83" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 360.52 540.83 L 382.5 529.8 L 360.48 518.83" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 460.2 604.8 L 460.2 595 L 459.8 555.2" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 471.2 604.8 L 460.2 582.8 L 449.2 604.8" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 580.2 604.8 L 580.2 595 L 579.8 555.2" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 591.2 604.8 L 580.2 582.8 L 569.2 604.8" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 630.24 479.81 L 650 479.8 L 680.24 479.81" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 680.24 468.81 L 658.24 479.8 L 680.23 490.81" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 360.24 479.81 L 380 479.8 L 410.24 479.81" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 360.24 490.81 L 382.24 479.8 L 360.23 468.81" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="478" y="940" width="222" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 220px; height: 1px; padding-top: 970px; margin-left: 479px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Missions / RTL / ...</div></div></div></foreignObject><text x="589" y="975" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">Missions / RTL / ...</text></switch></g><path d="M 630.23 529.81 L 650 529.8 L 680.23 529.81" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 680.23 518.81 L 658.23 529.8 L 680.22 540.81" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="473" y="1073" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="480" y="1063" width="220" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 1093px; margin-left: 481px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Position Controller</div></div></div></foreignObject><text x="590" y="1100" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Position Controller</text></switch></g><path d="M 488 1118 L 690 1118" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="478" y="1123" width="222" height="50" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 220px; height: 1px; padding-top: 1148px; margin-left: 479px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">mc_pos_control /<br />fw_pos_control_l1</div></div></div></foreignObject><text x="589" y="1153" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">mc_pos_control /...</text></switch></g><rect x="474" y="1296" width="233" height="174" rx="26.1" ry="26.1" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="484" y="1530" width="218" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 1560px; margin-left: 485px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Control Allocation</div></div></div></foreignObject><text x="593" y="1567" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Control Allocation</text></switch></g><path d="M 491 1585 L 693 1585" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="482" y="1590" width="220" height="41" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 1611px; margin-left: 483px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">control_allocator</div></div></div></foreignObject><text x="592" y="1616" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">control_allocator</text></switch></g><rect x="865" y="990" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="874" y="980" width="216" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 214px; height: 1px; padding-top: 1010px; margin-left: 875px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Sensors Hub</div></div></div></foreignObject><text x="982" y="1017" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Sensors Hub</text></switch></g><path d="M 880 1035 L 1082 1035" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="872" y="1040" width="218" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 1060px; margin-left: 873px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">sensors</div></div></div></foreignObject><text x="981" y="1065" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">sensors</text></switch></g><rect x="874" y="1080" width="216" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 214px; height: 1px; padding-top: 1110px; margin-left: 875px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Handle failover and transform sensor data</div></div></div></foreignObject><text x="982" y="1115" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">Handle failover and tran...</text></switch></g><rect x="865" y="1215" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="874" y="1218" width="216" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 214px; height: 1px; padding-top: 1248px; margin-left: 875px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Position &amp; Attitude Estimator</div></div></div></foreignObject><text x="982" y="1255" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Position &amp; Attitude...</text></switch></g><path d="M 880 1285 L 1082 1285" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="872" y="1290" width="218" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 1310px; margin-left: 873px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">ekf2 / attitude_estimator_q</div></div></div></foreignObject><text x="981" y="1315" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">ekf2 / attitude_estimato...</text></switch></g><path d="M 981.5 1204.9 L 981.5 1178 L 982 1140" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 981.5 1211.65 L 977 1202.65 L 981.5 1204.9 L 986 1202.65 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1178px; margin-left: 991px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;">IMU data,<br />Airspeed</div></div></div></foreignObject><text x="991" y="1184" fill="#7F7F7F" font-family="monospace" font-size="18px">IMU data,...</text></switch></g><rect x="195" y="850" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="198" y="840" width="226" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 224px; height: 1px; padding-top: 870px; margin-left: 199px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">State Machine</div></div></div></foreignObject><text x="311" y="877" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">State Machine</text></switch></g><path d="M 210 895 L 412 895" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="202" y="900" width="218" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 920px; margin-left: 203px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">commander</div></div></div></foreignObject><text x="311" y="925" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">commander</text></switch></g><rect x="203" y="947" width="216" height="46" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 214px; height: 1px; padding-top: 970px; margin-left: 204px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Mode Switching / Arming / ...</div></div></div></foreignObject><text x="311" y="975" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">Mode Switching / Arming...</text></switch></g><rect x="719" y="572" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="721" y="562" width="228" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 226px; height: 1px; padding-top: 592px; margin-left: 722px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">RC Input</div></div></div></foreignObject><text x="835" y="599" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">RC Input</text></switch></g><path d="M 734 617 L 936 617" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="720" y="622" width="230" height="48" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 646px; margin-left: 721px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">rc_input / px4io / ...</div></div></div></foreignObject><text x="835" y="651" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">rc_input / px4io / ...</text></switch></g><rect x="721" y="665" width="228" height="54" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 226px; height: 1px; padding-top: 692px; margin-left: 722px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">PPM / SBUS / DSM  / SUMD / ST24</div></div></div></foreignObject><text x="835" y="697" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">PPM / SBUS / DSM  / SUMD...</text></switch></g><rect x="721" y="390" width="230" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 410px; margin-left: 722px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">camera_trigger</div></div></div></foreignObject><text x="836" y="415" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">camera_trigger</text></switch></g><rect x="994" y="390" width="226" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 224px; height: 1px; padding-top: 410px; margin-left: 995px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">gimbal</div></div></div></foreignObject><text x="1107" y="415" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">gimbal</text></switch></g><rect x="992" y="572" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="998" y="562" width="222" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 220px; height: 1px; padding-top: 592px; margin-left: 999px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">IMU Drivers</div></div></div></foreignObject><text x="1109" y="599" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">IMU Drivers</text></switch></g><path d="M 1007 617 L 1209 617" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="998" y="662" width="222" height="58" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 220px; height: 1px; padding-top: 691px; margin-left: 999px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">via SPI, UAVCAN, I<sup>2</sup>C</div></div></div></foreignObject><text x="1109" y="696" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">via SPI, UAVCAN, I2C</text></switch></g><path d="M 984.59 789.92 L 984.6 760 L 835.5 760 L 835.5 722" fill="none" stroke="#6ca8a4" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 984.58 796.67 L 980.09 787.66 L 984.59 789.92 L 989.09 787.67 Z" fill="#6ca8a4" stroke="#6ca8a4" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="720" y="457" width="233" height="95" rx="14.25" ry="14.25" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="724" y="447" width="226" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 224px; height: 1px; padding-top: 477px; margin-left: 725px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">GPS</div></div></div></foreignObject><text x="837" y="484" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">GPS</text></switch></g><path d="M 735 502 L 937 502" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="722" y="507" width="228" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 226px; height: 1px; padding-top: 527px; margin-left: 723px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">gps</div></div></div></foreignObject><text x="836" y="532" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">gps</text></switch></g><rect x="992" y="458" width="233" height="95" rx="14.25" ry="14.25" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="998" y="454" width="220" height="91" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 500px; margin-left: 999px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Airspeed, Telemetry, Optical Flow, Distance Sensor, ...</div></div></div></foreignObject><text x="1108" y="506" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Airspeed, Telemetry,...</text></switch></g><path d="M 252.1 430.9 L 310 430.9 L 310 203.1" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 245.35 430.9 L 254.35 426.4 L 252.1 430.9 L 254.35 435.4 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 310 196.35 L 314.5 205.35 L 310 203.1 L 305.5 205.35 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 250.58 660 L 261 660 L 280.37 660" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 243.83 660 L 252.83 655.5 L 250.58 660 L 252.83 664.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 287.12 660 L 278.12 664.5 L 280.37 660 L 278.12 655.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 250.56 588.18 L 260 588.2 L 280.37 588.07" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 243.81 588.17 L 252.82 583.69 L 250.56 588.18 L 252.8 592.69 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 287.12 588.02 L 278.15 592.58 L 280.37 588.07 L 278.09 583.58 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 250.58 623 L 260 623 L 280.37 623" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 243.83 623 L 252.83 618.5 L 250.58 623 L 252.83 627.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 287.12 623 L 278.12 627.5 L 280.37 623 L 278.12 618.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 311 839.92 L 311 469.9 L 252.34 469.92" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 311 846.67 L 306.5 837.67 L 311 839.92 L 315.5 837.67 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 245.59 469.93 L 254.59 465.42 L 252.34 469.92 L 254.59 474.42 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1108.1 1290 L 1190 1290 L 1190 740" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1101.35 1290 L 1110.35 1285.5 L 1108.1 1290 L 1110.35 1294.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1340px; margin-left: 1112px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;">GPS,<br />Optical<br />Flow,<br />Distance</div></div></div></foreignObject><text x="1112" y="1345" fill="#7F7F7F" font-family="monospace" font-size="18px">GPS,...</text></switch></g><path d="M 716.1 1148 L 750 1148 L 750 850 L 869.5 850" fill="none" stroke="#6ca8a4" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 709.35 1148 L 718.35 1143.5 L 716.1 1148 L 718.35 1152.5 Z" fill="#6ca8a4" stroke="#6ca8a4" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 870px; margin-left: 752px;"><div data-drawio-colors="color: #6CA8A4; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(108, 168, 164); line-height: 1.2; pointer-events: all; white-space: nowrap;"><font style="font-size: 10px"> </font>RC</div></div></div></foreignObject><text x="752" y="875" fill="#6CA8A4" font-family="monospace" font-size="18px"> RC</text></switch></g><path d="M 717.1 1348.2 L 750 1348.2 L 750 1148" fill="none" stroke="#6ca8a4" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 710.35 1348.2 L 719.35 1343.7 L 717.1 1348.2 L 719.35 1352.7 Z" fill="#6ca8a4" stroke="#6ca8a4" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 800 850 L 780 850 L 750 850 L 750 810 L 350 810 L 350 839.9" fill="none" stroke="#6ca8a4" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 350 846.65 L 345.5 837.65 L 350 839.9 L 354.5 837.65 Z" fill="#6ca8a4" stroke="#6ca8a4" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 428 925 L 460 925 L 461.9 925" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 468.65 925 L 459.65 929.5 L 461.9 925 L 459.65 920.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 309.87 1000 L 309.9 1148 L 462.9 1148" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 469.65 1148 L 460.65 1152.5 L 462.9 1148 L 460.65 1143.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 310 1141 L 310 1383 L 463.9 1383" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 470.65 1383 L 461.65 1387.5 L 463.9 1383 L 461.65 1378.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 589 1285.9 L 589 1223" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 589 1292.65 L 584.5 1283.65 L 589 1285.9 L 593.5 1283.65 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1256px; margin-left: 601px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;">Attitude<br />Setpoints</div></div></div></foreignObject><text x="601" y="1261" fill="#7F7F7F" font-family="monospace" font-size="18px">Attitude...</text></switch></g><path d="M 589 1532.75 L 589 1503 L 589 1470" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 589 1539.5 L 584.5 1530.5 L 589 1532.75 L 593.5 1530.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1503px; margin-left: 601px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;">Thrust and Torque<br />Setpoints</div></div></div></foreignObject><text x="601" y="1508" fill="#7F7F7F" font-family="monospace" font-size="18px">Thrust and Torque...</text></switch></g><path d="M 865 1290 L 830 1290 L 830 1428.4 L 717.1 1428.41" fill="none" stroke="#a38b49" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 710.35 1428.41 L 719.35 1423.91 L 717.1 1428.41 L 719.35 1432.91 Z" fill="#a38b49" stroke="#a38b49" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 840 1290 L 830 1290 L 830 1179.1 L 716.1 1179.05" fill="none" stroke="#a38b49" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 709.35 1179.05 L 718.36 1174.55 L 716.1 1179.05 L 718.35 1183.55 Z" fill="#a38b49" stroke="#a38b49" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="499" y="88" width="178" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 176px; height: 1px; padding-top: 108px; margin-left: 500px;"><div data-drawio-colors="color: #5C5C5C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(92, 92, 92); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">param</div></div></div></foreignObject><text x="588" y="113" fill="#5C5C5C" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">param</text></switch></g><rect x="488" y="127" width="199" height="70" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 197px; height: 1px; padding-top: 162px; margin-left: 489px;"><div data-drawio-colors="color: #5C5C5C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(92, 92, 92); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;"><font style="font-size: 18px;"><font face="Helvetica" style="font-size: 18px;">EEPROM / SD Card /<br />FLASH</font><br /></font></div></div></div></foreignObject><text x="588" y="167" fill="#5C5C5C" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">EEPROM / SD Card /...</text></switch></g><path d="M 260 126.14 C 269.6 130.75 286.87 134.81 306.29 136.89 C 340.45 140.6 376.92 140.5 409.55 137.24 C 429.76 135.23 447.37 131.55 459 126.14 L 459 160.44 C 457.48 169.91 443.7 174.9 428.8 178.23 C 395.04 185.13 337.06 186 295.9 179.38 C 278.8 176.25 261.47 171.02 260 160.41 Z M 260 74.29 C 269.6 78.94 286.87 82.96 306.29 85.08 C 340.45 88.76 376.92 88.69 409.55 85.43 C 429.76 83.38 447.37 79.74 459 74.33 L 459 108.59 C 457.48 118.06 443.7 123.05 428.8 126.42 C 395.04 133.29 337.06 134.15 295.9 127.56 C 278.8 124.44 261.47 119.2 260 108.59 Z M 260 60.59 C 260 51.54 290.88 42.08 357.61 41 C 423.83 41 459 51.37 459 60.56 C 459 70.72 416.38 80.02 364.04 80.02 C 310.36 80.47 260 72.21 260 60.59 Z" fill="#b3b2b2" stroke="none" pointer-events="all"/><rect x="261" y="37" width="198" height="42" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 196px; height: 1px; padding-top: 58px; margin-left: 262px;"><div data-drawio-colors="color: #5C5C5C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(92, 92, 92); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;"><font style="font-size: 22px">Database</font></div></div></div></foreignObject><text x="360" y="65" fill="#5C5C5C" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Database</text></switch></g><rect x="271" y="88" width="178" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 176px; height: 1px; padding-top: 108px; margin-left: 272px;"><div data-drawio-colors="color: #5C5C5C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(92, 92, 92); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">dataman</div></div></div></foreignObject><text x="360" y="113" fill="#5C5C5C" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">dataman</text></switch></g><rect x="260" y="125" width="199" height="70" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 197px; height: 1px; padding-top: 160px; margin-left: 261px;"><div data-drawio-colors="color: #5C5C5C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(92, 92, 92); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;"><font style="font-size: 18px"><font face="Helvetica" style="font-size: 18px">Missions / Geofence<br />to SD Card / RAM</font></font></div></div></div></foreignObject><text x="360" y="165" fill="#5C5C5C" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">Missions / Geofence...</text></switch></g><rect x="483" y="1300" width="216" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 214px; height: 1px; padding-top: 1330px; margin-left: 484px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Attitude &amp; Rate Controller</div></div></div></foreignObject><text x="591" y="1337" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Attitude &amp; Rate Cont...</text></switch></g><path d="M 489 1367 L 691 1367" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="481" y="1372" width="218" height="88" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 1416px; margin-left: 482px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">mc_att_control &amp; mc_rate_control /<br />fw_att_control /<br />vtol_att_control</div></div></div></foreignObject><text x="590" y="1421" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">mc_att_control &amp; mc_rate...</text></switch></g><path d="M 830 1180 L 830 954.9 L 715.25 954.86" fill="none" stroke="#a38b49" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 708.5 954.86 L 717.5 950.36 L 715.25 954.86 L 717.5 959.36 Z" fill="#a38b49" stroke="#a38b49" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 519.2 454.8 L 518.8 405.2" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 507.8 405.29 L 518.98 427.2 L 529.8 405.11" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="864.5" y="800" width="233" height="110" rx="16.5" ry="16.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="871.5" y="790" width="220" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 820px; margin-left: 873px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">RC Handling</div></div></div></foreignObject><text x="982" y="827" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">RC Handling</text></switch></g><path d="M 879.5 845 L 1081.5 845" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="869.5" y="850" width="222" height="50" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 220px; height: 1px; padding-top: 875px; margin-left: 871px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">rc_update -&gt; manual_control</div></div></div></foreignObject><text x="981" y="880" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">rc_update -&gt; manual_contr...</text></switch></g><rect x="471.5" y="1760" width="233" height="150" rx="22.5" ry="22.5" fill="#4f73bd" stroke="none" pointer-events="all"/><rect x="479.5" y="1850" width="218" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 1880px; margin-left: 481px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">ESC / Servo control</div></div></div></foreignObject><text x="589" y="1885" fill="#FFFFFF" font-family="Helvetica" font-size="18px" text-anchor="middle" font-weight="bold">ESC / Servo control</text></switch></g><path d="M 588.75 1939.9 L 589 1930 L 588.5 1910" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 588.58 1946.65 L 584.31 1937.54 L 588.75 1939.9 L 593.31 1937.76 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1921px; margin-left: 591px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;"><font face="Helvetica">PWM / UART / CAN / ...<br /></font></div></div></div></foreignObject><text x="591" y="1926" fill="#7F7F7F" font-family="monospace" font-size="18px">PWM / UART / CAN / ...&#xa;</text></switch></g><rect x="479.5" y="1750" width="218" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 216px; height: 1px; padding-top: 1780px; margin-left: 481px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Output Driver</div></div></div></foreignObject><text x="589" y="1787" fill="#FFFFFF" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Output Driver</text></switch></g><path d="M 486.5 1805 L 688.5 1805" fill="none" stroke="#ffffff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="477.5" y="1810" width="220" height="41" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 1831px; margin-left: 479px;"><div data-drawio-colors="color: #CCCCCC; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(204, 204, 204); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">pwm_out / px4io / ...</div></div></div></foreignObject><text x="588" y="1836" fill="#CCCCCC" font-family="monospace" font-size="18px" text-anchor="middle" font-weight="bold">pwm_out / px4io / ...</text></switch></g><path d="M 588.71 1752.75 L 588.7 1723 L 588.71 1690" fill="none" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 588.71 1759.5 L 584.21 1750.5 L 588.71 1752.75 L 593.21 1750.5 Z" fill="#7f7f7f" stroke="#7f7f7f" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1723px; margin-left: 601px;"><div data-drawio-colors="color: #7F7F7F; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: monospace; color: rgb(127, 127, 127); line-height: 1.2; pointer-events: all; white-space: nowrap;">Motor and Servo<br />Setpoints</div></div></div></foreignObject><text x="601" y="1728" fill="#7F7F7F" font-family="monospace" font-size="18px">Motor and Servo...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>