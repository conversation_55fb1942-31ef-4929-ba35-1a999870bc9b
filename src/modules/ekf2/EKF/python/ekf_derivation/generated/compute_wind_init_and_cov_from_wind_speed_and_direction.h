// -----------------------------------------------------------------------------
// This file was autogenerated by symforce from template:
//     function/FUNCTION.h.jinja
// Do NOT modify by hand.
// -----------------------------------------------------------------------------

#pragma once

#include <matrix/math.hpp>

namespace sym {

/**
 * This function was autogenerated from a symbolic function. Do not modify by hand.
 *
 * Symbolic function: compute_wind_init_and_cov_from_wind_speed_and_direction
 *
 * Args:
 *     wind_speed: Scalar
 *     wind_direction: Scalar
 *     wind_speed_var: Scalar
 *     wind_direction_var: Scalar
 *
 * Outputs:
 *     wind: Matrix21
 *     P_wind: Matrix21
 */
template <typename Scalar>
void ComputeWindInitAndCovFromWindSpeedAndDirection(
    const Scalar wind_speed, const Scalar wind_direction, const Scalar wind_speed_var,
    const Scalar wind_direction_var, matrix::Matrix<Scalar, 2, 1>* const wind = nullptr,
    matrix::Matrix<Scalar, 2, 1>* const P_wind = nullptr) {
  // Total ops: 14

  // Input arrays

  // Intermediate terms (5)
  const Scalar _tmp0 = std::cos(wind_direction);
  const Scalar _tmp1 = std::sin(wind_direction);
  const Scalar _tmp2 = std::pow(_tmp0, Scalar(2));
  const Scalar _tmp3 = std::pow(_tmp1, Scalar(2));
  const Scalar _tmp4 = wind_direction_var * std::pow(wind_speed, Scalar(2));

  // Output terms (2)
  if (wind != nullptr) {
    matrix::Matrix<Scalar, 2, 1>& _wind = (*wind);

    _wind(0, 0) = _tmp0 * wind_speed;
    _wind(1, 0) = _tmp1 * wind_speed;
  }

  if (P_wind != nullptr) {
    matrix::Matrix<Scalar, 2, 1>& _p_wind = (*P_wind);

    _p_wind(0, 0) = _tmp2 * wind_speed_var + _tmp3 * _tmp4;
    _p_wind(1, 0) = _tmp2 * _tmp4 + _tmp3 * wind_speed_var;
  }
}  // NOLINT(readability/fn_size)

// NOLINTNEXTLINE(readability/fn_size)
}  // namespace sym
