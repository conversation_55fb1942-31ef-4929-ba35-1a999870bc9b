#!/bin/sh
#
# @name Hippocampus UUV
#

. ${R}etc/init.d/rc.uuv_defaults

param set-default CA_AIRFRAME 7
param set-default CA_ROTOR_COUNT 4
param set-default CA_R_REV 255
param set-default CA_ROTOR0_AX 1
param set-default CA_ROTOR0_AY 0
param set-default CA_ROTOR0_AZ 0
param set-default CA_ROTOR0_KM 0
param set-default CA_ROTOR0_PX 0
param set-default CA_ROTOR0_PY -0.3
param set-default CA_ROTOR0_PZ -0.3
param set-default CA_ROTOR1_AX 1
param set-default CA_ROTOR1_AY 0
param set-default CA_ROTOR1_AZ 0
param set-default CA_ROTOR1_KM 0
param set-default CA_ROTOR1_PX 0
param set-default CA_ROTOR1_PY 0.3
param set-default CA_ROTOR1_PZ -0.3
param set-default CA_ROTOR2_AX 1
param set-default CA_ROTOR2_AY 0
param set-default CA_ROTOR2_AZ 0
param set-default CA_ROTOR2_KM 0
param set-default CA_ROTOR2_PX 0
param set-default CA_ROTOR2_PY 0.3
param set-default CA_ROTOR2_PZ 0.3
param set-default CA_ROTOR3_AX 1
param set-default CA_ROTOR3_AY 0
param set-default CA_ROTOR3_AZ 0
param set-default CA_ROTOR3_KM 0
param set-default CA_ROTOR3_PX 0
param set-default CA_ROTOR3_PY -0.3
param set-default CA_ROTOR3_PZ 0.3

param set-default PWM_MAIN_FUNC1 101
param set-default PWM_MAIN_FUNC2 102
param set-default PWM_MAIN_FUNC3 103
param set-default PWM_MAIN_FUNC4 104
