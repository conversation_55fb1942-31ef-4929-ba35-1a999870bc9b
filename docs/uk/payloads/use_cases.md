# Використання корисного навантаження

Цей розділ містить кілька загальних випадків використання "корисного навантаження дронів" та те, як вони підтримуються PX4.

## Дрони для картографування

Дрони для картографування використовують камери для знімання зображень з інтервалами у часі або відстані під час обстежень.

PX4 підтримує камери, які підключені через [MAVLink](../camera/mavlink_v2_camera.md) або до [Flight Controller Outputs](../camera/fc_connected_camera.md).
Обидва типи камер підтримують використання варіанту використання картографування, але за допомогою іншого набору команд/елементів місій MAVLink.

## Вантажні дрони (доставлення посилок)

Вантажні дрони зазвичай використовують захоплювачі, лебідки та інші механізми, щоб розвантажувати пакунки в місцях призначення.

PX4 підтримує _доставку посилок у місіях_ за допомогою [захвату](../peripherals/gripper.md).
Захоплювачі також можна активувати за допомогою [MAV_CMD_DO_GRIPPER](https://mavlink.io/en/messages/common.html#MAV_CMD_DO_GRIPPER) команди MAVLink, або вручну за допомогою кнопки джойстика.

Встановлення та інформація про використання:

- [Захоплювач](../peripherals/gripper.md)
- [Політ > Планування місії доставки посилок](../flying/package_delivery_mission.md)

:::info
Support for winches and other release mechanisms is also intended.

Якщо вам необхідно здійснити доставлення вантажу, використовуючи апаратне забезпечення, яке ще не інтегроване, ви можете використати [Керування Загальним Приводом](../payloads/generic_actuator_control.md).
:::

## Спостереження, Пошук & Порятунок

Дрони Спостереження, Пошуку & Порятунку мають вимоги, подібні до картографічних дронів.
Основні відмінності полягають у тому, що, окрім польоту в запланованій зоні огляду, їм зазвичай потрібен хороший автономний контроль над камерою для знімання зображень і відео, і їм може знадобитися можливість працювати вдень і вночі

Використовуйте [MAVLink](../camera/mavlink_v2_camera.md), який підтримує [протокол камери MAVLink](https://mavlink.io/en/services/camera.html), оскільки це дозволяє захоплення зображень і відео, масштабування, керування сховищем, кілька камер на одному апараті та перемикання між ними тощо.
Ці камери можна контролювати як вручну з QGroundControl, так і через MAVSDK (як для [операцій автономної камери](https://mavsdk.mavlink.io/main/en/cpp/api_reference/classmavsdk_1_1_camera.html), так і в [місіях](https://mavsdk.mavlink.io/main/en/cpp/api_reference/structmavsdk_1_1_mission_1_1_mission_item.html#structmavsdk_1_1_mission_1_1_mission_item_1a0299fbbe7c7b03bc43eb116f96b48df4)).
Дивіться [MAVLink Camera](../camera/mavlink_v2_camera.md), щоб дізнатися як налаштувати камеру на роботу з MAVLink.

:::info
Камери, підключені безпосередньо до політного контролера підтримують _тільки_ активацію камери та навряд чи підходять для більшості діяльностей зі спостереження/пошуку.
:::

Пошуково-рятувальному безпілотнику також може знадобитися транспортувати вантажі, наприклад, для екстреної допомоги туристу, що застряг.
Перегляньте [Вантажні дрони](#cargo-drones-package-delivery) вище, щоб отримати інформацію про доставлення корисного вантажу.

## Сільськогосподарські дрони/Обприскування насаджень

Сільськогосподарські безпілотні літальні апарати зазвичай використовуються для картографування стану рослин, виявлення шкідників і догляду за тваринами (випасання, відстеження тощо).
Ці варіанти використання подібні до [картографування](#mapping-drones) та [спостереження, пошуку & порятунку](#surveillance-search-rescue) наведених вище.
Хоча для окремих культур/тварин можуть знадобитися спеціальні камери, інтеграція з PX4 залишається такою ж.

Сільськогосподарський дрон також можна використовувати для обприскування посівів.
У цьому випадку розпилювачем необхідно керувати як [загальним приводом](../payloads/generic_actuator_control.md):

- [Керування Загальним Приводом](../payloads/generic_actuator_control.md#generic-actuator-control-with-mavlink) пояснює, як можна під'єднати виводи політного контролера до розпилювача, щоб ним можна було керувати за допомогою MAVLink.
  Більшість розпилювачів мають засоби керування для ввімкнення/вимкнення помпи; деякі також дозволяють контролювати швидкість потоку або область розпилення (тобто, керуючи формою сопла або використовуючи спінер для розподілу корисного навантаження).
- Ви можете визначити область для оприскування, використовуючи [Survey патерн](https://docs.qgroundcontrol.com/master/en/qgc-user-guide/plan_view/pattern_survey.html), або ви можете визначити сітку для польоту, використовуючи точки маршрутів.
  У будь-якому випадку важливо переконатися, що траєкторія польоту апарату та його висота забезпечують належне покриття для конкретного спрею, що використовується.
- Щоб увімкнути та вимкнути розпилювач, ви повинні додати [елемент місії "Set actuator"](../payloads/generic_actuator_control.md#generic-actuator-control-in-missions) до вашої місії до та після survey патерну.
