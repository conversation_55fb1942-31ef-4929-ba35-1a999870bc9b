# Release 1.13

- [Release 1.13](#release-1-13)
  - [Pre Releases](#pre-releases)
  - [Changes](#changes)
    - [Common](#common)
    - [Sensors](#sensors)
    - [Hardware](#hardware)
    - [MAVLink](#mavlink)
    - [Multicopter](#multicopter)
    - [VTOL](#vtol)
    - [Control](#control)
    - [GPS](#gps)
    - [NuttX](#nuttx)
    - [CAN](#can)

## Pre Releases

- [Release Candidate 1](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.13.0-rc1)
- [Beta 1](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.13.0-beta1)

## Changes

### Common

- **Explicit Joystick source selection ([PR#17404](https://github.com/PX4/PX4-Autopilot/pull/17404))**

  - Possibility to:
    - Explicitly allow just one source
    - Fall back to other source in air
    - Disable stick input completely
  - Configuration parameter: [COM_RC_IN_MODE](../advanced_config/parameter_reference.md#COM_RC_IN_MODE)

- Remaining flight time based on low battery ([PR#17828](https://github.com/PX4/PX4-Autopilot/pull/17828))
- Remaining flight time based low battery RTL ([PR#18646](https://github.com/PX4/PX4-Autopilot/pull/18646))
- Improved default battery estimation parameters ([PR#19429](https://github.com/PX4/PX4-Autopilot/pull/19429), [PR#19700](https://github.com/PX4/PX4-Autopilot/pull/19700))
- Autonomous gentle touch down using distance sensor ([PR#19126](https://github.com/PX4/PX4-Autopilot/pull/19126))
- Orbit Flight Mode Improvements ([approach smoothness](https://github.com/PX4/PX4-Autopilot/pull/18988), [radius limiting](https://github.com/PX4/PX4-Autopilot/pull/19362), and [stick input with heading not to the center](https://github.com/PX4/PX4-Autopilot/pull/19367))

### Sensors

- Barometers
  - Goertek SPL06 ([PR#19229](https://github.com/PX4/PX4-Autopilot/pull/19229))
  - TDK ICP10100 & ICP10111 ([PR#18451](https://github.com/PX4/PX4-Autopilot/pull/18451))
  - Maiertek MPC2520 ([PR#17910](https://github.com/PX4/PX4-Autopilot/pull/17910))
  - TE MS5837 ([PR#19183](https://github.com/PX4/PX4-Autopilot/pull/19183))
- Hygometer
  - Sensirion SHT3X ([PR#18910](https://github.com/PX4/PX4-Autopilot/pull/18910))
- IMU
  - TDK ICM42670p ([PR#18141](https://github.com/PX4/PX4-Autopilot/pull/18141))
  - BMI088 I2C (Support Improved) ([PR#17467](https://github.com/PX4/PX4-Autopilot/pull/17467))
- Optical Flow
  - Pixart PAA3905 ([PR#19461](https://github.com/PX4/PX4-Autopilot/pull/19461))
- Power Monitor
  - TI INA228 ([PR#19461](https://github.com/PX4/PX4-Autopilot/pull/17994))
  - TI INA238 ([PR#18260](https://github.com/PX4/PX4-Autopilot/pull/18260))
- Misc
  - GPS PPS Input Capture ([PR#18849](https://github.com/PX4/PX4-Autopilot/pull/18849))
  - NXP Ultra Wide Band (UWB) SR150 ([PR#14474](https://github.com/PX4/PX4-Autopilot/pull/14474))

### Hardware

- ARK CAN RTK GPS ([PR#18412](https://github.com/PX4/PX4-Autopilot/pull/14474))
- ATL Mantis EDU ([PR#17910](https://github.com/PX4/PX4-Autopilot/pull/17910))
- Diatone Mamba f405 mk2 ([PR#18402](https://github.com/PX4/PX4-Autopilot/pull/18402))
- Holybro Kakute H7 ([PR#19019](https://github.com/PX4/PX4-Autopilot/pull/19019))
- Matek GNSS-M9N-F4 ([PR#19061](https://github.com/PX4/PX4-Autopilot/pull/19061))
- Matek H743-slim ([PR#18544](https://github.com/PX4/PX4-Autopilot/pull/18544))
- mRo Control Zero Classic ([PR#19593](https://github.com/PX4/PX4-Autopilot/pull/19593))
- Pixhawk FMUv6C ([PR#19544](https://github.com/PX4/PX4-Autopilot/pull/19544))
- Raspberry Pi Pico ([PR#18083](https://github.com/PX4/PX4-Autopilot/pull/18083))
- Sky Drones SmartAP Airlink ([PR#19529](https://github.com/PX4/PX4-Autopilot/pull/19529))

### MAVLink

- MAVLink triggered parachute system support ([PR#18589](https://github.com/PX4/PX4-Autopilot/pull/18589))

### Estimation

- Control Allocation Beta ([PR#18776](https://github.com/PX4/PX4-Autopilot/pull/18776))
- Estimation
  - EKF2 barometer bias estimator
  - EKF2 range finder kinematic consistency check
  - EKF2 propeller momentum drag coefficient used for multi-rotor wind estimation.
  - incremental accel and gyro auto-calibration
  - dedicated magnetometer bias estimator for easy preflight calibration

### Control

- **Dynamic Control Allocation ([PR#18776](https://github.com/PX4/PX4-Autopilot/pull/18776))**
  - Disabled by default, see [usage instructions](../config/actuators.md) to get started
  - Easy and flexible actuator configuration
  - Support for more hardware setups without manual mixer file adjustments
  - Possibility to dynamically adjust allocation in flight e.g. rotor loss

### Multicopter

- auto tune

### VTOL

- auto tune

### GPS

### NuttX

### CAN

- UAVCANv1 aka OpenCyphal
- DroneCAN
  - Battery Support
  - Hygrometer Support
  - Internal Combustion Engine (ICE)
  - Log message handling from CAN nodes
