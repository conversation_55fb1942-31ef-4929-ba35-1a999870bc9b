# Arm, Disarm, Prearm Конфігурація

Транспортні засоби можуть мати рухомі частини, деякі з яких можуть бути потенційно небезпечними під час роботи (особливо мотори та пропелери)!

Для зменшення ймовірності аварій, PX4 має явні стани для включення компонентів транспортного засобу:

- **Вимкнено:** Немає живлення для моторів або приводів.
- **Передпускний стан:** Мотори/пропелери заблоковані, але приводи для не небезпечної електроніки живлені (наприклад, елерони, закрилки і т. д.).
- **Озброєно:** Транспортний засіб повністю увімкнено. Двигуни/пропелери можуть обертатися (небезпечно!)

:::info
Наземні станції можуть відображати _вимкнено_ для транспортних засобів у режимі передпуску.
Хоча це не є технічно правильним для транспортних засобів у режимі передпуску, це "безпечно".
:::

Користувачі можуть керувати переходом між цими станами, використовуючи [захисний перемикач](../getting_started/px4_basic_concepts.md#safety-switch) на транспортному засобі (за бажанням) _та_ перемикач/кнопку [озброєння](#arm_disarm_switch), [жест озброєння](#arm_disarm_gestures) або _команду MAVLink_ на наземному контролері:

- _Захисний перемикач_ - це керування _на транспортному засобі_, яке повинно бути увімкнене перед озброєнням транспортного засобу, і яке може також запобігати передпуску (в залежності від конфігурації).
  Зазвичай захисний перемикач інтегровано у блок GPS, але він також може бути окремим фізичним компонентом.

  Транспортний засіб, який озброєний, потенційно небезпечний.
  Захисний перемикач - це додатковий механізм, який запобігає випадковому озброєнню.

:::

- Перемикач _озброєння_ - це перемикач або кнопка _на пульті керування RC_, який може бути використаний для озброєння транспортного засобу та запуску моторів (якщо озброєння не заборонене захисним перемикачем).

- Жест озброєння - це рух педалей _на пульті керування RC_, який може бути використаний як альтернатива перемикачу озброєння.

- Команди MAVLink також можуть бути відправлені наземною станцією управління для озброєння/вимкнення транспортного засобу.

PX4 також автоматично вимикає транспортний засіб, якщо він не злітає протягом певного часу після озброєння, і якщо він не вимикається вручну після посадки.
Це зменшує час, коли на землі знаходиться озброєний (і, отже, небезпечний) транспортний засіб.

PX4 дозволяє налаштовувати роботу передпуску, озброєння та вимикання за допомогою параметрів (які можна редагувати в _QGroundControl_ за допомогою [редактора параметрів](../advanced_config/parameters.md)), як описано у наступних розділах.

:::tip
Параметри озброєння/вимикання можна знайти у [Посилання на параметри > Командир](../advanced_config/parameter_reference.md#commander) (шукайте `COM_ARM_*` та `COM_DISARM_*`).
:::

## Arming/Disarming Gestures {#arm_disarm_gestures}

За замовчуванням, транспортний засіб озброюється та вимикається шляхом виконання певних рухів педалями газу/рулем в маневрів та утримання їх протягом 1 секунди.

- **Озброєння:** Мінімальна педаль газу, максимальний рульовий вектор
- **Вимикання:** Мінімальна педаль газу, мінімальний рульовий вектор

RC controllers will use different sticks for throttle and yaw [based on their mode](../getting_started/rc_transmitter_receiver.md#types-of-remote-controllers), and hence different gestures:

- **Режим 2**:
  - _Озброєння:_ Ліва педаль вниз і вправо.
  - _Вимкнення:_ Ліва педаль вниз і вліво.
- **Режим 1**:
  - _Озброєння:_ Ліва педаль вправо, права педаль вниз.
  - _Вимкнення:_ Ліва педаль вліво, права педаль вниз.

The required hold time can be configured using [COM_RC_ARM_HYST](#COM_RC_ARM_HYST).
Note that by default ([COM_DISARM_MAN](#COM_DISARM_MAN)) you can also disarm in flight using gestures/buttons: you may choose to disable this to avoid accidental disarming.

| Параметр                                                                                                                                                                | Опис                                                                                                                                                                                                    |
| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a id="MAN_ARM_GESTURE"></a>[MAN_ARM_GESTURE](../advanced_config/parameter_reference.md#MAN_ARM_GESTURE)                      | Enable arm/disarm stick guesture. `0`: Disabled, `1`: Enabled (default).                                             |
| <a id="COM_DISARM_MAN"></a>[COM_DISARM_MAN](../advanced_config/parameter_reference.md#COM_DISARM_MAN)                         | Enable disarming in flight via switch/stick/button in MC manual thrust modes. `0`: Disabled, `1`: Enabled (default). |
| <a id="COM_RC_ARM_HYST"></a>[COM_RC_ARM_HYST](../advanced_config/parameter_reference.md#COM_RC_ARM_HYST) | Час, протягом якого педаль RC повинна бути утримана в позиції озброєння/вимкнення перед озброєнням/вимкненням (за замовчуванням: 1 секунда).         |

## Arming Button/Switch {#arm_disarm_switch}

Кнопку _озброєння_ або "моментальний перемикач" можна налаштувати для спрацьовування озброєння/вимикання _замість_ [озброєння за допомогою жестів](#arm_disarm_gestures) (встановлення перемикача озброєння вимикає озброєння за допомогою жестів).
Кнопку слід утримувати натиснутою протягом ([зазвичай](#COM_RC_ARM_HYST)) однієї секунди, щоб озброїти (якщо вимкнено) або вимкнути (якщо озброєно).

Двопозиційний перемикач також може використовуватися для озброєння/вимикання, при цьому відповідні команди на озброєння/вимкнення надсилаються при _перемиканні_ перемикача.

:::tip
Двопозиційні перемикачі для озброєння переважно використовуються в/рекомендовані для гоночних дронів.
:::

Перемикач або кнопка призначається (та активується) за допомогою [RC_MAP_ARM_SW](#RC_MAP_ARM_SW), а тип перемикача налаштовується за допомогою [COM_ARM_SWISBTN](#COM_ARM_SWISBTN).

| Параметр                                                                                                                                                          | Опис                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| ----------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a id="RC_MAP_ARM_SW"></a>[RC_MAP_ARM_SW](../advanced_config/parameter_reference.md#RC_MAP_ARM_SW) | Канал перемикача озброєння радіокерування (типове значення: 0 - не призначено). Якщо визначено, вказаний канал радіокерування (кнопка/перемикач) використовується для озброєння замість жесту палиці. <br>**Note:**<br>- This setting _disables the stick gesture_!<br>- This setting applies to RC controllers. It does not apply to Joystick controllers that are connected via _QGroundControl_. |
| <a id="COM_ARM_SWISBTN"></a>[COM_ARM_SWISBTN](../advanced_config/parameter_reference.md#COM_ARM_SWISBTN)                | Перемикач озброєння є моментальною кнопкою. <br>- `0`: Arm switch is a 2-position switch where arm/disarm commands are sent on switch transitions.<br>-`1`: Arm switch is a button or momentary button where the arm/disarm command ae sent after holding down button for set time ([COM_RC_ARM_HYST](#COM_RC_ARM_HYST)).                                               |

:::info
Перемикач також можна налаштувати як частину конфігурації _QGroundControl_ для [Режиму польоту](../config/flight_mode.md).
:::

## Автоматичне вимкнення

За замовчуванням транспортні засоби автоматично вимикаються при посадці або якщо ви заберете занадто багато часу, щоб злітати після озброєння.
Ця функція налаштовується за допомогою наступних таймаутів.

| Параметр                                                                                                                                              | Опис                                                                                                                                                                                                                 |
| ----------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a id="COM_DISARM_LAND"></a>[COM_DISARM_LAND](../advanced_config/parameter_reference.md#COM_DISARM_LAND)    | Час очікування для автоматичного відбрасування після приземлення. За замовчуванням: 2с (значення -1, щоб вимкнути).                               |
| <a id="COM_DISARM_PRFLT"></a>[COM_DISARM_PRFLT](../advanced_config/parameter_reference.md#COM_DISARM_PRFLT) | Час очікування для автоматичного відбрасування, якщо занадто повільно підйом. За замовчуванням: 10с (<=0, щоб вимкнути). |

## Pre-Arm Checks

To reduce accidents, vehicles are only allowed to arm certain conditions are met (some of which are configurable).
Армування заборонено у таких випадках:

- Повітряне судно не перебуває у "здоровому" стані.
  Наприклад, воно не калібрується або має помилки датчиків.
- Транспортний засіб має [захисний перемикач](../getting_started/px4_basic_concepts.md#safety-switch), який не був увімкнений.
- The vehicle has a [remote ID](../peripherals/remote_id.md) that is unhealthy or otherwise not ready
- VTOL-повітряне судно перебуває в режимі фіксованого крила ([by default(за замовчуванням)](../advanced_config/parameter_reference.md#CBRK_VTOLARMING)).
- Поточний режим потребує належної глобальної позиційної оцінки, але повітряне судно не має блокування GPS.
- Many more (see [arming/disarming safety settings](../config/safety.md#arming-disarming-settings) for more information).

The current failed checks can be viewed in QGroundControl (v4.2.0 and later) [Arming Check Report](../flying/pre_flight_checks.md#qgc-arming-check-report) (see also [Fly View > Arming and Preflight Checks](https://docs.qgroundcontrol.com/master/en/qgc-user-guide/fly_view/fly_view.md#arm)).

Зауважте, що внутрішньо PX4 перевіряє активацію на 10 Гц.
Список невдалих перевірок зберігається, і якщо цей список змінюється, PX4 видає поточний список за допомогою [інтерфейсу подій](../concept/events_interface.md).
Список також надсилається, коли GCS підключається.
Пристрій керування (GCS) негайно знає статус передпускових перевірок як при вимкненні, так і при озброєнні.

:::details
Implementation notes for developers
Примітки для розробників Реалізація клієнта знаходиться у [libevents](https://github.com/mavlink/libevents):

- [libevents > Групи подій](https://github.com/mavlink/libevents#event-groups)
- [health_and_arming_checks.h](https://github.com/mavlink/libevents/blob/main/libs/cpp/parse/health_and_arming_checks.h)

QGC реалізація: [HealthAndArmingCheckReport.cc](https://github.com/mavlink/qgroundcontrol/blob/master/src/MAVLink/LibEvents/HealthAndArmingCheckReport.cc).
:::

PX4 також видає підмножину інформації перевірки постановки на охорону в повідомленні [SYS_STATUS](https://mavlink.io/en/messages/common.html#SYS_STATUS) (див. [MAV_SYS_STATUS_SENSOR](https://mavlink.io/en/messages/common.html#MAV_SYS_STATUS_SENSOR)).

## Послідовність: Режим попереднього озброєння & Кнопка безпеки

Послідовність озброєння залежить від наявності _захисного перемикача_ і контролюється параметрами [COM_PREARM_MODE](#COM_PREARM_MODE) (Режим передпуску) та [CBRK_IO_SAFETY](#CBRK_IO_SAFETY) (Вимикач безпеки введення/виведення).

Параметр [COM_PREARM_MODE](#COM_PREARM_MODE) визначає, коли/якщо передпусковий режим увімкнено ("безпечні"/приводи без збільшення газу можуть рухатися):

- _Вимкнено_: Режим передпуску відключено (немає етапу, коли тільки "безпечні"/приводи без збільшення газу увімкнені).
- _Захисний перемикач_ (За замовчуванням): Режим передпуску увімкнено за допомогою захисного перемикача.
  Якщо немає захисного перемикача, то режим передпуску не буде увімкнено.
- _Завжди_: Режим передармування активований з моменту включення живлення.

Якщо є засувка безпеки, то це буде передумовою для армування.
Якщо немає захисного перемикача, то вимикач безпеки введення/виведення ([CBRK_IO_SAFETY](#CBRK_IO_SAFETY)) повинен бути активований, і озброєння буде залежати лише від команди озброєння.

Нижче наведено деталі початкових послідовностей для різних конфігурацій.

### Default: COM_PREARM_MODE=Safety та Safety Switch

За замовчуванням використовується захисний перемикач для передпуску.
З режиму передпуску ви можете перейти до режиму озброєння, щоб активувати всі мотори/приводи.
Це відповідає: [COM_PREARM_MODE=1](#COM_PREARM_MODE) (захисний перемикач) та [CBRK_IO_SAFETY=0](#CBRK_IO_SAFETY) (вимикач безпеки введення/виведення вимкнено).

Типова послідовність запуску:

1. Увімкнення живлення.
  - Усі приводи заблоковано у беззбройному(вимкненому) положенні
  - Неможливо озброїти(збурити).
2. Перемикання безпеки натиснуто.
  - Система зараз перевіряється перед збурюванням: актуатори без збурювання можуть рухатися (наприклад, елерони).
  - Безпека системи відключена: можливість озброєння(збурення).
3. Видається команда на озброєння(збурення).

  - Система озброєна(збурена).
  - Усі мотори та приводи можуть рухатися.

### COM_PREARM_MODE=Disabled та Safety Switch

Коли режим передпуску встановлено на _Вимкнено_, увімкнення захисного перемикача не розблоковує "безпечні" приводи, але дозволяє озброїти транспортний засіб.
Це відповідає [COM_PREARM_MODE=0](#COM_PREARM_MODE) (Вимкнено) та [CBRK_IO_SAFETY=0](#CBRK_IO_SAFETY) (Вимикач безпеки введення/виведення вимкнений).

Послідовність запуску така:

1. Увімкнення живлення.
  - Усі приводи заблоковано у беззбройному(вимкненому) положенні
  - Неможливо озброїти(збурити).
2. Перемикання безпеки натиснуто.
  - _All actuators stay locked into disarmed position (same as disarmed)._
  - Безпека системи відключена: можливість озброєння(збурення).
3. Видається команда на озброєння(збурення).

  - Система озброєна(збурена).
  - Усі мотори та приводи можуть рухатися.

### COM_PREARM_MODE=Always and Safety Switch

Якщо для режиму попереднього озброєння встановлено значення _Always_, режим попереднього озброєння вмикається після ввімкнення.
Для озброєння все ще потрібний захисний перемикач.
Це відповідає [COM_PREARM_MODE=2](#COM_PREARM_MODE) (завжди) і [CBRK_IO_SAFETY=0](#CBRK_IO_SAFETY) (захисний автомат вводу-виводу вимкнено).

Послідовність запуску така:

1. Увімкнення живлення.
  - Система зараз перевіряється перед збурюванням: актуатори без збурювання можуть рухатися (наприклад, елерони).
  - Неможливо озброїти(збурити).
2. Перемикання безпеки натиснуто.
  - Безпека системи відключена: можливість озброєння(збурення).
3. Видається команда на озброєння(збурення).
  - Система озброєна(збурена).
  - Усі мотори та приводи можуть рухатися.

### COM_PREARM_MODE=Safety(Безпека) або вимкнено(Disabled) та без перемикача безпеки(No Safety Switch)

Без захисного перемикача, коли `COM_PREARM_MODE` встановлено на _Захист(Safety)_ або _Вимкнено(Disabled)_, режим передпуску не може бути увімкнений (так само, як і вимкнено).
Це відповідає [COM_PREARM_MODE=0 або 1](#COM_PREARM_MODE) (вимкнено/запобіжний перемикач) і [CBRK_IO_SAFETY=22027](#CBRK_IO_SAFETY) (ввімкнено захисний вимикач вводу-виводу).

Послідовність запуску така:

1. Увімкнення живлення.
  - Усі приводи заблоковано у беззбройному(вимкненому) положенні
  - Безпека системи відключена: можливість озброєння(збурення).
2. Видається команда на озброєння(збурення).
  - Система озброєна(збурена).
  - Усі мотори та приводи можуть рухатися.

### COM_PREARM_MODE=Завжди і без зміни безпеки

Якщо для режиму попереднього озброєння встановлено значення _Always_, режим попереднього озброєння вмикається після ввімкнення.
Це відповідає [COM_PREARM_MODE=2](#COM_PREARM_MODE) (Завжди) та [CBRK_IO_SAFETY=22027](#CBRK_IO_SAFETY) (ввімкнено вимикач безпеки введення/виведення).

Послідовність запуску така:

1. Увімкнення живлення.
  - Система зараз перевіряється перед збурюванням: актуатори без збурювання можуть рухатися (наприклад, елерони).
  - Безпека системи відключена: можливість озброєння(збурення).
2. Видається команда на озброєння(збурення).
  - Система озброєна(збурена).
  - Усі мотори та приводи можуть рухатися.

### Параметри

| Параметр                                                                                                                                           | Опис                                                                                                                                                                                                                                                                                                                                                                                                          |
| -------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a id="COM_PREARM_MODE"></a>[COM_PREARM_MODE](../advanced_config/parameter_reference.md#COM_PREARM_MODE) | Умова для входу в режим передпуску. `0`: Disabled, `1`: Safety switch (prearm mode enabled by safety switch; if no switch present cannot be enabled), `2`: Always (prearm mode enabled from power up). Default: `1` (safety button). |
| <a id="CBRK_IO_SAFETY"></a>[CBRK_IO_SAFETY](../advanced_config/parameter_reference.md#CBRK_IO_SAFETY)    | Вимикач безпеки для введення/виведення (I/O).                                                                                                                                                                                                                                                                                                                              |

<!-- Discussion:
https://github.com/PX4/PX4-Autopilot/pull/12806#discussion_r318337567
https://github.com/PX4/PX4-user_guide/issues/567#issue-486653048
-->
