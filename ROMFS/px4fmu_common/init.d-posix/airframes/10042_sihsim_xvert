#!/bin/sh
#
# @name SIH Tailsitter Duo
#
# @type VTOL
#
# @maintainer <PERSON><PERSON> <<EMAIL>>

. ${R}etc/init.d/rc.vtol_defaults

PX4_SIMULATOR=${PX4_SIMULATOR:=sihsim}
PX4_SIM_MODEL=${PX4_SIM_MODEL:=xvert}

param set-default EKF2_GPS_DELAY 0
param set-default EKF2_FUSE_BETA 0 # side slip fusion is currently not supported for tailsitters

param set-default SENS_EN_GPSSIM 1
param set-default SENS_EN_BAROSIM 1
param set-default SENS_EN_MAGSIM 1

param set-default VT_B_TRANS_DUR 5
param set-default VT_ELEV_MC_LOCK 0
param set-default VT_TYPE 0
param set-default VT_FW_DIFTHR_EN 1
param set-default VT_FW_DIFTHR_S_Y 0.3
param set-default MPC_MAN_Y_MAX 60
param set-default MC_PITCH_P 5

param set-default MAV_TYPE 19

param set-default SIH_T_MAX 2
param set-default SIH_Q_MAX 0.0165
param set-default SIH_MASS 0.2
# IXX and IZZ are inverted from the thesis as the body frame is pitched by 90 deg
param set-default SIH_IXX 0.00354
param set-default SIH_IYY 0.000625
param set-default SIH_IZZ 0.00300
param set-default SIH_IXZ 0
param set-default SIH_KDV 0.2
param set-default SIH_L_ROLL 0.145

# sih as tailsitter
param set-default SIH_VEHICLE_TYPE 2

param set-default CA_AIRFRAME 4

param set-default CA_ROTOR_COUNT 2
param set-default CA_ROTOR0_PX 0
param set-default CA_ROTOR0_PY 1
param set-default CA_ROTOR0_KM 0.05
param set-default CA_ROTOR1_PX 0
param set-default CA_ROTOR1_PY -1
param set-default CA_ROTOR1_KM 0.05

param set-default CA_SV_CS_COUNT 2
param set-default CA_SV_CS0_TYPE 5
param set-default CA_SV_CS0_TRQ_P 0.5
param set-default CA_SV_CS0_TRQ_Y -0.5
param set-default CA_SV_CS1_TYPE 6
param set-default CA_SV_CS1_TRQ_P 0.5
param set-default CA_SV_CS1_TRQ_Y 0.5

param set-default PWM_MAIN_FUNC1 101
param set-default PWM_MAIN_FUNC2 102
param set-default PWM_MAIN_FUNC5 201
param set-default PWM_MAIN_FUNC6 202
