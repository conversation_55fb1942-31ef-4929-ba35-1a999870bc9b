# Режим повернення (мультикоптер)

<img src="../../assets/site/position_fixed.svg" title="Position fix required (e.g. GPS)" width="30px" />

Режим польоту _Return_ використовується для _повернення транспортного засобу до безпеки_ по вільному шляху до безпечного пункту призначення, де він може приземлитися.

Мультикоптери за замовчуванням використовують тип повертання до [домашньої/ралійної точки](../flight_modes/return.md#home-rally-point-return-type-rtl-type-0).
У цьому типі повернення транспортні засоби підіймаються на безпечну висоту над перешкодами, якщо потрібно, прямують безпосередньо до найближчої безпечної точки посадки (точки збору або домашньої позиції), опускаються на "висоту опускання", надвіюються коротко, а потім сідають.
Висота повернення, висота зниження та затримка при посадці зазвичай встановлені на консервативні "безпечні" значення, але їх можна змінити за потреби.

Multicopter підтримує [інші типи повернення PX4](../flight_modes/return.md#return-types-rtl-type), включаючи посадку місії, маршрут місії і найближче безпечне місце призначення.
За замовчуванням рекомендується використовувати цей тип.

::: info

- Режим автоматичний - для керування апаратом не потрібно втручання користувача.
- Режим вимагає глобальної оцінки 3D-позиції (з GPS або виведеної з [локальної позиції](../ros/external_position_estimation.md#enabling-auto-modes-with-a-local-position)).
  - Літаючі транспортні засоби не можуть переключатися на цей режим без глобального положення.
  - Літаючі транспортні засоби перейдуть в режим аварійної безпеки, якщо втратять оцінку положення.
- Режим вимагає встановленої домашньої позиції.
- Режим перешкоджає зброюванню (транспортний засіб повинен бути зброєний при переході на цей режим).
- Перемикачі керування RC можуть використовуватися для зміни режимів польоту на будь-якому транспортному засобі.
- Рух палиць дистанційного керування буде [за замовчуванням](#COM_RC_OVERRIDE) змінювати транспортний засіб на [режим позиції](../flight_modes_mc/position.md), якщо не виникне критична аварія батареї.

<!-- https://github.com/PX4/PX4-Autopilot/blob/main/src/modules/commander/ModeUtil/mode_requirements.cpp -->

:::

## Технічний підсумок

Мультикоптери за замовчуванням використовують тип повертання до [домашньої/ралійної точки](../flight_modes/return.md#home-rally-point-return-type-rtl-type-0).
У цьому типі повернення вертольот:

- Піднімається на [мінімальну висоту повернення](#minimum-return-altitude) (безпечно вище будь-яких очікуваних перешкод).
  Транспортний засіб підтримує свою початкову висоту, якщо вона вище, ніж мінімальна висота повернення.
- Летить за допомогою прямого постійного шляху на безпечну точку посадки, яка буде найближчою із всіх ралі-точок та домашньої позиції.
- Прибуваючи до пункту призначення, він швидко спускається на «висоту спуску» ([RTL_DESCEND_ALT](#RTL_DESCEND_ALT)).
- Він чекає протягом налаштованого часу ([RTL_LAND_DELAY](#RTL_LAND_DELAY)), який може бути використаний для розгортання шасі посадки.
- Потім сідає на землю.

### Мінімальна висота повернення

За замовчуванням _мінімальна висота повернення_ встановлюється за допомогою [RTL_RETURN_ALT](#RTL_RETURN_ALT), і транспортний засіб просто повернеться на вищу з висоти `RTL_RETURN_ALT` або початкової висоти транспортного засобу.

Мінімальна висота повернення може бути подальше налаштована за допомогою [RTL_CONE_ANG](#RTL_CONE_ANG), яка разом з [RTL_RETURN_ALT](#RTL_RETURN_ALT) визначає напівконус, центрований навколо пункту призначення для посадки.
Кут конуса дозволяє нижчу мінімальну висоту повернення, коли режим повернення виконується близько до пункту призначення.
Це корисно, коли неподалік від місця призначення є мало перешкод, оскільки це може зменшити мінімальну висоту, яку потрібно подолати транспортному засобу перед посадкою, а отже споживання енергії та час для посадки.

![Режим повернення конуса](../../assets/flying/rtl_cone.jpg)

Конус впливає на мінімальну висоту повернення, якщо режим повернення активується всередині циліндра, що визначений максимальним радіусом конуса та `RTL_RETURN_ALT`: за межами цього циліндра використовується `RTL_RETURN_ALT`.
У межах коду мінімальна висота повернення - це перетин позиції транспортного засобу з конусом, або `RTL_DESCEND_ALT` (яка буде вище).
Іншими словами, транспортний засіб завжди повинен підніматися принаймні до значення `RTL_DESCEND_ALT`, якщо воно нижче цього значення.

Для отримання додаткової інформації про цей тип повернення див. [Тип повернення додому/району збору (RTL_TYPE=0)](../flight_modes/return.md#home-rally-point-return-type-rtl-type-0)

## Параметри

Параметри RTL перелічені в [Референсі параметрів > Режим повернення](../advanced_config/parameter_reference.md#return-mode).

Параметри, які є важливими для мультикоптерів (припускаючи, що [RTL_TYPE](../advanced_config/parameter_reference.md#RTL_TYPE) встановлено на 0), перераховані нижче.

| Параметр                                                                                                                                                                | Опис                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a id="RTL_RETURN_ALT"></a>[RTL_RETURN_ALT](../advanced_config/parameter_reference.md#RTL_RETURN_ALT)                         | Повернути висоту в метрах (за замовчуванням: 60м), коли [RTL_CONE_ANG](../advanced_config/parameter_reference.md#RTL_CONE_ANG) дорівнює 0. Якщо вже вище цієї величини, транспортний засіб повернеться на поточну висоту.                                                                                                                                                                                                                |
| <a id="RTL_DESCEND_ALT"></a>[RTL_DESCEND_ALT](../advanced_config/parameter_reference.md#RTL_DESCEND_ALT)                      | Мінімальна висота повернення і висота, на якій повітряне судно сповільнює або зупиняє своє початкове зниження з вищої висоти повернення (за замовчуванням: 30 м)                                                                                                                                                                                                                                                                                                                                                   |
| <a id="RTL_LAND_DELAY"></a>[RTL_LAND_DELAY](../advanced_config/parameter_reference.md#RTL_LAND_DELAY)                         | Час очікування на висоті `RTL_DESCEND_ALT` перед посадкою (за замовчуванням: 0.5 с) - за замовчуванням цей період короткий, щоб транспортний засіб просто сповільнився, а потім враз відразу приземлився. Якщо встановлено значення -1, система буде кружляти на висоті `RTL_DESCEND_ALT` замість посадки. Затримка надається для того, щоб ви могли налаштувати час для розгортання шасі для посадки (автоматично спрацьовує). |
| <a id="RTL_MIN_DIST"></a>[RTL_MIN_DIST](../advanced_config/parameter_reference.md#RTL_MIN_DIST)                               | Мінімальна горизонтальна відстань від домашньої позиції, щоб викликати підйом на висоту повернення, вказану "конусом". Якщо транспортний засіб горизонтально ближче, ніж ця відстань до дому, він повернеться на свою поточну висоту або `RTL_DESCEND_ALT` (вище з двох) замість того, щоб спочатку підніматися на RTL_RETURN_ALT).                                                                                                                      |
| <a id="RTL_CONE_ANG"></a>[RTL_CONE_ANG](../advanced_config/parameter_reference.md#RTL_CONE_ANG)                               | Половина кута конуса, який визначає висоту повернення транспортного засобу RTL. Значення (у градусах): 0, 25, 45, 65, 80, 90. Зауважте, що 0 означає "без конуса" (завжди повертається на висоту `RTL_RETURN_ALT` або вище), тоді як 90 показує, що транспортний засіб повинен повертатися на поточну висоту або `RTL_DESCEND_ALT` (яка вище).                                                                               |
| <a id="COM_RC_OVERRIDE"></a>[COM_RC_OVERRIDE](../advanced_config/parameter_reference.md#COM_RC_OVERRIDE)                      | Контролює, чи рух палиць на багтрековому літальному апараті (або VTOL у режимі MC) викликає зміну режиму на [Режим позиціонування](../flight_modes_mc/position.md) (крім випадку, коли транспортний засіб вирішує критичне аварійне вимкнення батареї). Це можна окремо увімкнути для автоматичних режимів та для режиму поза бортом, і в автоматичних режимах воно включено за замовчуванням.                                                                                  |
| <a id="COM_RC_STICK_OV"></a>[COM_RC_STICK_OV](../advanced_config/parameter_reference.md#COM_RC_STICK_OV) | Кількість рухів стиків, яка викликає перехід у [режим Положення](../flight_modes_mc/position.md) (якщо [COM_RC_OVERRIDE](#COM_RC_OVERRIDE) увімкнено).                                                                                                                                                                                                                                                                                                                   |

## Дивіться також

- [Режим повернення (Загальний)](../flight_modes/return.md)
- [Режим повернення (з нерухомим крилом)](../flight_modes_fw/return.md)
- [Режим повернення (VTOL)](../flight_modes_vtol/return.md)
