# Перемикання оцінювачів стану

Ця сторінка показує вам доступні оцінювачі стану та як ви можете перемикатися між ними.

:::tip
EKF2 є типовим і повинен використовуватися, якщо немає причин не робити цього (особливо на транспортних засобах з GNSS/GPS).
Q-оцінювач може бути використаний, якщо у вас немає GPS, і це часто використовується у [multicopter racers](../config_mc/racer_setup.md).
:::

## Доступні оцінювачі

Доступні оцінювачі:

- **EKF2 оцінювач стану орієнтації, положення та вітру** (_рекомендовано_) - Розширений фільтр Кальмана, який оцінює орієнтацію, тривимірну позицію/швидкість та стан вітру.

- **LPE оцінювач положення** (_не рекомендовано_) - Розширений фільтр Кальмана для тривимірної позиції та швидкості.

  :::warning
  LPE є застарілим.
  Він працює (на момент написання, у версії PX4 v1.14), але більше не підтримується або не обслуговується.

:::

- **Q оцінювач положення** - Дуже простий додатковий фільтр на основі кватерніонів для орієнтації.
  Він не вимагає наявності GPS, магнітомера або барометра.
  <!-- Q estimator is supported (at time of writing in PX4 v1.14). Test added in PX4-Autopilot/pull/21922 -->

## Як увімкнути різні оцінювачі

<!-- Changed in https://github.com/PX4/PX4-Autopilot/pull/22567 after v1.14 -->

Щоб увімкнути певний оцінювач, увімкніть його параметр та вимкніть інші:

- [EKF2_EN](../advanced_config/parameter_reference.md#EKF2_EN) - EKF2 (за замовчуванням/рекомендовано)
- [ATT_EN](../advanced_config/parameter_reference.md#ATT_EN) - Q Estimator (оцінювач орієнтації на основі кватерніонів)
- [LPE_EN](../advanced_config/parameter_reference.md#LPE_EN) - LPE (не підтримується для фіксованого крила)

:::warning
Важливо увімкнути один, і тільки один оцінювач.
Якщо увімкнено більше одного, використовується перший, хто публікує теми UOrb [vehicle_attitude](../msg_docs/VehicleAttitude.md) або [vehicle_local_position](../msg_docs/VehicleLocalPosition.md).
Якщо жоден з них не увімкнено, то теми не публікуються.
:::

:::info
Для FMU-v2 (тільки) вам також потрібно збудувати PX4 з включенням необхідного оцінювача (наприклад, EKF2: `make px4_fmu-v2`, LPE: `make px4_fmu-v2_lpe`).
Це потрібно, оскільки FMU-v2 має недостатньо ресурсів для включення обох оцінювачів.
Інші версії FMU для Pixhawk включають обидва оцінювачі.
:::
