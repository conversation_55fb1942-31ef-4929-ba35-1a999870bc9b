syntax = "proto2";
package sensor_msgs.msgs;

message IRLock
{
  required int64 time_usec	= 1;
  required int32 signature	= 2;
  required float pos_x		= 3;
  required float pos_y		= 4;
  required float size_x		= 5;
  required float size_y		= 6;
  optional double q_w = 7;
  optional double q_x = 8;
  optional double q_y = 9;
  optional double q_z = 10;
  optional double attitude_q_w = 11;
  optional double attitude_q_x = 12;
  optional double attitude_q_y = 13;
  optional double attitude_q_z = 14;
}
