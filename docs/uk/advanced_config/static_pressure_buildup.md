# Static Pressure Buildup

Повітря, що протікає над закритим транспортним засобом, може змінювати _статичний тиск_ всередині кабіни/обшивки.
В залежності від місця розташування отворів/протікань в корпусі, ви можете мати недостатній або зайвий тиск (аналогічно крилу).

Зміна тиску може впливати на виміри барометра, що призводить до неточної оцінки висоти.
Це може проявитися у втраті транспортним засобом висоти, коли він зупиняється у режимах [Висота](../flight_modes_mc/altitude.md), [Положення](../flight_modes_mc/position.md) або [Місія](../flight_modes_mc/mission.md) (коли транспортний засіб зупиняється, статичний тиск знижується, датчик повідомляє більшу висоту, і транспортний засіб компенсує це спуском).
Проблема особливо помітна на багтороторних літальних апаратах, оскільки твердокрилі транспортні засоби рухаються з більш постійною швидкістю повітря (і саме зміни швидкості повітря помітні).

Одним із рішень є використання отворів з наповненим пінопластом для зменшення накопичення (наскільки це можливо), а потім спроба динамічної калібрування для усунення будь-яких залишкових ефектів.

:::tip
Перед "виправленням" проблеми варто перевірити, що установлений пункт Z відстежує оцінену висоту (щоб перевірити, чи немає проблем з контролером).
:::

:::info
Хоча можливо виключити барометр з оцінки висоти (тобто використовувати лише висоту з GPS), це не рекомендується.
Система GPS є неточною в багатьох середовищах, особливо в міських середовищах, де відбувається відбиття сигналу від будівель.
:::

## Аналіз повітряного потоку

Ви можете змінити корпус, просвердливши отвори або заповнивши їх пінопластом.

Один із способів проаналізувати ефекти цих змін - закріпити дрон на автомобілі та їздити навколо (на відносно рівній поверхні) з відкритим корпусом на вітер.
Розглядаючи наземну станцію, ви можете оцінити ефекти змін статичного тиску, спричинених рухом, на виміряну висоту (використовуючи дорогу як "об'єктивну правду").

Цей процес дозволяє швидко вносити зміни без розрядження акумуляторів: модифікуйте дрон, їздіть/переглядайте, повторюйте!

:::tip
Мета - зменшити падіння висоти за допомогою барометра на максимальній горизонтальній швидкості менше ніж на 2 метри, перед спробою програмного калібрування.
:::

## Динамічне калібрування

Після модифікації апаратної частини ви можете використовувати параметри [EKF2_PCOEF_\*](../advanced_config/parameter_reference.md#EKF2_PCOEF_XN), щоб налаштувати очікувану варіацію барометра на основі відносної швидкості повітря.
For more information see [Using PX4's Navigation Filter (EKF2) > Correction for Static Pressure Position Error](../advanced_config/tuning_the_ecl_ekf.md#correction-for-static-pressure-position-error).

:::info
Даний підхід працює добре, якщо зв'язок між помилкою, спричиненою статичним тиском, та швидкістю змінюється лінійно.
Якщо у транспортного засобу є складніша аеродинамічна модель, цей метод буде менш ефективним.
:::
