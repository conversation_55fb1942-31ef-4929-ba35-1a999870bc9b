<?xml version="1.0" encoding="UTF-8"?>
<sdf version='1.9'>
  <model name='x500_base'>
    <pose>0 0 .24 0 0 0</pose>
    <self_collide>false</self_collide>
    <static>false</static>
    <link name="base_link">
      <inertial>
        <mass>2.0</mass>
        <inertia>
          <ixx>0.02166666666666667</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.02166666666666667</iyy>
          <iyz>0</iyz>
          <izz>0.04000000000000001</izz>
        </inertia>
      </inertial>
      <gravity>true</gravity>
      <velocity_decay/>
      <visual name="base_link_visual">
        <pose>0 0 .025 0 0 3.141592654</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/NXP-HGD-CF.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <visual name="5010_motor_base_0">
        <pose>0.174 0.174 .032 0 0 -.45</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/5010Base.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <visual name="5010_motor_base_1">
        <pose>-0.174 0.174 .032 0 0 -.45</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/5010Base.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <visual name="5010_motor_base_2">
        <pose>0.174 -0.174 .032 0 0 -.45</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/5010Base.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <visual name="5010_motor_base_3">
        <pose>-0.174 -0.174 .032 0 0 -.45</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/5010Base.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <visual name="NXP_FMUK66_FRONT">
        <pose>0.047 .001 .043 1 0 1.57</pose>
        <cast_shadows>false</cast_shadows>
        <geometry>
          <plane>
            <normal>0 0 1</normal>
            <size>.013 .007</size>
          </plane>
        </geometry>
        <material>
          <diffuse>1.0 1.0 1.0</diffuse>
          <specular>1.0 1.0 1.0</specular>
          <pbr>
            <metal>
              <albedo_map>model://x500_base/materials/textures/nxp.png</albedo_map>
            </metal>
          </pbr>
        </material>
      </visual>
      <visual name="NXP_FMUK66_TOP">
        <pose>-0.023 0 .0515 0 0 -1.57</pose>
        <cast_shadows>false</cast_shadows>
        <geometry>
          <plane>
            <normal>0 0 1</normal>
            <size>.013 .007</size>
          </plane>
        </geometry>
        <material>
          <diffuse>1.0 1.0 1.0</diffuse>
          <specular>1.0 1.0 1.0</specular>
          <pbr>
            <metal>
              <albedo_map>model://x500_base/materials/textures/nxp.png</albedo_map>
            </metal>
          </pbr>
        </material>
      </visual>
      <visual name="RDDRONE_FMUK66_TOP">
        <pose>-.03 0 .0515 0 0 -1.57</pose>
        <cast_shadows>false</cast_shadows>
        <geometry>
          <plane>
            <normal>0 0 1</normal>
            <size>.032 .0034</size>
          </plane>
        </geometry>
        <material>
          <diffuse>1.0 1.0 1.0</diffuse>
          <specular>1.0 1.0 1.0</specular>
          <pbr>
            <metal>
              <albedo_map>model://x500_base/materials/textures/rd.png</albedo_map>
            </metal>
          </pbr>
        </material>
      </visual>
      <collision name="base_link_collision_0">
        <pose>0 0 .007 0 0 0</pose>
        <geometry>
          <box>
            <size>0.35355339059327373 0.35355339059327373 0.05</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <collision name="base_link_collision_1">
        <pose>0 -0.098 -.123 -0.35 0 0</pose>
        <geometry>
          <box>
            <size>0.015 0.015 0.21</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <collision name="base_link_collision_2">
        <pose>0 0.098 -.123 0.35 0 0</pose>
        <geometry>
          <box>
            <size>0.015 0.015 0.21</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <collision name="base_link_collision_3">
        <pose>0 -0.132 -.2195 0 0 0</pose>
        <geometry>
          <box>
            <size>0.25 0.015 0.015</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <collision name="base_link_collision_4">
        <pose>0 0.132 -.2195 0 0 0</pose>
        <geometry>
          <box>
            <size>0.25 0.015 0.015</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <sensor name="air_pressure_sensor" type="air_pressure">
        <always_on>1</always_on>
        <update_rate>50</update_rate>
        <air_pressure>
          <!-- Noise modeled after BMP390 -->
          <pressure>
            <noise type="gaussian">
              <mean>0</mean>
              <stddev>3</stddev>
            </noise>
          </pressure>
        </air_pressure>
      </sensor>
      <sensor name="magnetometer_sensor" type="magnetometer">
        <always_on>1</always_on>
        <update_rate>100</update_rate>
        <magnetometer>
          <!-- TODO: update to fix units and coordinate system when we move past Harmonic -->
          <!-- See https://github.com/gazebosim/gz-sim/pull/2460 -->
          <!-- 3mgauss RMS: NOTE: noise is in tesla but sensor reports data in gauss -->
          <!-- Noise modeled after IIS2MDC -->
          <x>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </z>
        </magnetometer>
      </sensor>
      <sensor name="imu_sensor" type="imu">
        <always_on>1</always_on>
        <update_rate>250</update_rate>
        <imu>
          <angular_velocity>
            <!-- Noise modeled after IIM42653 -->
            <!-- 0.05 deg/s converted to rad/s -->
            <x>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.0008726646</stddev>
              </noise>
            </x>
            <y>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.0008726646</stddev>
              </noise>
            </y>
            <z>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.0008726646</stddev>
              </noise>
            </z>
          </angular_velocity>
          <linear_acceleration>
            <!-- Noise modeled after IIM42653 -->
            <!-- X & Y axis: 0.65 mg-rms converted to m/ss -->
            <x>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.00637</stddev>
              </noise>
            </x>
            <y>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.00637</stddev>
              </noise>
            </y>
            <!-- Z axis: 0.70 mg-rms converted to m/ss-->
            <z>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.00686</stddev>
              </noise>
            </z>
          </linear_acceleration>
        </imu>
      </sensor>
      <sensor name="navsat_sensor" type="navsat">
        <always_on>1</always_on>
        <update_rate>30</update_rate>
      </sensor>
    </link>
    <link name="rotor_0">
      <gravity>true</gravity>
      <self_collide>false</self_collide>
      <velocity_decay/>
      <pose>0.174 -0.174 0.06 0 0 0</pose>
      <inertial>
          <mass>0.016076923076923075</mass>
          <inertia>
            <ixx>3.8464910483993325e-07</ixx>
            <iyy>2.6115851691700804e-05</iyy>
            <izz>2.649858234714004e-05</izz>
          </inertia>
        </inertial>
      <visual name="rotor_0_visual">
        <pose>-0.022 -0.14638461538461536 -0.016 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>0.8461538461538461 0.8461538461538461 0.8461538461538461</scale>
            <uri>model://x500_base/meshes/1345_prop_ccw.stl</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/DarkGrey</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name="rotor_0_visual_motor_bell">
        <pose>0 0 -.032 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/5010Bell.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <collision name="rotor_0_collision">
        <pose>0 0 0 0 0 0 </pose>
        <geometry>
          <box>
            <size>0.2792307692307692 0.016923076923076923 0.0008461538461538462</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
    </link>
    <joint name="rotor_0_joint" type="revolute">
      <parent>base_link</parent>
      <child>rotor_0</child>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name="rotor_1">
      <gravity>true</gravity>
      <self_collide>false</self_collide>
      <velocity_decay/>
      <pose>-0.174 0.174 0.06 0 0 0</pose>
      <inertial>
          <mass>0.016076923076923075</mass>
          <inertia>
            <ixx>3.8464910483993325e-07</ixx>
            <iyy>2.6115851691700804e-05</iyy>
            <izz>2.649858234714004e-05</izz>
          </inertia>
        </inertial>
      <visual name="rotor_1_visual">
        <pose>-0.022 -0.14638461538461536 -0.016 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>0.8461538461538461 0.8461538461538461 0.8461538461538461</scale>
            <uri>model://x500_base/meshes/1345_prop_ccw.stl</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/DarkGrey</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name="rotor_1_visual_motor_top">
        <pose>0 0 -.032 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/5010Bell.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <collision name="rotor_1_collision">
        <pose>0 0 0 0 0 0 </pose>
        <geometry>
          <box>
            <size>0.2792307692307692 0.016923076923076923 0.0008461538461538462</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
    </link>
    <joint name="rotor_1_joint" type="revolute">
      <parent>base_link</parent>
      <child>rotor_1</child>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name="rotor_2">
      <gravity>true</gravity>
      <self_collide>false</self_collide>
      <velocity_decay/>
      <pose>0.174 0.174 0.06 0 0 0</pose>
      <inertial>
          <mass>0.016076923076923075</mass>
          <inertia>
            <ixx>3.8464910483993325e-07</ixx>
            <iyy>2.6115851691700804e-05</iyy>
            <izz>2.649858234714004e-05</izz>
          </inertia>
        </inertial>
      <visual name="rotor_2_visual">
        <pose>-0.022 -0.14638461538461536 -0.016 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>0.8461538461538461 0.8461538461538461 0.8461538461538461</scale>
            <uri>model://x500_base/meshes/1345_prop_cw.stl</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/DarkGrey</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name="rotor_2_visual_motor_top">
        <pose>0 0 -.032 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/5010Bell.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <collision name="rotor_2_collision">
        <pose>0 0 0 0 0 0 </pose>
        <geometry>
          <box>
            <size>0.2792307692307692 0.016923076923076923 0.0008461538461538462</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
    </link>
    <joint name="rotor_2_joint" type="revolute">
      <parent>base_link</parent>
      <child>rotor_2</child>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name="rotor_3">
      <gravity>true</gravity>
      <self_collide>false</self_collide>
      <velocity_decay/>
      <pose>-0.174 -0.174 0.06 0 0 0</pose>
      <inertial>
          <mass>0.016076923076923075</mass>
          <inertia>
            <ixx>3.8464910483993325e-07</ixx>
            <iyy>2.6115851691700804e-05</iyy>
            <izz>2.649858234714004e-05</izz>
          </inertia>
        </inertial>
      <visual name="rotor_3_visual">
        <pose>-0.022 -0.14638461538461536 -0.016 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>0.8461538461538461 0.8461538461538461 0.8461538461538461</scale>
            <uri>model://x500_base/meshes/1345_prop_cw.stl</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/DarkGrey</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name="rotor_3_visual_motor_top">
        <pose>0 0 -.032 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://x500_base/meshes/5010Bell.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <collision name="rotor_3_collision">
        <pose>0 0 0 0 0 0 </pose>
        <geometry>
          <box>
            <size>0.2792307692307692 0.016923076923076923 0.0008461538461538462</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
    </link>
    <joint name="rotor_3_joint" type="revolute">
      <parent>base_link</parent>
      <child>rotor_3</child>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
  </model>
</sdf>
