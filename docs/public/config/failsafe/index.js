var Module=typeof Module!="undefined"?Module:{};var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof WorkerGlobalScope!="undefined";var ENVIRONMENT_IS_NODE=typeof process=="object"&&process.versions?.node&&process.type!="renderer";if(ENVIRONMENT_IS_NODE){}var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var _scriptName=typeof document!="undefined"?document.currentScript?.src:undefined;if(typeof __filename!="undefined"){_scriptName=__filename}else if(ENVIRONMENT_IS_WORKER){_scriptName=self.location.href}var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var readAsync,readBinary;if(ENVIRONMENT_IS_NODE){var fs=require("fs");var nodePath=require("path");scriptDirectory=__dirname+"/";readBinary=filename=>{filename=isFileURI(filename)?new URL(filename):filename;var ret=fs.readFileSync(filename);return ret};readAsync=async(filename,binary=true)=>{filename=isFileURI(filename)?new URL(filename):filename;var ret=fs.readFileSync(filename,binary?undefined:"utf8");return ret};if(process.argv.length>1){thisProgram=process.argv[1].replace(/\\/g,"/")}arguments_=process.argv.slice(2);if(typeof module!="undefined"){module["exports"]=Module}quit_=(status,toThrow)=>{process.exitCode=status;throw toThrow}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){try{scriptDirectory=new URL(".",_scriptName).href}catch{}{if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=async url=>{if(isFileURI(url)){return new Promise((resolve,reject)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){resolve(xhr.response);return}reject(xhr.status)};xhr.onerror=reject;xhr.send(null)})}var response=await fetch(url,{credentials:"same-origin"});if(response.ok){return response.arrayBuffer()}throw new Error(response.status+" : "+response.url)}}}else{}var out=console.log.bind(console);var err=console.error.bind(console);var wasmBinary;var wasmMemory;var ABORT=false;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAP64,HEAPU64,HEAPF64;var runtimeInitialized=false;var isFileURI=filename=>filename.startsWith("file://");function updateMemoryViews(){var b=wasmMemory.buffer;HEAP8=new Int8Array(b);HEAP16=new Int16Array(b);HEAPU8=new Uint8Array(b);HEAPU16=new Uint16Array(b);HEAP32=new Int32Array(b);HEAPU32=new Uint32Array(b);HEAPF32=new Float32Array(b);HEAPF64=new Float64Array(b);HEAP64=new BigInt64Array(b);HEAPU64=new BigUint64Array(b)}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(onPreRuns)}function initRuntime(){runtimeInitialized=true;wasmExports["v"]()}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(onPostRuns)}var runDependencies=0;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;Module["monitorRunDependencies"]?.(runDependencies)}function removeRunDependency(id){runDependencies--;Module["monitorRunDependencies"]?.(runDependencies);if(runDependencies==0){if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){Module["onAbort"]?.(what);what="Aborted("+what+")";err(what);ABORT=true;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var wasmBinaryFile;function findWasmBinary(){return locateFile("index.wasm")}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}async function getWasmBinary(binaryFile){if(!wasmBinary){try{var response=await readAsync(binaryFile);return new Uint8Array(response)}catch{}}return getBinarySync(binaryFile)}async function instantiateArrayBuffer(binaryFile,imports){try{var binary=await getWasmBinary(binaryFile);var instance=await WebAssembly.instantiate(binary,imports);return instance}catch(reason){err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)}}async function instantiateAsync(binary,binaryFile,imports){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isFileURI(binaryFile)&&!ENVIRONMENT_IS_NODE){try{var response=fetch(binaryFile,{credentials:"same-origin"});var instantiationResult=await WebAssembly.instantiateStreaming(response,imports);return instantiationResult}catch(reason){err(`wasm streaming compile failed: ${reason}`);err("falling back to ArrayBuffer instantiation")}}return instantiateArrayBuffer(binaryFile,imports)}function getWasmImports(){return{a:wasmImports}}async function createWasm(){function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports["u"];updateMemoryViews();wasmTable=wasmExports["A"];removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){return receiveInstance(result["instance"])}var info=getWasmImports();if(Module["instantiateWasm"]){return new Promise((resolve,reject)=>{Module["instantiateWasm"](info,(mod,inst)=>{resolve(receiveInstance(mod,inst))})})}wasmBinaryFile??=findWasmBinary();var result=await instantiateAsync(wasmBinary,wasmBinaryFile,info);var exports=receiveInstantiationResult(result);return exports}class ExitStatus{name="ExitStatus";constructor(status){this.message=`Program terminated with exit(${status})`;this.status=status}}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var onPostRuns=[];var addOnPostRun=cb=>onPostRuns.push(cb);var onPreRuns=[];var addOnPreRun=cb=>onPreRuns.push(cb);var noExitRuntime=true;class ExceptionInfo{constructor(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24}set_type(type){HEAPU32[this.ptr+4>>2]=type}get_type(){return HEAPU32[this.ptr+4>>2]}set_destructor(destructor){HEAPU32[this.ptr+8>>2]=destructor}get_destructor(){return HEAPU32[this.ptr+8>>2]}set_caught(caught){caught=caught?1:0;HEAP8[this.ptr+12]=caught}get_caught(){return HEAP8[this.ptr+12]!=0}set_rethrown(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13]=rethrown}get_rethrown(){return HEAP8[this.ptr+13]!=0}init(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)}set_adjusted_ptr(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr}get_adjusted_ptr(){return HEAPU32[this.ptr+16>>2]}}var exceptionLast=0;var uncaughtExceptionCount=0;var ___cxa_throw=(ptr,type,destructor)=>{var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast};var __abort_js=()=>abort("");var embind_init_charCodes=()=>{var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes};var embind_charCodes;var readLatin1String=ptr=>{var ret="";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret};var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var BindingError=class BindingError extends Error{constructor(message){super(message);this.name="BindingError"}};var throwBindingError=message=>{throw new BindingError(message)};function sharedRegisterType(rawType,registeredInstance,options={}){var name=registeredInstance.name;if(!rawType){throwBindingError(`type "${name}" must have a positive integer typeid pointer`)}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(`Cannot register type '${name}' twice`)}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb())}}function registerType(rawType,registeredInstance,options={}){return sharedRegisterType(rawType,registeredInstance,options)}var integerReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?pointer=>HEAP8[pointer]:pointer=>HEAPU8[pointer];case 2:return signed?pointer=>HEAP16[pointer>>1]:pointer=>HEAPU16[pointer>>1];case 4:return signed?pointer=>HEAP32[pointer>>2]:pointer=>HEAPU32[pointer>>2];case 8:return signed?pointer=>HEAP64[pointer>>3]:pointer=>HEAPU64[pointer>>3];default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_bigint=(primitiveType,name,size,minRange,maxRange)=>{name=readLatin1String(name);const isUnsignedType=minRange===0n;let fromWireType=value=>value;if(isUnsignedType){const bitSize=size*8;fromWireType=value=>BigInt.asUintN(bitSize,value);maxRange=fromWireType(maxRange)}registerType(primitiveType,{name,fromWireType,toWireType:(destructors,value)=>{if(typeof value=="number"){value=BigInt(value)}return value},argPackAdvance:GenericWireTypeSize,readValueFromPointer:integerReadValueFromPointer(name,size,!isUnsignedType),destructorFunction:null})};var GenericWireTypeSize=8;var __embind_register_bool=(rawType,name,trueValue,falseValue)=>{name=readLatin1String(name);registerType(rawType,{name,fromWireType:function(wt){return!!wt},toWireType:function(destructors,o){return o?trueValue:falseValue},argPackAdvance:GenericWireTypeSize,readValueFromPointer:function(pointer){return this["fromWireType"](HEAPU8[pointer])},destructorFunction:null})};var shallowCopyInternalPointer=o=>({count:o.count,deleteScheduled:o.deleteScheduled,preservePointerOnDelete:o.preservePointerOnDelete,ptr:o.ptr,ptrType:o.ptrType,smartPtr:o.smartPtr,smartPtrType:o.smartPtrType});var throwInstanceAlreadyDeleted=obj=>{function getInstanceTypeName(handle){return handle.$$.ptrType.registeredClass.name}throwBindingError(getInstanceTypeName(obj)+" instance already deleted")};var finalizationRegistry=false;var detachFinalizer=handle=>{};var runDestructor=$$=>{if($$.smartPtr){$$.smartPtrType.rawDestructor($$.smartPtr)}else{$$.ptrType.registeredClass.rawDestructor($$.ptr)}};var releaseClassHandle=$$=>{$$.count.value-=1;var toDelete=0===$$.count.value;if(toDelete){runDestructor($$)}};var attachFinalizer=handle=>{if("undefined"===typeof FinalizationRegistry){attachFinalizer=handle=>handle;return handle}finalizationRegistry=new FinalizationRegistry(info=>{releaseClassHandle(info.$$)});attachFinalizer=handle=>{var $$=handle.$$;var hasSmartPtr=!!$$.smartPtr;if(hasSmartPtr){var info={$$};finalizationRegistry.register(handle,info,handle)}return handle};detachFinalizer=handle=>finalizationRegistry.unregister(handle);return attachFinalizer(handle)};var deletionQueue=[];var flushPendingDeletes=()=>{while(deletionQueue.length){var obj=deletionQueue.pop();obj.$$.deleteScheduled=false;obj["delete"]()}};var delayFunction;var init_ClassHandle=()=>{let proto=ClassHandle.prototype;Object.assign(proto,{isAliasOf(other){if(!(this instanceof ClassHandle)){return false}if(!(other instanceof ClassHandle)){return false}var leftClass=this.$$.ptrType.registeredClass;var left=this.$$.ptr;other.$$=other.$$;var rightClass=other.$$.ptrType.registeredClass;var right=other.$$.ptr;while(leftClass.baseClass){left=leftClass.upcast(left);leftClass=leftClass.baseClass}while(rightClass.baseClass){right=rightClass.upcast(right);rightClass=rightClass.baseClass}return leftClass===rightClass&&left===right},clone(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.preservePointerOnDelete){this.$$.count.value+=1;return this}else{var clone=attachFinalizer(Object.create(Object.getPrototypeOf(this),{$$:{value:shallowCopyInternalPointer(this.$$)}}));clone.$$.count.value+=1;clone.$$.deleteScheduled=false;return clone}},delete(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError("Object already scheduled for deletion")}detachFinalizer(this);releaseClassHandle(this.$$);if(!this.$$.preservePointerOnDelete){this.$$.smartPtr=undefined;this.$$.ptr=undefined}},isDeleted(){return!this.$$.ptr},deleteLater(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError("Object already scheduled for deletion")}deletionQueue.push(this);if(deletionQueue.length===1&&delayFunction){delayFunction(flushPendingDeletes)}this.$$.deleteScheduled=true;return this}});const symbolDispose=Symbol.dispose;if(symbolDispose){proto[symbolDispose]=proto["delete"]}};function ClassHandle(){}var createNamedFunction=(name,func)=>Object.defineProperty(func,"name",{value:name});var registeredPointers={};var ensureOverloadTable=(proto,methodName,humanName)=>{if(undefined===proto[methodName].overloadTable){var prevFunc=proto[methodName];proto[methodName]=function(...args){if(!proto[methodName].overloadTable.hasOwnProperty(args.length)){throwBindingError(`Function '${humanName}' called with an invalid number of arguments (${args.length}) - expects one of (${proto[methodName].overloadTable})!`)}return proto[methodName].overloadTable[args.length].apply(this,args)};proto[methodName].overloadTable=[];proto[methodName].overloadTable[prevFunc.argCount]=prevFunc}};var exposePublicSymbol=(name,value,numArguments)=>{if(Module.hasOwnProperty(name)){if(undefined===numArguments||undefined!==Module[name].overloadTable&&undefined!==Module[name].overloadTable[numArguments]){throwBindingError(`Cannot register public name '${name}' twice`)}ensureOverloadTable(Module,name,name);if(Module[name].overloadTable.hasOwnProperty(numArguments)){throwBindingError(`Cannot register multiple overloads of a function with the same number of arguments (${numArguments})!`)}Module[name].overloadTable[numArguments]=value}else{Module[name]=value;Module[name].argCount=numArguments}};var char_0=48;var char_9=57;var makeLegalFunctionName=name=>{name=name.replace(/[^a-zA-Z0-9_]/g,"$");var f=name.charCodeAt(0);if(f>=char_0&&f<=char_9){return`_${name}`}return name};function RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast){this.name=name;this.constructor=constructor;this.instancePrototype=instancePrototype;this.rawDestructor=rawDestructor;this.baseClass=baseClass;this.getActualType=getActualType;this.upcast=upcast;this.downcast=downcast;this.pureVirtualFunctions=[]}var upcastPointer=(ptr,ptrClass,desiredClass)=>{while(ptrClass!==desiredClass){if(!ptrClass.upcast){throwBindingError(`Expected null or instance of ${desiredClass.name}, got an instance of ${ptrClass.name}`)}ptr=ptrClass.upcast(ptr);ptrClass=ptrClass.baseClass}return ptr};var embindRepr=v=>{if(v===null){return"null"}var t=typeof v;if(t==="object"||t==="array"||t==="function"){return v.toString()}else{return""+v}};function constNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}return 0}if(!handle.$$){throwBindingError(`Cannot pass "${embindRepr(handle)}" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function genericPointerToWireType(destructors,handle){var ptr;if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}if(this.isSmartPointer){ptr=this.rawConstructor();if(destructors!==null){destructors.push(this.rawDestructor,ptr)}return ptr}else{return 0}}if(!handle||!handle.$$){throwBindingError(`Cannot pass "${embindRepr(handle)}" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}if(!this.isConst&&handle.$$.ptrType.isConst){throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name} to parameter type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);if(this.isSmartPointer){if(undefined===handle.$$.smartPtr){throwBindingError("Passing raw pointer to smart pointer is illegal")}switch(this.sharingPolicy){case 0:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr}else{throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name} to parameter type ${this.name}`)}break;case 1:ptr=handle.$$.smartPtr;break;case 2:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr}else{var clonedHandle=handle["clone"]();ptr=this.rawShare(ptr,Emval.toHandle(()=>clonedHandle["delete"]()));if(destructors!==null){destructors.push(this.rawDestructor,ptr)}}break;default:throwBindingError("Unsupporting sharing policy")}}return ptr}function nonConstNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}return 0}if(!handle.$$){throwBindingError(`Cannot pass "${embindRepr(handle)}" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}if(handle.$$.ptrType.isConst){throwBindingError(`Cannot convert argument of type ${handle.$$.ptrType.name} to parameter type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function readPointer(pointer){return this["fromWireType"](HEAPU32[pointer>>2])}var downcastPointer=(ptr,ptrClass,desiredClass)=>{if(ptrClass===desiredClass){return ptr}if(undefined===desiredClass.baseClass){return null}var rv=downcastPointer(ptr,ptrClass,desiredClass.baseClass);if(rv===null){return null}return desiredClass.downcast(rv)};var registeredInstances={};var getBasestPointer=(class_,ptr)=>{if(ptr===undefined){throwBindingError("ptr should not be undefined")}while(class_.baseClass){ptr=class_.upcast(ptr);class_=class_.baseClass}return ptr};var getInheritedInstance=(class_,ptr)=>{ptr=getBasestPointer(class_,ptr);return registeredInstances[ptr]};var InternalError=class InternalError extends Error{constructor(message){super(message);this.name="InternalError"}};var throwInternalError=message=>{throw new InternalError(message)};var makeClassHandle=(prototype,record)=>{if(!record.ptrType||!record.ptr){throwInternalError("makeClassHandle requires ptr and ptrType")}var hasSmartPtrType=!!record.smartPtrType;var hasSmartPtr=!!record.smartPtr;if(hasSmartPtrType!==hasSmartPtr){throwInternalError("Both smartPtrType and smartPtr must be specified")}record.count={value:1};return attachFinalizer(Object.create(prototype,{$$:{value:record,writable:true}}))};function RegisteredPointer_fromWireType(ptr){var rawPointer=this.getPointee(ptr);if(!rawPointer){this.destructor(ptr);return null}var registeredInstance=getInheritedInstance(this.registeredClass,rawPointer);if(undefined!==registeredInstance){if(0===registeredInstance.$$.count.value){registeredInstance.$$.ptr=rawPointer;registeredInstance.$$.smartPtr=ptr;return registeredInstance["clone"]()}else{var rv=registeredInstance["clone"]();this.destructor(ptr);return rv}}function makeDefaultHandle(){if(this.isSmartPointer){return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:rawPointer,smartPtrType:this,smartPtr:ptr})}else{return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this,ptr})}}var actualType=this.registeredClass.getActualType(rawPointer);var registeredPointerRecord=registeredPointers[actualType];if(!registeredPointerRecord){return makeDefaultHandle.call(this)}var toType;if(this.isConst){toType=registeredPointerRecord.constPointerType}else{toType=registeredPointerRecord.pointerType}var dp=downcastPointer(rawPointer,this.registeredClass,toType.registeredClass);if(dp===null){return makeDefaultHandle.call(this)}if(this.isSmartPointer){return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp,smartPtrType:this,smartPtr:ptr})}else{return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp})}}var init_RegisteredPointer=()=>{Object.assign(RegisteredPointer.prototype,{getPointee(ptr){if(this.rawGetPointee){ptr=this.rawGetPointee(ptr)}return ptr},destructor(ptr){this.rawDestructor?.(ptr)},argPackAdvance:GenericWireTypeSize,readValueFromPointer:readPointer,fromWireType:RegisteredPointer_fromWireType})};function RegisteredPointer(name,registeredClass,isReference,isConst,isSmartPointer,pointeeType,sharingPolicy,rawGetPointee,rawConstructor,rawShare,rawDestructor){this.name=name;this.registeredClass=registeredClass;this.isReference=isReference;this.isConst=isConst;this.isSmartPointer=isSmartPointer;this.pointeeType=pointeeType;this.sharingPolicy=sharingPolicy;this.rawGetPointee=rawGetPointee;this.rawConstructor=rawConstructor;this.rawShare=rawShare;this.rawDestructor=rawDestructor;if(!isSmartPointer&&registeredClass.baseClass===undefined){if(isConst){this["toWireType"]=constNoSmartPtrRawPointerToWireType;this.destructorFunction=null}else{this["toWireType"]=nonConstNoSmartPtrRawPointerToWireType;this.destructorFunction=null}}else{this["toWireType"]=genericPointerToWireType}}var replacePublicSymbol=(name,value,numArguments)=>{if(!Module.hasOwnProperty(name)){throwInternalError("Replacing nonexistent public symbol")}if(undefined!==Module[name].overloadTable&&undefined!==numArguments){Module[name].overloadTable[numArguments]=value}else{Module[name]=value;Module[name].argCount=numArguments}};var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var embind__requireFunction=(signature,rawFunction,isAsync=false)=>{signature=readLatin1String(signature);function makeDynCaller(){var rtn=getWasmTableEntry(rawFunction);return rtn}var fp=makeDynCaller();if(typeof fp!="function"){throwBindingError(`unknown function pointer with signature ${signature}: ${rawFunction}`)}return fp};class UnboundTypeError extends Error{}var getTypeName=type=>{var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv};var throwUnboundTypeError=(message,types)=>{var unboundTypes=[];var seen={};function visit(type){if(seen[type]){return}if(registeredTypes[type]){return}if(typeDependencies[type]){typeDependencies[type].forEach(visit);return}unboundTypes.push(type);seen[type]=true}types.forEach(visit);throw new UnboundTypeError(`${message}: `+unboundTypes.map(getTypeName).join([", "]))};var whenDependentTypesAreResolved=(myTypes,dependentTypes,getTypeConverters)=>{myTypes.forEach(type=>typeDependencies[type]=dependentTypes);function onComplete(typeConverters){var myTypeConverters=getTypeConverters(typeConverters);if(myTypeConverters.length!==myTypes.length){throwInternalError("Mismatched type converter count")}for(var i=0;i<myTypes.length;++i){registerType(myTypes[i],myTypeConverters[i])}}var typeConverters=new Array(dependentTypes.length);var unregisteredTypes=[];var registered=0;dependentTypes.forEach((dt,i)=>{if(registeredTypes.hasOwnProperty(dt)){typeConverters[i]=registeredTypes[dt]}else{unregisteredTypes.push(dt);if(!awaitingDependencies.hasOwnProperty(dt)){awaitingDependencies[dt]=[]}awaitingDependencies[dt].push(()=>{typeConverters[i]=registeredTypes[dt];++registered;if(registered===unregisteredTypes.length){onComplete(typeConverters)}})}});if(0===unregisteredTypes.length){onComplete(typeConverters)}};var __embind_register_class=(rawType,rawPointerType,rawConstPointerType,baseClassRawType,getActualTypeSignature,getActualType,upcastSignature,upcast,downcastSignature,downcast,name,destructorSignature,rawDestructor)=>{name=readLatin1String(name);getActualType=embind__requireFunction(getActualTypeSignature,getActualType);upcast&&=embind__requireFunction(upcastSignature,upcast);downcast&&=embind__requireFunction(downcastSignature,downcast);rawDestructor=embind__requireFunction(destructorSignature,rawDestructor);var legalFunctionName=makeLegalFunctionName(name);exposePublicSymbol(legalFunctionName,function(){throwUnboundTypeError(`Cannot construct ${name} due to unbound types`,[baseClassRawType])});whenDependentTypesAreResolved([rawType,rawPointerType,rawConstPointerType],baseClassRawType?[baseClassRawType]:[],base=>{base=base[0];var baseClass;var basePrototype;if(baseClassRawType){baseClass=base.registeredClass;basePrototype=baseClass.instancePrototype}else{basePrototype=ClassHandle.prototype}var constructor=createNamedFunction(name,function(...args){if(Object.getPrototypeOf(this)!==instancePrototype){throw new BindingError(`Use 'new' to construct ${name}`)}if(undefined===registeredClass.constructor_body){throw new BindingError(`${name} has no accessible constructor`)}var body=registeredClass.constructor_body[args.length];if(undefined===body){throw new BindingError(`Tried to invoke ctor of ${name} with invalid number of parameters (${args.length}) - expected (${Object.keys(registeredClass.constructor_body).toString()}) parameters instead!`)}return body.apply(this,args)});var instancePrototype=Object.create(basePrototype,{constructor:{value:constructor}});constructor.prototype=instancePrototype;var registeredClass=new RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast);if(registeredClass.baseClass){registeredClass.baseClass.__derivedClasses??=[];registeredClass.baseClass.__derivedClasses.push(registeredClass)}var referenceConverter=new RegisteredPointer(name,registeredClass,true,false,false);var pointerConverter=new RegisteredPointer(name+"*",registeredClass,false,false,false);var constPointerConverter=new RegisteredPointer(name+" const*",registeredClass,false,true,false);registeredPointers[rawType]={pointerType:pointerConverter,constPointerType:constPointerConverter};replacePublicSymbol(legalFunctionName,constructor);return[referenceConverter,pointerConverter,constPointerConverter]})};var heap32VectorToArray=(count,firstElement)=>{var array=[];for(var i=0;i<count;i++){array.push(HEAPU32[firstElement+i*4>>2])}return array};var runDestructors=destructors=>{while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr)}};function usesDestructorStack(argTypes){for(var i=1;i<argTypes.length;++i){if(argTypes[i]!==null&&argTypes[i].destructorFunction===undefined){return true}}return false}function createJsInvoker(argTypes,isClassMethodFunc,returns,isAsync){var needsDestructorStack=usesDestructorStack(argTypes);var argCount=argTypes.length-2;var argsList=[];var argsListWired=["fn"];if(isClassMethodFunc){argsListWired.push("thisWired")}for(var i=0;i<argCount;++i){argsList.push(`arg${i}`);argsListWired.push(`arg${i}Wired`)}argsList=argsList.join(",");argsListWired=argsListWired.join(",");var invokerFnBody=`return function (${argsList}) {\n`;if(needsDestructorStack){invokerFnBody+="var destructors = [];\n"}var dtorStack=needsDestructorStack?"destructors":"null";var args1=["humanName","throwBindingError","invoker","fn","runDestructors","retType","classParam"];if(isClassMethodFunc){invokerFnBody+=`var thisWired = classParam['toWireType'](${dtorStack}, this);\n`}for(var i=0;i<argCount;++i){invokerFnBody+=`var arg${i}Wired = argType${i}['toWireType'](${dtorStack}, arg${i});\n`;args1.push(`argType${i}`)}invokerFnBody+=(returns||isAsync?"var rv = ":"")+`invoker(${argsListWired});\n`;if(needsDestructorStack){invokerFnBody+="runDestructors(destructors);\n"}else{for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){var paramName=i===1?"thisWired":"arg"+(i-2)+"Wired";if(argTypes[i].destructorFunction!==null){invokerFnBody+=`${paramName}_dtor(${paramName});\n`;args1.push(`${paramName}_dtor`)}}}if(returns){invokerFnBody+="var ret = retType['fromWireType'](rv);\n"+"return ret;\n"}else{}invokerFnBody+="}\n";return[args1,invokerFnBody]}function craftInvokerFunction(humanName,argTypes,classType,cppInvokerFunc,cppTargetFunc,isAsync){var argCount=argTypes.length;if(argCount<2){throwBindingError("argTypes array size mismatch! Must at least get return value and 'this' types!")}var isClassMethodFunc=argTypes[1]!==null&&classType!==null;var needsDestructorStack=usesDestructorStack(argTypes);var returns=argTypes[0].name!=="void";var closureArgs=[humanName,throwBindingError,cppInvokerFunc,cppTargetFunc,runDestructors,argTypes[0],argTypes[1]];for(var i=0;i<argCount-2;++i){closureArgs.push(argTypes[i+2])}if(!needsDestructorStack){for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){if(argTypes[i].destructorFunction!==null){closureArgs.push(argTypes[i].destructorFunction)}}}let[args,invokerFnBody]=createJsInvoker(argTypes,isClassMethodFunc,returns,isAsync);var invokerFn=new Function(...args,invokerFnBody)(...closureArgs);return createNamedFunction(humanName,invokerFn)}var __embind_register_class_constructor=(rawClassType,argCount,rawArgTypesAddr,invokerSignature,invoker,rawConstructor)=>{var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);invoker=embind__requireFunction(invokerSignature,invoker);whenDependentTypesAreResolved([],[rawClassType],classType=>{classType=classType[0];var humanName=`constructor ${classType.name}`;if(undefined===classType.registeredClass.constructor_body){classType.registeredClass.constructor_body=[]}if(undefined!==classType.registeredClass.constructor_body[argCount-1]){throw new BindingError(`Cannot register multiple constructors with identical number of parameters (${argCount-1}) for class '${classType.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`)}classType.registeredClass.constructor_body[argCount-1]=()=>{throwUnboundTypeError(`Cannot construct ${classType.name} due to unbound types`,rawArgTypes)};whenDependentTypesAreResolved([],rawArgTypes,argTypes=>{argTypes.splice(1,0,null);classType.registeredClass.constructor_body[argCount-1]=craftInvokerFunction(humanName,argTypes,null,invoker,rawConstructor);return[]});return[]})};var getFunctionName=signature=>{signature=signature.trim();const argsIndex=signature.indexOf("(");if(argsIndex===-1)return signature;return signature.slice(0,argsIndex)};var __embind_register_class_function=(rawClassType,methodName,argCount,rawArgTypesAddr,invokerSignature,rawInvoker,context,isPureVirtual,isAsync,isNonnullReturn)=>{var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);methodName=readLatin1String(methodName);methodName=getFunctionName(methodName);rawInvoker=embind__requireFunction(invokerSignature,rawInvoker,isAsync);whenDependentTypesAreResolved([],[rawClassType],classType=>{classType=classType[0];var humanName=`${classType.name}.${methodName}`;if(methodName.startsWith("@@")){methodName=Symbol[methodName.substring(2)]}if(isPureVirtual){classType.registeredClass.pureVirtualFunctions.push(methodName)}function unboundTypesHandler(){throwUnboundTypeError(`Cannot call ${humanName} due to unbound types`,rawArgTypes)}var proto=classType.registeredClass.instancePrototype;var method=proto[methodName];if(undefined===method||undefined===method.overloadTable&&method.className!==classType.name&&method.argCount===argCount-2){unboundTypesHandler.argCount=argCount-2;unboundTypesHandler.className=classType.name;proto[methodName]=unboundTypesHandler}else{ensureOverloadTable(proto,methodName,humanName);proto[methodName].overloadTable[argCount-2]=unboundTypesHandler}whenDependentTypesAreResolved([],rawArgTypes,argTypes=>{var memberFunction=craftInvokerFunction(humanName,argTypes,classType,rawInvoker,context,isAsync);if(undefined===proto[methodName].overloadTable){memberFunction.argCount=argCount-2;proto[methodName]=memberFunction}else{proto[methodName].overloadTable[argCount-2]=memberFunction}return[]});return[]})};var validateThis=(this_,classType,humanName)=>{if(!(this_ instanceof Object)){throwBindingError(`${humanName} with invalid "this": ${this_}`)}if(!(this_ instanceof classType.registeredClass.constructor)){throwBindingError(`${humanName} incompatible with "this" of type ${this_.constructor.name}`)}if(!this_.$$.ptr){throwBindingError(`cannot call emscripten binding method ${humanName} on deleted object`)}return upcastPointer(this_.$$.ptr,this_.$$.ptrType.registeredClass,classType.registeredClass)};var __embind_register_class_property=(classType,fieldName,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{fieldName=readLatin1String(fieldName);getter=embind__requireFunction(getterSignature,getter);whenDependentTypesAreResolved([],[classType],classType=>{classType=classType[0];var humanName=`${classType.name}.${fieldName}`;var desc={get(){throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`,[getterReturnType,setterArgumentType])},enumerable:true,configurable:true};if(setter){desc.set=()=>throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`,[getterReturnType,setterArgumentType])}else{desc.set=v=>throwBindingError(humanName+" is a read-only property")}Object.defineProperty(classType.registeredClass.instancePrototype,fieldName,desc);whenDependentTypesAreResolved([],setter?[getterReturnType,setterArgumentType]:[getterReturnType],types=>{var getterReturnType=types[0];var desc={get(){var ptr=validateThis(this,classType,humanName+" getter");return getterReturnType["fromWireType"](getter(getterContext,ptr))},enumerable:true};if(setter){setter=embind__requireFunction(setterSignature,setter);var setterArgumentType=types[1];desc.set=function(v){var ptr=validateThis(this,classType,humanName+" setter");var destructors=[];setter(setterContext,ptr,setterArgumentType["toWireType"](destructors,v));runDestructors(destructors)}}Object.defineProperty(classType.registeredClass.instancePrototype,fieldName,desc);return[]});return[]})};var emval_freelist=[];var emval_handles=[0,1,,1,null,1,true,1,false,1];var __emval_decref=handle=>{if(handle>9&&0===--emval_handles[handle+1]){emval_handles[handle]=undefined;emval_freelist.push(handle)}};var Emval={toValue:handle=>{if(!handle){throwBindingError(`Cannot use deleted val. handle = ${handle}`)}return emval_handles[handle]},toHandle:value=>{switch(value){case undefined:return 2;case null:return 4;case true:return 6;case false:return 8;default:{const handle=emval_freelist.pop()||emval_handles.length;emval_handles[handle]=value;emval_handles[handle+1]=1;return handle}}}};var EmValType={name:"emscripten::val",fromWireType:handle=>{var rv=Emval.toValue(handle);__emval_decref(handle);return rv},toWireType:(destructors,value)=>Emval.toHandle(value),argPackAdvance:GenericWireTypeSize,readValueFromPointer:readPointer,destructorFunction:null};var __embind_register_emval=rawType=>registerType(rawType,EmValType);var floatReadValueFromPointer=(name,width)=>{switch(width){case 4:return function(pointer){return this["fromWireType"](HEAPF32[pointer>>2])};case 8:return function(pointer){return this["fromWireType"](HEAPF64[pointer>>3])};default:throw new TypeError(`invalid float width (${width}): ${name}`)}};var __embind_register_float=(rawType,name,size)=>{name=readLatin1String(name);registerType(rawType,{name,fromWireType:value=>value,toWireType:(destructors,value)=>value,argPackAdvance:GenericWireTypeSize,readValueFromPointer:floatReadValueFromPointer(name,size),destructorFunction:null})};var __embind_register_function=(name,argCount,rawArgTypesAddr,signature,rawInvoker,fn,isAsync,isNonnullReturn)=>{var argTypes=heap32VectorToArray(argCount,rawArgTypesAddr);name=readLatin1String(name);name=getFunctionName(name);rawInvoker=embind__requireFunction(signature,rawInvoker,isAsync);exposePublicSymbol(name,function(){throwUnboundTypeError(`Cannot call ${name} due to unbound types`,argTypes)},argCount-1);whenDependentTypesAreResolved([],argTypes,argTypes=>{var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));replacePublicSymbol(name,craftInvokerFunction(name,invokerArgsArray,null,rawInvoker,fn,isAsync),argCount-1);return[]})};var __embind_register_integer=(primitiveType,name,size,minRange,maxRange)=>{name=readLatin1String(name);const isUnsignedType=minRange===0;let fromWireType=value=>value;if(isUnsignedType){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift;maxRange=fromWireType(maxRange)}registerType(primitiveType,{name,fromWireType,toWireType:(destructors,value)=>value,argPackAdvance:GenericWireTypeSize,readValueFromPointer:integerReadValueFromPointer(name,size,minRange!==0),destructorFunction:null})};var __embind_register_memory_view=(rawType,dataTypeIndex,name)=>{var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,BigInt64Array,BigUint64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){var size=HEAPU32[handle>>2];var data=HEAPU32[handle+4>>2];return new TA(HEAP8.buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name,fromWireType:decodeMemoryView,argPackAdvance:GenericWireTypeSize,readValueFromPointer:decodeMemoryView},{ignoreDuplicateRegistrations:true})};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder:undefined;var UTF8ArrayToString=(heapOrArray,idx=0,maxBytesToRead=NaN)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):"";var __embind_register_std_string=(rawType,name)=>{name=readLatin1String(name);var stdStringIsUTF8=true;registerType(rawType,{name,fromWireType(value){var length=HEAPU32[value>>2];var payload=value+4;var str;if(stdStringIsUTF8){var decodeStartPtr=payload;for(var i=0;i<=length;++i){var currentBytePtr=payload+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[payload+i])}str=a.join("")}_free(value);return str},toWireType(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var length;var valueIsOfTypeString=typeof value=="string";if(!(valueIsOfTypeString||ArrayBuffer.isView(value)&&value.BYTES_PER_ELEMENT==1)){throwBindingError("Cannot pass non-string to std::string")}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value)}else{length=value.length}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>2]=length;if(valueIsOfTypeString){if(stdStringIsUTF8){stringToUTF8(value,ptr,length+1)}else{for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(base);throwBindingError("String has UTF-16 code units that do not fit in 8 bits")}HEAPU8[ptr+i]=charCode}}}else{HEAPU8.set(value,ptr)}if(destructors!==null){destructors.push(_free,base)}return base},argPackAdvance:GenericWireTypeSize,readValueFromPointer:readPointer,destructorFunction(ptr){_free(ptr)}})};var UTF16Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):undefined;var UTF16ToString=(ptr,maxBytesToRead)=>{var idx=ptr>>1;var maxIdx=idx+maxBytesToRead/2;var endIdx=idx;while(!(endIdx>=maxIdx)&&HEAPU16[endIdx])++endIdx;if(endIdx-idx>16&&UTF16Decoder)return UTF16Decoder.decode(HEAPU16.subarray(idx,endIdx));var str="";for(var i=idx;!(i>=maxIdx);++i){var codeUnit=HEAPU16[i];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str};var stringToUTF16=(str,outPtr,maxBytesToWrite)=>{maxBytesToWrite??=2147483647;if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr};var lengthBytesUTF16=str=>str.length*2;var UTF32ToString=(ptr,maxBytesToRead)=>{var i=0;var str="";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str};var stringToUTF32=(str,outPtr,maxBytesToWrite)=>{maxBytesToWrite??=2147483647;if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr};var lengthBytesUTF32=str=>{var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len};var __embind_register_std_wstring=(rawType,charSize,name)=>{name=readLatin1String(name);var decodeString,encodeString,readCharAt,lengthBytesUTF;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;readCharAt=pointer=>HEAPU16[pointer>>1]}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;readCharAt=pointer=>HEAPU32[pointer>>2]}registerType(rawType,{name,fromWireType:value=>{var length=HEAPU32[value>>2];var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||readCharAt(currentBytePtr)==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},toWireType:(destructors,value)=>{if(!(typeof value=="string")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length/charSize;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},argPackAdvance:GenericWireTypeSize,readValueFromPointer:readPointer,destructorFunction(ptr){_free(ptr)}})};var __embind_register_void=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{isVoid:true,name,argPackAdvance:0,fromWireType:()=>undefined,toWireType:(destructors,o)=>undefined})};var requireRegisteredType=(rawType,humanName)=>{var impl=registeredTypes[rawType];if(undefined===impl){throwBindingError(`${humanName} has unknown type ${getTypeName(rawType)}`)}return impl};var __emval_take_value=(type,arg)=>{type=requireRegisteredType(type,"_emval_take_value");var v=type["readValueFromPointer"](arg);return Emval.toHandle(v)};var _emscripten_date_now=()=>Date.now();var abortOnCannotGrowMemory=requestedSize=>{abort("OOM")};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;abortOnCannotGrowMemory(requestedSize)};var printCharBuffers=[null,[],[]];var printChar=(stream,curr)=>{var buffer=printCharBuffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer));buffer.length=0}else{buffer.push(curr)}};var _fd_write=(fd,iov,iovcnt,pnum)=>{var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){printChar(fd,HEAPU8[ptr+j])}num+=len}HEAPU32[pnum>>2]=num;return 0};embind_init_charCodes();init_ClassHandle();init_RegisteredPointer();{if(Module["noExitRuntime"])noExitRuntime=Module["noExitRuntime"];if(Module["print"])out=Module["print"];if(Module["printErr"])err=Module["printErr"];if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"]}var wasmImports={f:___cxa_throw,p:__abort_js,k:__embind_register_bigint,m:__embind_register_bool,l:__embind_register_class,h:__embind_register_class_constructor,e:__embind_register_class_function,a:__embind_register_class_property,r:__embind_register_emval,j:__embind_register_float,c:__embind_register_function,d:__embind_register_integer,b:__embind_register_memory_view,s:__embind_register_std_string,g:__embind_register_std_wstring,n:__embind_register_void,o:__emval_take_value,t:_emscripten_date_now,q:_emscripten_resize_heap,i:_fd_write};var wasmExports;createWasm();var ___wasm_call_ctors=()=>(___wasm_call_ctors=wasmExports["v"])();var _param_get=Module["_param_get"]=(a0,a1)=>(_param_get=Module["_param_get"]=wasmExports["w"])(a0,a1);var _param_set_used=Module["_param_set_used"]=a0=>(_param_set_used=Module["_param_set_used"]=wasmExports["x"])(a0);var __Znwm=Module["__Znwm"]=a0=>(__Znwm=Module["__Znwm"]=wasmExports["y"])(a0);var __ZdlPvm=Module["__ZdlPvm"]=(a0,a1)=>(__ZdlPvm=Module["__ZdlPvm"]=wasmExports["z"])(a0,a1);var _malloc=a0=>(_malloc=wasmExports["B"])(a0);var __ZNSt12length_errorD1Ev=Module["__ZNSt12length_errorD1Ev"]=a0=>(__ZNSt12length_errorD1Ev=Module["__ZNSt12length_errorD1Ev"]=wasmExports["C"])(a0);var ___cxa_allocate_exception=Module["___cxa_allocate_exception"]=a0=>(___cxa_allocate_exception=Module["___cxa_allocate_exception"]=wasmExports["D"])(a0);var __ZNSt20bad_array_new_lengthD1Ev=Module["__ZNSt20bad_array_new_lengthD1Ev"]=a0=>(__ZNSt20bad_array_new_lengthD1Ev=Module["__ZNSt20bad_array_new_lengthD1Ev"]=wasmExports["E"])(a0);var __ZNSt20bad_array_new_lengthC1Ev=Module["__ZNSt20bad_array_new_lengthC1Ev"]=a0=>(__ZNSt20bad_array_new_lengthC1Ev=Module["__ZNSt20bad_array_new_lengthC1Ev"]=wasmExports["F"])(a0);var __ZNSt12out_of_rangeD1Ev=Module["__ZNSt12out_of_rangeD1Ev"]=a0=>(__ZNSt12out_of_rangeD1Ev=Module["__ZNSt12out_of_rangeD1Ev"]=wasmExports["G"])(a0);var ___cxa_pure_virtual=Module["___cxa_pure_virtual"]=()=>(___cxa_pure_virtual=Module["___cxa_pure_virtual"]=wasmExports["H"])();var ___getTypeName=a0=>(___getTypeName=wasmExports["I"])(a0);var __ZNSt9exceptionD2Ev=Module["__ZNSt9exceptionD2Ev"]=a0=>(__ZNSt9exceptionD2Ev=Module["__ZNSt9exceptionD2Ev"]=wasmExports["J"])(a0);var __emscripten_memcpy_bulkmem=Module["__emscripten_memcpy_bulkmem"]=(a0,a1,a2)=>(__emscripten_memcpy_bulkmem=Module["__emscripten_memcpy_bulkmem"]=wasmExports["K"])(a0,a1,a2);var _emscripten_stack_get_end=Module["_emscripten_stack_get_end"]=()=>(_emscripten_stack_get_end=Module["_emscripten_stack_get_end"]=wasmExports["L"])();var _emscripten_stack_get_base=Module["_emscripten_stack_get_base"]=()=>(_emscripten_stack_get_base=Module["_emscripten_stack_get_base"]=wasmExports["M"])();var _free=a0=>(_free=wasmExports["N"])(a0);var _emscripten_stack_init=Module["_emscripten_stack_init"]=()=>(_emscripten_stack_init=Module["_emscripten_stack_init"]=wasmExports["O"])();var _emscripten_stack_set_limits=Module["_emscripten_stack_set_limits"]=(a0,a1)=>(_emscripten_stack_set_limits=Module["_emscripten_stack_set_limits"]=wasmExports["P"])(a0,a1);var _emscripten_stack_get_free=Module["_emscripten_stack_get_free"]=()=>(_emscripten_stack_get_free=Module["_emscripten_stack_get_free"]=wasmExports["Q"])();var __ZSt15get_new_handlerv=Module["__ZSt15get_new_handlerv"]=()=>(__ZSt15get_new_handlerv=Module["__ZSt15get_new_handlerv"]=wasmExports["R"])();var __Znam=Module["__Znam"]=a0=>(__Znam=Module["__Znam"]=wasmExports["S"])(a0);var __ZdlPv=Module["__ZdlPv"]=a0=>(__ZdlPv=Module["__ZdlPv"]=wasmExports["T"])(a0);var __ZdaPv=Module["__ZdaPv"]=a0=>(__ZdaPv=Module["__ZdaPv"]=wasmExports["U"])(a0);var __ZdaPvm=Module["__ZdaPvm"]=(a0,a1)=>(__ZdaPvm=Module["__ZdaPvm"]=wasmExports["V"])(a0,a1);var __ZnwmSt11align_val_t=Module["__ZnwmSt11align_val_t"]=(a0,a1)=>(__ZnwmSt11align_val_t=Module["__ZnwmSt11align_val_t"]=wasmExports["W"])(a0,a1);var __ZnamSt11align_val_t=Module["__ZnamSt11align_val_t"]=(a0,a1)=>(__ZnamSt11align_val_t=Module["__ZnamSt11align_val_t"]=wasmExports["X"])(a0,a1);var __ZdlPvSt11align_val_t=Module["__ZdlPvSt11align_val_t"]=(a0,a1)=>(__ZdlPvSt11align_val_t=Module["__ZdlPvSt11align_val_t"]=wasmExports["Y"])(a0,a1);var __ZdlPvmSt11align_val_t=Module["__ZdlPvmSt11align_val_t"]=(a0,a1,a2)=>(__ZdlPvmSt11align_val_t=Module["__ZdlPvmSt11align_val_t"]=wasmExports["Z"])(a0,a1,a2);var __ZdaPvSt11align_val_t=Module["__ZdaPvSt11align_val_t"]=(a0,a1)=>(__ZdaPvSt11align_val_t=Module["__ZdaPvSt11align_val_t"]=wasmExports["_"])(a0,a1);var __ZdaPvmSt11align_val_t=Module["__ZdaPvmSt11align_val_t"]=(a0,a1,a2)=>(__ZdaPvmSt11align_val_t=Module["__ZdaPvmSt11align_val_t"]=wasmExports["$"])(a0,a1,a2);var __ZSt14set_unexpectedPFvvE=Module["__ZSt14set_unexpectedPFvvE"]=a0=>(__ZSt14set_unexpectedPFvvE=Module["__ZSt14set_unexpectedPFvvE"]=wasmExports["aa"])(a0);var __ZSt13set_terminatePFvvE=Module["__ZSt13set_terminatePFvvE"]=a0=>(__ZSt13set_terminatePFvvE=Module["__ZSt13set_terminatePFvvE"]=wasmExports["ba"])(a0);var __ZSt15set_new_handlerPFvvE=Module["__ZSt15set_new_handlerPFvvE"]=a0=>(__ZSt15set_new_handlerPFvvE=Module["__ZSt15set_new_handlerPFvvE"]=wasmExports["ca"])(a0);var __ZSt14get_unexpectedv=Module["__ZSt14get_unexpectedv"]=()=>(__ZSt14get_unexpectedv=Module["__ZSt14get_unexpectedv"]=wasmExports["da"])();var __ZSt10unexpectedv=Module["__ZSt10unexpectedv"]=()=>(__ZSt10unexpectedv=Module["__ZSt10unexpectedv"]=wasmExports["ea"])();var __ZSt13get_terminatev=Module["__ZSt13get_terminatev"]=()=>(__ZSt13get_terminatev=Module["__ZSt13get_terminatev"]=wasmExports["fa"])();var __ZSt9terminatev=Module["__ZSt9terminatev"]=()=>(__ZSt9terminatev=Module["__ZSt9terminatev"]=wasmExports["ga"])();var ___cxa_current_primary_exception=Module["___cxa_current_primary_exception"]=()=>(___cxa_current_primary_exception=Module["___cxa_current_primary_exception"]=wasmExports["ha"])();var ___cxa_rethrow_primary_exception=Module["___cxa_rethrow_primary_exception"]=a0=>(___cxa_rethrow_primary_exception=Module["___cxa_rethrow_primary_exception"]=wasmExports["ia"])(a0);var ___cxa_uncaught_exception=Module["___cxa_uncaught_exception"]=()=>(___cxa_uncaught_exception=Module["___cxa_uncaught_exception"]=wasmExports["ja"])();var ___cxa_uncaught_exceptions=Module["___cxa_uncaught_exceptions"]=()=>(___cxa_uncaught_exceptions=Module["___cxa_uncaught_exceptions"]=wasmExports["ka"])();var ___cxa_free_exception=Module["___cxa_free_exception"]=a0=>(___cxa_free_exception=Module["___cxa_free_exception"]=wasmExports["la"])(a0);var ___cxa_init_primary_exception=Module["___cxa_init_primary_exception"]=(a0,a1,a2)=>(___cxa_init_primary_exception=Module["___cxa_init_primary_exception"]=wasmExports["ma"])(a0,a1,a2);var ___cxa_deleted_virtual=Module["___cxa_deleted_virtual"]=()=>(___cxa_deleted_virtual=Module["___cxa_deleted_virtual"]=wasmExports["na"])();var ___dynamic_cast=Module["___dynamic_cast"]=(a0,a1,a2,a3)=>(___dynamic_cast=Module["___dynamic_cast"]=wasmExports["oa"])(a0,a1,a2,a3);var __ZNSt9type_infoD2Ev=Module["__ZNSt9type_infoD2Ev"]=a0=>(__ZNSt9type_infoD2Ev=Module["__ZNSt9type_infoD2Ev"]=wasmExports["pa"])(a0);var __ZNSt9exceptionD0Ev=Module["__ZNSt9exceptionD0Ev"]=a0=>(__ZNSt9exceptionD0Ev=Module["__ZNSt9exceptionD0Ev"]=wasmExports["qa"])(a0);var __ZNSt9exceptionD1Ev=Module["__ZNSt9exceptionD1Ev"]=a0=>(__ZNSt9exceptionD1Ev=Module["__ZNSt9exceptionD1Ev"]=wasmExports["ra"])(a0);var __ZNKSt9exception4whatEv=Module["__ZNKSt9exception4whatEv"]=a0=>(__ZNKSt9exception4whatEv=Module["__ZNKSt9exception4whatEv"]=wasmExports["sa"])(a0);var __ZNSt13bad_exceptionD0Ev=Module["__ZNSt13bad_exceptionD0Ev"]=a0=>(__ZNSt13bad_exceptionD0Ev=Module["__ZNSt13bad_exceptionD0Ev"]=wasmExports["ta"])(a0);var __ZNSt13bad_exceptionD1Ev=Module["__ZNSt13bad_exceptionD1Ev"]=a0=>(__ZNSt13bad_exceptionD1Ev=Module["__ZNSt13bad_exceptionD1Ev"]=wasmExports["ua"])(a0);var __ZNKSt13bad_exception4whatEv=Module["__ZNKSt13bad_exception4whatEv"]=a0=>(__ZNKSt13bad_exception4whatEv=Module["__ZNKSt13bad_exception4whatEv"]=wasmExports["va"])(a0);var __ZNSt9bad_allocC2Ev=Module["__ZNSt9bad_allocC2Ev"]=a0=>(__ZNSt9bad_allocC2Ev=Module["__ZNSt9bad_allocC2Ev"]=wasmExports["wa"])(a0);var __ZNSt9bad_allocD0Ev=Module["__ZNSt9bad_allocD0Ev"]=a0=>(__ZNSt9bad_allocD0Ev=Module["__ZNSt9bad_allocD0Ev"]=wasmExports["xa"])(a0);var __ZNSt9bad_allocD1Ev=Module["__ZNSt9bad_allocD1Ev"]=a0=>(__ZNSt9bad_allocD1Ev=Module["__ZNSt9bad_allocD1Ev"]=wasmExports["ya"])(a0);var __ZNKSt9bad_alloc4whatEv=Module["__ZNKSt9bad_alloc4whatEv"]=a0=>(__ZNKSt9bad_alloc4whatEv=Module["__ZNKSt9bad_alloc4whatEv"]=wasmExports["za"])(a0);var __ZNSt20bad_array_new_lengthC2Ev=Module["__ZNSt20bad_array_new_lengthC2Ev"]=a0=>(__ZNSt20bad_array_new_lengthC2Ev=Module["__ZNSt20bad_array_new_lengthC2Ev"]=wasmExports["Aa"])(a0);var __ZNSt20bad_array_new_lengthD0Ev=Module["__ZNSt20bad_array_new_lengthD0Ev"]=a0=>(__ZNSt20bad_array_new_lengthD0Ev=Module["__ZNSt20bad_array_new_lengthD0Ev"]=wasmExports["Ba"])(a0);var __ZNKSt20bad_array_new_length4whatEv=Module["__ZNKSt20bad_array_new_length4whatEv"]=a0=>(__ZNKSt20bad_array_new_length4whatEv=Module["__ZNKSt20bad_array_new_length4whatEv"]=wasmExports["Ca"])(a0);var __ZNSt13bad_exceptionD2Ev=Module["__ZNSt13bad_exceptionD2Ev"]=a0=>(__ZNSt13bad_exceptionD2Ev=Module["__ZNSt13bad_exceptionD2Ev"]=wasmExports["Da"])(a0);var __ZNSt9bad_allocC1Ev=Module["__ZNSt9bad_allocC1Ev"]=a0=>(__ZNSt9bad_allocC1Ev=Module["__ZNSt9bad_allocC1Ev"]=wasmExports["Ea"])(a0);var __ZNSt9bad_allocD2Ev=Module["__ZNSt9bad_allocD2Ev"]=a0=>(__ZNSt9bad_allocD2Ev=Module["__ZNSt9bad_allocD2Ev"]=wasmExports["Fa"])(a0);var __ZNSt20bad_array_new_lengthD2Ev=Module["__ZNSt20bad_array_new_lengthD2Ev"]=a0=>(__ZNSt20bad_array_new_lengthD2Ev=Module["__ZNSt20bad_array_new_lengthD2Ev"]=wasmExports["Ga"])(a0);var __ZNSt11logic_errorD2Ev=Module["__ZNSt11logic_errorD2Ev"]=a0=>(__ZNSt11logic_errorD2Ev=Module["__ZNSt11logic_errorD2Ev"]=wasmExports["Ha"])(a0);var __ZNSt11logic_errorD0Ev=Module["__ZNSt11logic_errorD0Ev"]=a0=>(__ZNSt11logic_errorD0Ev=Module["__ZNSt11logic_errorD0Ev"]=wasmExports["Ia"])(a0);var __ZNSt11logic_errorD1Ev=Module["__ZNSt11logic_errorD1Ev"]=a0=>(__ZNSt11logic_errorD1Ev=Module["__ZNSt11logic_errorD1Ev"]=wasmExports["Ja"])(a0);var __ZNKSt11logic_error4whatEv=Module["__ZNKSt11logic_error4whatEv"]=a0=>(__ZNKSt11logic_error4whatEv=Module["__ZNKSt11logic_error4whatEv"]=wasmExports["Ka"])(a0);var __ZNSt13runtime_errorD2Ev=Module["__ZNSt13runtime_errorD2Ev"]=a0=>(__ZNSt13runtime_errorD2Ev=Module["__ZNSt13runtime_errorD2Ev"]=wasmExports["La"])(a0);var __ZNSt13runtime_errorD0Ev=Module["__ZNSt13runtime_errorD0Ev"]=a0=>(__ZNSt13runtime_errorD0Ev=Module["__ZNSt13runtime_errorD0Ev"]=wasmExports["Ma"])(a0);var __ZNSt13runtime_errorD1Ev=Module["__ZNSt13runtime_errorD1Ev"]=a0=>(__ZNSt13runtime_errorD1Ev=Module["__ZNSt13runtime_errorD1Ev"]=wasmExports["Na"])(a0);var __ZNKSt13runtime_error4whatEv=Module["__ZNKSt13runtime_error4whatEv"]=a0=>(__ZNKSt13runtime_error4whatEv=Module["__ZNKSt13runtime_error4whatEv"]=wasmExports["Oa"])(a0);var __ZNSt12domain_errorD0Ev=Module["__ZNSt12domain_errorD0Ev"]=a0=>(__ZNSt12domain_errorD0Ev=Module["__ZNSt12domain_errorD0Ev"]=wasmExports["Pa"])(a0);var __ZNSt12domain_errorD1Ev=Module["__ZNSt12domain_errorD1Ev"]=a0=>(__ZNSt12domain_errorD1Ev=Module["__ZNSt12domain_errorD1Ev"]=wasmExports["Qa"])(a0);var __ZNSt16invalid_argumentD0Ev=Module["__ZNSt16invalid_argumentD0Ev"]=a0=>(__ZNSt16invalid_argumentD0Ev=Module["__ZNSt16invalid_argumentD0Ev"]=wasmExports["Ra"])(a0);var __ZNSt16invalid_argumentD1Ev=Module["__ZNSt16invalid_argumentD1Ev"]=a0=>(__ZNSt16invalid_argumentD1Ev=Module["__ZNSt16invalid_argumentD1Ev"]=wasmExports["Sa"])(a0);var __ZNSt12length_errorD0Ev=Module["__ZNSt12length_errorD0Ev"]=a0=>(__ZNSt12length_errorD0Ev=Module["__ZNSt12length_errorD0Ev"]=wasmExports["Ta"])(a0);var __ZNSt12out_of_rangeD0Ev=Module["__ZNSt12out_of_rangeD0Ev"]=a0=>(__ZNSt12out_of_rangeD0Ev=Module["__ZNSt12out_of_rangeD0Ev"]=wasmExports["Ua"])(a0);var __ZNSt11range_errorD0Ev=Module["__ZNSt11range_errorD0Ev"]=a0=>(__ZNSt11range_errorD0Ev=Module["__ZNSt11range_errorD0Ev"]=wasmExports["Va"])(a0);var __ZNSt11range_errorD1Ev=Module["__ZNSt11range_errorD1Ev"]=a0=>(__ZNSt11range_errorD1Ev=Module["__ZNSt11range_errorD1Ev"]=wasmExports["Wa"])(a0);var __ZNSt14overflow_errorD0Ev=Module["__ZNSt14overflow_errorD0Ev"]=a0=>(__ZNSt14overflow_errorD0Ev=Module["__ZNSt14overflow_errorD0Ev"]=wasmExports["Xa"])(a0);var __ZNSt14overflow_errorD1Ev=Module["__ZNSt14overflow_errorD1Ev"]=a0=>(__ZNSt14overflow_errorD1Ev=Module["__ZNSt14overflow_errorD1Ev"]=wasmExports["Ya"])(a0);var __ZNSt15underflow_errorD0Ev=Module["__ZNSt15underflow_errorD0Ev"]=a0=>(__ZNSt15underflow_errorD0Ev=Module["__ZNSt15underflow_errorD0Ev"]=wasmExports["Za"])(a0);var __ZNSt15underflow_errorD1Ev=Module["__ZNSt15underflow_errorD1Ev"]=a0=>(__ZNSt15underflow_errorD1Ev=Module["__ZNSt15underflow_errorD1Ev"]=wasmExports["_a"])(a0);var __ZNSt12domain_errorD2Ev=Module["__ZNSt12domain_errorD2Ev"]=a0=>(__ZNSt12domain_errorD2Ev=Module["__ZNSt12domain_errorD2Ev"]=wasmExports["$a"])(a0);var __ZNSt16invalid_argumentD2Ev=Module["__ZNSt16invalid_argumentD2Ev"]=a0=>(__ZNSt16invalid_argumentD2Ev=Module["__ZNSt16invalid_argumentD2Ev"]=wasmExports["ab"])(a0);var __ZNSt12length_errorD2Ev=Module["__ZNSt12length_errorD2Ev"]=a0=>(__ZNSt12length_errorD2Ev=Module["__ZNSt12length_errorD2Ev"]=wasmExports["bb"])(a0);var __ZNSt12out_of_rangeD2Ev=Module["__ZNSt12out_of_rangeD2Ev"]=a0=>(__ZNSt12out_of_rangeD2Ev=Module["__ZNSt12out_of_rangeD2Ev"]=wasmExports["cb"])(a0);var __ZNSt11range_errorD2Ev=Module["__ZNSt11range_errorD2Ev"]=a0=>(__ZNSt11range_errorD2Ev=Module["__ZNSt11range_errorD2Ev"]=wasmExports["db"])(a0);var __ZNSt14overflow_errorD2Ev=Module["__ZNSt14overflow_errorD2Ev"]=a0=>(__ZNSt14overflow_errorD2Ev=Module["__ZNSt14overflow_errorD2Ev"]=wasmExports["eb"])(a0);var __ZNSt15underflow_errorD2Ev=Module["__ZNSt15underflow_errorD2Ev"]=a0=>(__ZNSt15underflow_errorD2Ev=Module["__ZNSt15underflow_errorD2Ev"]=wasmExports["fb"])(a0);var __ZNSt9type_infoD0Ev=Module["__ZNSt9type_infoD0Ev"]=a0=>(__ZNSt9type_infoD0Ev=Module["__ZNSt9type_infoD0Ev"]=wasmExports["gb"])(a0);var __ZNSt9type_infoD1Ev=Module["__ZNSt9type_infoD1Ev"]=a0=>(__ZNSt9type_infoD1Ev=Module["__ZNSt9type_infoD1Ev"]=wasmExports["hb"])(a0);var __ZNSt8bad_castC2Ev=Module["__ZNSt8bad_castC2Ev"]=a0=>(__ZNSt8bad_castC2Ev=Module["__ZNSt8bad_castC2Ev"]=wasmExports["ib"])(a0);var __ZNSt8bad_castD2Ev=Module["__ZNSt8bad_castD2Ev"]=a0=>(__ZNSt8bad_castD2Ev=Module["__ZNSt8bad_castD2Ev"]=wasmExports["jb"])(a0);var __ZNSt8bad_castD0Ev=Module["__ZNSt8bad_castD0Ev"]=a0=>(__ZNSt8bad_castD0Ev=Module["__ZNSt8bad_castD0Ev"]=wasmExports["kb"])(a0);var __ZNSt8bad_castD1Ev=Module["__ZNSt8bad_castD1Ev"]=a0=>(__ZNSt8bad_castD1Ev=Module["__ZNSt8bad_castD1Ev"]=wasmExports["lb"])(a0);var __ZNKSt8bad_cast4whatEv=Module["__ZNKSt8bad_cast4whatEv"]=a0=>(__ZNKSt8bad_cast4whatEv=Module["__ZNKSt8bad_cast4whatEv"]=wasmExports["mb"])(a0);var __ZNSt10bad_typeidC2Ev=Module["__ZNSt10bad_typeidC2Ev"]=a0=>(__ZNSt10bad_typeidC2Ev=Module["__ZNSt10bad_typeidC2Ev"]=wasmExports["nb"])(a0);var __ZNSt10bad_typeidD2Ev=Module["__ZNSt10bad_typeidD2Ev"]=a0=>(__ZNSt10bad_typeidD2Ev=Module["__ZNSt10bad_typeidD2Ev"]=wasmExports["ob"])(a0);var __ZNSt10bad_typeidD0Ev=Module["__ZNSt10bad_typeidD0Ev"]=a0=>(__ZNSt10bad_typeidD0Ev=Module["__ZNSt10bad_typeidD0Ev"]=wasmExports["pb"])(a0);var __ZNSt10bad_typeidD1Ev=Module["__ZNSt10bad_typeidD1Ev"]=a0=>(__ZNSt10bad_typeidD1Ev=Module["__ZNSt10bad_typeidD1Ev"]=wasmExports["qb"])(a0);var __ZNKSt10bad_typeid4whatEv=Module["__ZNKSt10bad_typeid4whatEv"]=a0=>(__ZNKSt10bad_typeid4whatEv=Module["__ZNKSt10bad_typeid4whatEv"]=wasmExports["rb"])(a0);var __ZNSt8bad_castC1Ev=Module["__ZNSt8bad_castC1Ev"]=a0=>(__ZNSt8bad_castC1Ev=Module["__ZNSt8bad_castC1Ev"]=wasmExports["sb"])(a0);var __ZNSt10bad_typeidC1Ev=Module["__ZNSt10bad_typeidC1Ev"]=a0=>(__ZNSt10bad_typeidC1Ev=Module["__ZNSt10bad_typeidC1Ev"]=wasmExports["tb"])(a0);var __ZTIPK16failsafe_flags_s=Module["__ZTIPK16failsafe_flags_s"]=47416;var __ZTIP16failsafe_flags_s=Module["__ZTIP16failsafe_flags_s"]=47400;var __ZTI16failsafe_flags_s=Module["__ZTI16failsafe_flags_s"]=47392;var __ZTIb=Module["__ZTIb"]=30276;var __ZTIh=Module["__ZTIh"]=30432;var __ZTIPKNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE=Module["__ZTIPKNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE"]=47564;var __ZTIPNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE=Module["__ZTIPNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE"]=47548;var __ZTINSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE=Module["__ZTINSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE"]=47496;var __ZTISt12length_error=Module["__ZTISt12length_error"]=32376;var __ZTVSt12length_error=Module["__ZTVSt12length_error"]=32356;var __ZTISt20bad_array_new_length=Module["__ZTISt20bad_array_new_length"]=32140;var __ZTINSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEE=Module["__ZTINSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEE"]=47484;var __ZTISt12out_of_range=Module["__ZTISt12out_of_range"]=32428;var __ZTVSt12out_of_range=Module["__ZTVSt12out_of_range"]=32408;var __ZTVN10__cxxabiv120__si_class_type_infoE=Module["__ZTVN10__cxxabiv120__si_class_type_infoE"]=31708;var __ZTVN10__cxxabiv121__vmi_class_type_infoE=Module["__ZTVN10__cxxabiv121__vmi_class_type_infoE"]=31800;var __ZTVN10__cxxabiv117__class_type_infoE=Module["__ZTVN10__cxxabiv117__class_type_infoE"]=31668;var __ZTS16failsafe_flags_s=Module["__ZTS16failsafe_flags_s"]=28016;var __ZTVN10__cxxabiv119__pointer_type_infoE=Module["__ZTVN10__cxxabiv119__pointer_type_infoE"]=31920;var __ZTSP16failsafe_flags_s=Module["__ZTSP16failsafe_flags_s"]=28035;var __ZTSPK16failsafe_flags_s=Module["__ZTSPK16failsafe_flags_s"]=28055;var __ZTIi=Module["__ZTIi"]=30640;var __ZTSNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEE=Module["__ZTSNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEE"]=28104;var __ZTSNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE=Module["__ZTSNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE"]=28171;var __ZTIv=Module["__ZTIv"]=30168;var __ZTIf=Module["__ZTIf"]=31112;var __ZTSPNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE=Module["__ZTSPNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE"]=28269;var __ZTSPKNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE=Module["__ZTSPKNSt3__26vectorINS_12basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEEENS4_IS6_EEEE"]=28356;var __ZTIm=Module["__ZTIm"]=30796;var __ZTIN10emscripten3valE=Module["__ZTIN10emscripten3valE"]=47636;var __ZTSN10emscripten3valE=Module["__ZTSN10emscripten3valE"]=28459;var __ZTIc=Module["__ZTIc"]=30380;var __ZTIa=Module["__ZTIa"]=30484;var __ZTIs=Module["__ZTIs"]=30536;var __ZTIt=Module["__ZTIt"]=30588;var __ZTIj=Module["__ZTIj"]=30692;var __ZTIl=Module["__ZTIl"]=30744;var __ZTIx=Module["__ZTIx"]=30848;var __ZTIy=Module["__ZTIy"]=30900;var __ZTId=Module["__ZTId"]=31164;var __ZTINSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEEE=Module["__ZTINSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEEE"]=28516;var __ZTINSt3__212basic_stringIDsNS_11char_traitsIDsEENS_9allocatorIDsEEEE=Module["__ZTINSt3__212basic_stringIDsNS_11char_traitsIDsEENS_9allocatorIDsEEEE"]=28588;var __ZTINSt3__212basic_stringIDiNS_11char_traitsIDiEENS_9allocatorIDiEEEE=Module["__ZTINSt3__212basic_stringIDiNS_11char_traitsIDiEENS_9allocatorIDiEEEE"]=28664;var __ZTIN10emscripten11memory_viewIcEE=Module["__ZTIN10emscripten11memory_viewIcEE"]=28740;var __ZTIN10emscripten11memory_viewIaEE=Module["__ZTIN10emscripten11memory_viewIaEE"]=28780;var __ZTIN10emscripten11memory_viewIhEE=Module["__ZTIN10emscripten11memory_viewIhEE"]=28820;var __ZTIN10emscripten11memory_viewIsEE=Module["__ZTIN10emscripten11memory_viewIsEE"]=28860;var __ZTIN10emscripten11memory_viewItEE=Module["__ZTIN10emscripten11memory_viewItEE"]=28900;var __ZTIN10emscripten11memory_viewIiEE=Module["__ZTIN10emscripten11memory_viewIiEE"]=28940;var __ZTIN10emscripten11memory_viewIjEE=Module["__ZTIN10emscripten11memory_viewIjEE"]=28980;var __ZTIN10emscripten11memory_viewIlEE=Module["__ZTIN10emscripten11memory_viewIlEE"]=29020;var __ZTIN10emscripten11memory_viewImEE=Module["__ZTIN10emscripten11memory_viewImEE"]=29060;var __ZTIN10emscripten11memory_viewIxEE=Module["__ZTIN10emscripten11memory_viewIxEE"]=29100;var __ZTIN10emscripten11memory_viewIyEE=Module["__ZTIN10emscripten11memory_viewIyEE"]=29140;var __ZTIN10emscripten11memory_viewIfEE=Module["__ZTIN10emscripten11memory_viewIfEE"]=29180;var __ZTIN10emscripten11memory_viewIdEE=Module["__ZTIN10emscripten11memory_viewIdEE"]=29220;var __ZTSNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEEE=Module["__ZTSNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEEE"]=28524;var __ZTSNSt3__212basic_stringIDsNS_11char_traitsIDsEENS_9allocatorIDsEEEE=Module["__ZTSNSt3__212basic_stringIDsNS_11char_traitsIDsEENS_9allocatorIDsEEEE"]=28596;var __ZTSNSt3__212basic_stringIDiNS_11char_traitsIDiEENS_9allocatorIDiEEEE=Module["__ZTSNSt3__212basic_stringIDiNS_11char_traitsIDiEENS_9allocatorIDiEEEE"]=28672;var __ZTSN10emscripten11memory_viewIcEE=Module["__ZTSN10emscripten11memory_viewIcEE"]=28748;var __ZTSN10emscripten11memory_viewIaEE=Module["__ZTSN10emscripten11memory_viewIaEE"]=28788;var __ZTSN10emscripten11memory_viewIhEE=Module["__ZTSN10emscripten11memory_viewIhEE"]=28828;var __ZTSN10emscripten11memory_viewIsEE=Module["__ZTSN10emscripten11memory_viewIsEE"]=28868;var __ZTSN10emscripten11memory_viewItEE=Module["__ZTSN10emscripten11memory_viewItEE"]=28908;var __ZTSN10emscripten11memory_viewIiEE=Module["__ZTSN10emscripten11memory_viewIiEE"]=28948;var __ZTSN10emscripten11memory_viewIjEE=Module["__ZTSN10emscripten11memory_viewIjEE"]=28988;var __ZTSN10emscripten11memory_viewIlEE=Module["__ZTSN10emscripten11memory_viewIlEE"]=29028;var __ZTSN10emscripten11memory_viewImEE=Module["__ZTSN10emscripten11memory_viewImEE"]=29068;var __ZTSN10emscripten11memory_viewIxEE=Module["__ZTSN10emscripten11memory_viewIxEE"]=29108;var __ZTSN10emscripten11memory_viewIyEE=Module["__ZTSN10emscripten11memory_viewIyEE"]=29148;var __ZTSN10emscripten11memory_viewIfEE=Module["__ZTSN10emscripten11memory_viewIfEE"]=29188;var __ZTSN10emscripten11memory_viewIdEE=Module["__ZTSN10emscripten11memory_viewIdEE"]=29228;var __ZTVSt11logic_error=Module["__ZTVSt11logic_error"]=32180;var __ZTVSt9exception=Module["__ZTVSt9exception"]=32016;var __ZTVSt13runtime_error=Module["__ZTVSt13runtime_error"]=32200;var __ZTISt9exception=Module["__ZTISt9exception"]=32036;var ___cxa_unexpected_handler=Module["___cxa_unexpected_handler"]=47864;var ___cxa_terminate_handler=Module["___cxa_terminate_handler"]=47860;var ___cxa_new_handler=Module["___cxa_new_handler"]=50108;var __ZTIN10__cxxabiv116__shim_type_infoE=Module["__ZTIN10__cxxabiv116__shim_type_infoE"]=29744;var __ZTIN10__cxxabiv117__class_type_infoE=Module["__ZTIN10__cxxabiv117__class_type_infoE"]=29792;var __ZTIN10__cxxabiv117__pbase_type_infoE=Module["__ZTIN10__cxxabiv117__pbase_type_infoE"]=29840;var __ZTIDn=Module["__ZTIDn"]=30220;var __ZTIN10__cxxabiv119__pointer_type_infoE=Module["__ZTIN10__cxxabiv119__pointer_type_infoE"]=29888;var __ZTIN10__cxxabiv120__function_type_infoE=Module["__ZTIN10__cxxabiv120__function_type_infoE"]=29936;var __ZTIN10__cxxabiv129__pointer_to_member_type_infoE=Module["__ZTIN10__cxxabiv129__pointer_to_member_type_infoE"]=29988;var __ZTISt9type_info=Module["__ZTISt9type_info"]=32700;var __ZTSN10__cxxabiv116__shim_type_infoE=Module["__ZTSN10__cxxabiv116__shim_type_infoE"]=29756;var __ZTSN10__cxxabiv117__class_type_infoE=Module["__ZTSN10__cxxabiv117__class_type_infoE"]=29804;var __ZTSN10__cxxabiv117__pbase_type_infoE=Module["__ZTSN10__cxxabiv117__pbase_type_infoE"]=29852;var __ZTSN10__cxxabiv119__pointer_type_infoE=Module["__ZTSN10__cxxabiv119__pointer_type_infoE"]=29900;var __ZTSN10__cxxabiv120__function_type_infoE=Module["__ZTSN10__cxxabiv120__function_type_infoE"]=29948;var __ZTSN10__cxxabiv129__pointer_to_member_type_infoE=Module["__ZTSN10__cxxabiv129__pointer_to_member_type_infoE"]=3e4;var __ZTVN10__cxxabiv116__shim_type_infoE=Module["__ZTVN10__cxxabiv116__shim_type_infoE"]=30060;var __ZTVN10__cxxabiv123__fundamental_type_infoE=Module["__ZTVN10__cxxabiv123__fundamental_type_infoE"]=30088;var __ZTIN10__cxxabiv123__fundamental_type_infoE=Module["__ZTIN10__cxxabiv123__fundamental_type_infoE"]=30116;var __ZTSN10__cxxabiv123__fundamental_type_infoE=Module["__ZTSN10__cxxabiv123__fundamental_type_infoE"]=30128;var __ZTSv=Module["__ZTSv"]=30176;var __ZTIPv=Module["__ZTIPv"]=30180;var __ZTSPv=Module["__ZTSPv"]=30196;var __ZTIPKv=Module["__ZTIPKv"]=30200;var __ZTSPKv=Module["__ZTSPKv"]=30216;var __ZTSDn=Module["__ZTSDn"]=30228;var __ZTIPDn=Module["__ZTIPDn"]=30232;var __ZTSPDn=Module["__ZTSPDn"]=30248;var __ZTIPKDn=Module["__ZTIPKDn"]=30252;var __ZTSPKDn=Module["__ZTSPKDn"]=30268;var __ZTSb=Module["__ZTSb"]=30284;var __ZTIPb=Module["__ZTIPb"]=30288;var __ZTSPb=Module["__ZTSPb"]=30304;var __ZTIPKb=Module["__ZTIPKb"]=30308;var __ZTSPKb=Module["__ZTSPKb"]=30324;var __ZTIw=Module["__ZTIw"]=30328;var __ZTSw=Module["__ZTSw"]=30336;var __ZTIPw=Module["__ZTIPw"]=30340;var __ZTSPw=Module["__ZTSPw"]=30356;var __ZTIPKw=Module["__ZTIPKw"]=30360;var __ZTSPKw=Module["__ZTSPKw"]=30376;var __ZTSc=Module["__ZTSc"]=30388;var __ZTIPc=Module["__ZTIPc"]=30392;var __ZTSPc=Module["__ZTSPc"]=30408;var __ZTIPKc=Module["__ZTIPKc"]=30412;var __ZTSPKc=Module["__ZTSPKc"]=30428;var __ZTSh=Module["__ZTSh"]=30440;var __ZTIPh=Module["__ZTIPh"]=30444;var __ZTSPh=Module["__ZTSPh"]=30460;var __ZTIPKh=Module["__ZTIPKh"]=30464;var __ZTSPKh=Module["__ZTSPKh"]=30480;var __ZTSa=Module["__ZTSa"]=30492;var __ZTIPa=Module["__ZTIPa"]=30496;var __ZTSPa=Module["__ZTSPa"]=30512;var __ZTIPKa=Module["__ZTIPKa"]=30516;var __ZTSPKa=Module["__ZTSPKa"]=30532;var __ZTSs=Module["__ZTSs"]=30544;var __ZTIPs=Module["__ZTIPs"]=30548;var __ZTSPs=Module["__ZTSPs"]=30564;var __ZTIPKs=Module["__ZTIPKs"]=30568;var __ZTSPKs=Module["__ZTSPKs"]=30584;var __ZTSt=Module["__ZTSt"]=30596;var __ZTIPt=Module["__ZTIPt"]=30600;var __ZTSPt=Module["__ZTSPt"]=30616;var __ZTIPKt=Module["__ZTIPKt"]=30620;var __ZTSPKt=Module["__ZTSPKt"]=30636;var __ZTSi=Module["__ZTSi"]=30648;var __ZTIPi=Module["__ZTIPi"]=30652;var __ZTSPi=Module["__ZTSPi"]=30668;var __ZTIPKi=Module["__ZTIPKi"]=30672;var __ZTSPKi=Module["__ZTSPKi"]=30688;var __ZTSj=Module["__ZTSj"]=30700;var __ZTIPj=Module["__ZTIPj"]=30704;var __ZTSPj=Module["__ZTSPj"]=30720;var __ZTIPKj=Module["__ZTIPKj"]=30724;var __ZTSPKj=Module["__ZTSPKj"]=30740;var __ZTSl=Module["__ZTSl"]=30752;var __ZTIPl=Module["__ZTIPl"]=30756;var __ZTSPl=Module["__ZTSPl"]=30772;var __ZTIPKl=Module["__ZTIPKl"]=30776;var __ZTSPKl=Module["__ZTSPKl"]=30792;var __ZTSm=Module["__ZTSm"]=30804;var __ZTIPm=Module["__ZTIPm"]=30808;var __ZTSPm=Module["__ZTSPm"]=30824;var __ZTIPKm=Module["__ZTIPKm"]=30828;var __ZTSPKm=Module["__ZTSPKm"]=30844;var __ZTSx=Module["__ZTSx"]=30856;var __ZTIPx=Module["__ZTIPx"]=30860;var __ZTSPx=Module["__ZTSPx"]=30876;var __ZTIPKx=Module["__ZTIPKx"]=30880;var __ZTSPKx=Module["__ZTSPKx"]=30896;var __ZTSy=Module["__ZTSy"]=30908;var __ZTIPy=Module["__ZTIPy"]=30912;var __ZTSPy=Module["__ZTSPy"]=30928;var __ZTIPKy=Module["__ZTIPKy"]=30932;var __ZTSPKy=Module["__ZTSPKy"]=30948;var __ZTIn=Module["__ZTIn"]=30952;var __ZTSn=Module["__ZTSn"]=30960;var __ZTIPn=Module["__ZTIPn"]=30964;var __ZTSPn=Module["__ZTSPn"]=30980;var __ZTIPKn=Module["__ZTIPKn"]=30984;var __ZTSPKn=Module["__ZTSPKn"]=31e3;var __ZTIo=Module["__ZTIo"]=31004;var __ZTSo=Module["__ZTSo"]=31012;var __ZTIPo=Module["__ZTIPo"]=31016;var __ZTSPo=Module["__ZTSPo"]=31032;var __ZTIPKo=Module["__ZTIPKo"]=31036;var __ZTSPKo=Module["__ZTSPKo"]=31052;var __ZTIDh=Module["__ZTIDh"]=31056;var __ZTSDh=Module["__ZTSDh"]=31064;var __ZTIPDh=Module["__ZTIPDh"]=31068;var __ZTSPDh=Module["__ZTSPDh"]=31084;var __ZTIPKDh=Module["__ZTIPKDh"]=31088;var __ZTSPKDh=Module["__ZTSPKDh"]=31104;var __ZTSf=Module["__ZTSf"]=31120;var __ZTIPf=Module["__ZTIPf"]=31124;var __ZTSPf=Module["__ZTSPf"]=31140;var __ZTIPKf=Module["__ZTIPKf"]=31144;var __ZTSPKf=Module["__ZTSPKf"]=31160;var __ZTSd=Module["__ZTSd"]=31172;var __ZTIPd=Module["__ZTIPd"]=31176;var __ZTSPd=Module["__ZTSPd"]=31192;var __ZTIPKd=Module["__ZTIPKd"]=31196;var __ZTSPKd=Module["__ZTSPKd"]=31212;var __ZTIe=Module["__ZTIe"]=31216;var __ZTSe=Module["__ZTSe"]=31224;var __ZTIPe=Module["__ZTIPe"]=31228;var __ZTSPe=Module["__ZTSPe"]=31244;var __ZTIPKe=Module["__ZTIPKe"]=31248;var __ZTSPKe=Module["__ZTSPKe"]=31264;var __ZTIg=Module["__ZTIg"]=31268;var __ZTSg=Module["__ZTSg"]=31276;var __ZTIPg=Module["__ZTIPg"]=31280;var __ZTSPg=Module["__ZTSPg"]=31296;var __ZTIPKg=Module["__ZTIPKg"]=31300;var __ZTSPKg=Module["__ZTSPKg"]=31316;var __ZTIDu=Module["__ZTIDu"]=31320;var __ZTSDu=Module["__ZTSDu"]=31328;var __ZTIPDu=Module["__ZTIPDu"]=31332;var __ZTSPDu=Module["__ZTSPDu"]=31348;var __ZTIPKDu=Module["__ZTIPKDu"]=31352;var __ZTSPKDu=Module["__ZTSPKDu"]=31368;var __ZTIDs=Module["__ZTIDs"]=31376;var __ZTSDs=Module["__ZTSDs"]=31384;var __ZTIPDs=Module["__ZTIPDs"]=31388;var __ZTSPDs=Module["__ZTSPDs"]=31404;var __ZTIPKDs=Module["__ZTIPKDs"]=31408;var __ZTSPKDs=Module["__ZTSPKDs"]=31424;var __ZTIDi=Module["__ZTIDi"]=31432;var __ZTSDi=Module["__ZTSDi"]=31440;var __ZTIPDi=Module["__ZTIPDi"]=31444;var __ZTSPDi=Module["__ZTSPDi"]=31460;var __ZTIPKDi=Module["__ZTIPKDi"]=31464;var __ZTSPKDi=Module["__ZTSPKDi"]=31480;var __ZTVN10__cxxabiv117__array_type_infoE=Module["__ZTVN10__cxxabiv117__array_type_infoE"]=31488;var __ZTIN10__cxxabiv117__array_type_infoE=Module["__ZTIN10__cxxabiv117__array_type_infoE"]=31516;var __ZTSN10__cxxabiv117__array_type_infoE=Module["__ZTSN10__cxxabiv117__array_type_infoE"]=31528;var __ZTVN10__cxxabiv120__function_type_infoE=Module["__ZTVN10__cxxabiv120__function_type_infoE"]=31564;var __ZTVN10__cxxabiv116__enum_type_infoE=Module["__ZTVN10__cxxabiv116__enum_type_infoE"]=31592;var __ZTIN10__cxxabiv116__enum_type_infoE=Module["__ZTIN10__cxxabiv116__enum_type_infoE"]=31620;var __ZTSN10__cxxabiv116__enum_type_infoE=Module["__ZTSN10__cxxabiv116__enum_type_infoE"]=31632;var __ZTIN10__cxxabiv120__si_class_type_infoE=Module["__ZTIN10__cxxabiv120__si_class_type_infoE"]=31748;var __ZTSN10__cxxabiv120__si_class_type_infoE=Module["__ZTSN10__cxxabiv120__si_class_type_infoE"]=31760;var __ZTIN10__cxxabiv121__vmi_class_type_infoE=Module["__ZTIN10__cxxabiv121__vmi_class_type_infoE"]=31840;var __ZTSN10__cxxabiv121__vmi_class_type_infoE=Module["__ZTSN10__cxxabiv121__vmi_class_type_infoE"]=31852;var __ZTVN10__cxxabiv117__pbase_type_infoE=Module["__ZTVN10__cxxabiv117__pbase_type_infoE"]=31892;var __ZTVN10__cxxabiv129__pointer_to_member_type_infoE=Module["__ZTVN10__cxxabiv129__pointer_to_member_type_infoE"]=31948;var __ZTVSt9bad_alloc=Module["__ZTVSt9bad_alloc"]=31976;var __ZTVSt20bad_array_new_length=Module["__ZTVSt20bad_array_new_length"]=31996;var __ZTISt9bad_alloc=Module["__ZTISt9bad_alloc"]=32112;var __ZTSSt9exception=Module["__ZTSSt9exception"]=32044;var __ZTVSt13bad_exception=Module["__ZTVSt13bad_exception"]=32060;var __ZTISt13bad_exception=Module["__ZTISt13bad_exception"]=32080;var __ZTSSt13bad_exception=Module["__ZTSSt13bad_exception"]=32092;var __ZTSSt9bad_alloc=Module["__ZTSSt9bad_alloc"]=32124;var __ZTSSt20bad_array_new_length=Module["__ZTSSt20bad_array_new_length"]=32152;var __ZTISt11logic_error=Module["__ZTISt11logic_error"]=32272;var __ZTISt13runtime_error=Module["__ZTISt13runtime_error"]=32508;var __ZTVSt12domain_error=Module["__ZTVSt12domain_error"]=32220;var __ZTISt12domain_error=Module["__ZTISt12domain_error"]=32240;var __ZTSSt12domain_error=Module["__ZTSSt12domain_error"]=32252;var __ZTSSt11logic_error=Module["__ZTSSt11logic_error"]=32284;var __ZTVSt16invalid_argument=Module["__ZTVSt16invalid_argument"]=32300;var __ZTISt16invalid_argument=Module["__ZTISt16invalid_argument"]=32320;var __ZTSSt16invalid_argument=Module["__ZTSSt16invalid_argument"]=32332;var __ZTSSt12length_error=Module["__ZTSSt12length_error"]=32388;var __ZTSSt12out_of_range=Module["__ZTSSt12out_of_range"]=32440;var __ZTVSt11range_error=Module["__ZTVSt11range_error"]=32460;var __ZTISt11range_error=Module["__ZTISt11range_error"]=32480;var __ZTSSt11range_error=Module["__ZTSSt11range_error"]=32492;var __ZTSSt13runtime_error=Module["__ZTSSt13runtime_error"]=32520;var __ZTVSt14overflow_error=Module["__ZTVSt14overflow_error"]=32540;var __ZTISt14overflow_error=Module["__ZTISt14overflow_error"]=32560;var __ZTSSt14overflow_error=Module["__ZTSSt14overflow_error"]=32572;var __ZTVSt15underflow_error=Module["__ZTVSt15underflow_error"]=32592;var __ZTISt15underflow_error=Module["__ZTISt15underflow_error"]=32612;var __ZTSSt15underflow_error=Module["__ZTSSt15underflow_error"]=32624;var __ZTVSt8bad_cast=Module["__ZTVSt8bad_cast"]=32644;var __ZTVSt10bad_typeid=Module["__ZTVSt10bad_typeid"]=32664;var __ZTISt8bad_cast=Module["__ZTISt8bad_cast"]=32724;var __ZTISt10bad_typeid=Module["__ZTISt10bad_typeid"]=32748;var __ZTVSt9type_info=Module["__ZTVSt9type_info"]=32684;var __ZTSSt9type_info=Module["__ZTSSt9type_info"]=32708;var __ZTSSt8bad_cast=Module["__ZTSSt8bad_cast"]=32736;var __ZTSSt10bad_typeid=Module["__ZTSSt10bad_typeid"]=32760;function run(){if(runDependencies>0){dependenciesFulfilled=run;return}preRun();if(runDependencies>0){dependenciesFulfilled=run;return}function doRun(){Module["calledRun"]=true;if(ABORT)return;initRuntime();Module["onRuntimeInitialized"]?.();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(()=>{setTimeout(()=>Module["setStatus"](""),1);doRun()},1)}else{doRun()}}function preInit(){if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].shift()()}}}preInit();run();
