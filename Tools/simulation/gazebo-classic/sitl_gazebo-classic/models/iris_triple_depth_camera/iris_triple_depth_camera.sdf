<?xml version="1.0" ?>
<sdf version='1.5'>
  <model name='iris_triple_depth_camera'>
    <include>
      <uri>model://iris</uri>
    </include>
   <model name="front_camera">
    <pose>0.1 0 -0.03 0 0 0</pose>
    <link name="link">
      <inertial>
        <pose>0.01 0.025 0.025 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://realsense_camera/meshes/realsense.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <sensor name="depth_camera" type="depth">
        <update_rate>20</update_rate>
        <camera>
          <horizontal_fov>1.02974</horizontal_fov>
          <image>
            <format>R8G8B8</format>
            <width>64</width>
            <height>48</height>
          </image>
          <clip>
            <near>0.5</near>
            <far>18</far>
          </clip>
        </camera>
        <plugin filename="libgazebo_ros_openni_kinect.so" name="camera_controller">
          <cameraName>camera_front</cameraName>
          <alwaysOn>true</alwaysOn>
          <updateRate>20</updateRate>
          <pointCloudCutoff>0.2</pointCloudCutoff>
          <pointCloudCutoffMax>20</pointCloudCutoffMax>
          <imageTopicName>rgb/image_raw</imageTopicName>
          <cameraInfoTopicName>rgb/camera_info</cameraInfoTopicName>
          <depthImageTopicName>depth/image_raw</depthImageTopicName>
          <depthImageCameraInfoTopicName>depth/camera_info</depthImageCameraInfoTopicName>
          <pointCloudTopicName>depth/points</pointCloudTopicName>
          <frameName>front_camera_link</frameName>
          <distortion_k1>0.0</distortion_k1>
          <distortion_k2>0.0</distortion_k2>
          <distortion_k3>0.0</distortion_k3>
          <distortion_t1>0.0</distortion_t1>
          <distortion_t2>0.0</distortion_t2>
        </plugin>
      </sensor>
    </link>
  </model>

    <joint name="depth_camera_joint_front" type="revolute">
      <child>front_camera::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>


  <model name="left_camera">
    <pose>0.05 0.09 -0.03 0 0 1.0472</pose>
    <link name="link">
      <inertial>
        <pose>0.01 0.025 0.025 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://realsense_camera/meshes/realsense.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <sensor name="depth_camera" type="depth">
        <update_rate>20</update_rate>
        <camera>
          <horizontal_fov>1.02974</horizontal_fov>
          <image>
            <format>R8G8B8</format>
            <width>64</width>
            <height>48</height>
          </image>
          <clip>
            <near>0.5</near>
            <far>18</far>
          </clip>
        </camera>
        <plugin filename="libgazebo_ros_openni_kinect.so" name="camera_controller">
          <cameraName>camera_left</cameraName>
          <alwaysOn>true</alwaysOn>
          <updateRate>20</updateRate>
          <pointCloudCutoff>0.2</pointCloudCutoff>
          <pointCloudCutoffMax>20</pointCloudCutoffMax>
          <imageTopicName>rgb/image_raw</imageTopicName>
          <cameraInfoTopicName>rgb/camera_info</cameraInfoTopicName>
          <depthImageTopicName>depth/image_raw</depthImageTopicName>
          <depthImageCameraInfoTopicName>depth/camera_info</depthImageCameraInfoTopicName>
          <pointCloudTopicName>depth/points</pointCloudTopicName>
          <frameName>left_camera_link</frameName>
          <distortion_k1>0.0</distortion_k1>
          <distortion_k2>0.0</distortion_k2>
          <distortion_k3>0.0</distortion_k3>
          <distortion_t1>0.0</distortion_t1>
          <distortion_t2>0.0</distortion_t2>
        </plugin>
      </sensor>
    </link>
  </model>

    <joint name="depth_camera_joint_left" type="revolute">
      <child>left_camera::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

  <model name="right_camera">
    <pose>0.05 -0.09 -0.03 0 0 -1.0472</pose>
    <link name="link">
      <inertial>
        <pose>0.01 0.025 0.025 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://realsense_camera/meshes/realsense.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <sensor name="depth_camera" type="depth">
        <update_rate>20</update_rate>
        <camera>
          <horizontal_fov>1.02974</horizontal_fov>
          <image>
            <format>R8G8B8</format>
            <width>64</width>
            <height>48</height>
          </image>
          <clip>
            <near>0.5</near>
            <far>18</far>
          </clip>
        </camera>
        <plugin filename="libgazebo_ros_openni_kinect.so" name="camera_controller">
          <cameraName>camera_right</cameraName>
          <alwaysOn>true</alwaysOn>
          <updateRate>20</updateRate>
          <pointCloudCutoff>0.2</pointCloudCutoff>
          <pointCloudCutoffMax>20</pointCloudCutoffMax>
          <imageTopicName>rgb/image_raw</imageTopicName>
          <cameraInfoTopicName>rgb/camera_info</cameraInfoTopicName>
          <depthImageTopicName>depth/image_raw</depthImageTopicName>
          <depthImageCameraInfoTopicName>depth/camera_info</depthImageCameraInfoTopicName>
          <pointCloudTopicName>depth/points</pointCloudTopicName>
          <frameName>right_camera_link</frameName>
          <distortion_k1>0.0</distortion_k1>
          <distortion_k2>0.0</distortion_k2>
          <distortion_k3>0.0</distortion_k3>
          <distortion_t1>0.0</distortion_t1>
          <distortion_t2>0.0</distortion_t2>
        </plugin>
      </sensor>
    </link>
  </model>

    <joint name="depth_camera_joint_right" type="revolute">
      <child>right_camera::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

    <include>
      <uri>model://lidar</uri>
      <pose>-0.12 0 0 0 0 0</pose>
    </include>
    <joint name="lidar_joint" type="fixed">
      <child>lidar::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

  </model>
</sdf>
