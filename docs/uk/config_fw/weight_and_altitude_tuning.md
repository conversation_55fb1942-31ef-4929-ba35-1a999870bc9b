# Покращене налаштування TECS (Вага та висота)

Ця тема показує, як ви можете компенсувати зміни в [вагу транспортного засобу](#vehicle-weight-compensation) та [щільність повітря](#air-density-compensation), разом із інформацією про [алгоритми](#weight-and-density-compensation-algorithms), які використовуються.

:::warning
Ця тема вимагає, щоб ви вже виконали [основне налаштування TECS](../config_fw/position_tuning_guide_fixedwing.md#tecs-tuning-altitude-and-airspeed).
:::

[Основне налаштування TECS](../config_fw/position_tuning_guide_fixedwing.md#tecs-tuning-altitude-and-airspeed) встановило ключові обмеження продуктивності транспортного засобу, які необхідні для належної роботи контролера висоти та швидкості.

Хоча ці обмеження вказані за допомогою постійних параметрів, насправді продуктивність транспортного засобу не є постійною і залежить від різних факторів.
Якщо не враховувати зміни в вазі та густині повітря, відстеження висоти та швидкості повітря ймовірно погіршиться у випадку, коли конфігурація (густини повітря та ваги) значно відрізняється від конфігурації, при якій тюнінгувався транспортний засіб.

## Компенсація ваги транспортного засобу

Встановіть (обидва) наступні параметри для масштабування максимальної швидкості підйому, мінімальної швидкості опускання та налаштування обмежень швидкості для ваги:

- [WEIGHT_BASE](../advanced_config/parameter_reference.md#WEIGHT_BASE) — вага транспортного засобу, при якій було виконано [Основне налаштування TECS](../config_fw/position_tuning_guide_fixedwing.md#tecs-tuning-altitude-and-airspeed).
- [ВАГА_БРУТТО](../advanced_config/parameter_reference.md#WEIGHT_BASE) — фактична вага транспортного засобу у будь-який момент часу, наприклад, при використанні більшого акумулятора або з вантажем, який не був присутній під час налаштування.

Ви можете визначити значення, вимірюючи вагу транспортного засобу за допомогою ваги в налаштуванні настройки та під час польоту з вантажем.

Масштабування виконується, коли _обидва_ `WEIGHT_BASE` та `WEIGHT_GROSS` більше `0`, і не матиме жодного впливу, якщо значення однакові.
Дивіться розділ [алгоритми](#weight-and-density-compensation-algorithms) нижче для отримання додаткової інформації.

## Компенсація щільності повітря

### Вкажіть максимальну висоту обслуговування

У PX4 службовий стелі [FW_SERVICE_CEIL](../advanced_config/parameter_reference.md#FW_SERVICE_CEIL) вказує висоту в стандартних атмосферних умовах, на якій транспортний засіб все ще може досягти максимальної швидкості підйому 0,5 м/с при максимальному режимі газу та вагою, рівною [WEIGHT_BASE](../advanced_config/parameter_reference.md#WEIGHT_BASE).
За замовчуванням цей параметр вимкнений, і компенсація не відбудеться.

Цей параметр потрібно визначити експериментально.
Завжди краще встановлювати консервативне значення (нижче значення), ніж оптимістичне значення.

### Застосувати корекцію щільності до мінімальної швидкості опускання

Мінімальна швидкість опускання встановлюється в [FW_T_SINK_MIN](../advanced_config/parameter_reference.md#FW_T_SINK_MIN).

Якщо налаштування [Основного налаштування TECS](../config_fw/position_tuning_guide_fixedwing.md#tecs-tuning-altitude-and-airspeed) не було виконано в стандартних умовах рівня моря, тоді параметр [FW_T_SINK_MIN](../advanced_config/parameter_reference.md#FW_T_SINK_MIN) повинен бути змінений шляхом множення на корекційний фактор $P$ (де $\rho$ - густина повітря під час налаштування):

$$P = \sqrt{\rho\over{\rho_{sealevel}}}$$

Для отримання додаткової інформації див. [Ефект густини на мінімальну швидкість опускання](#effect-of-density-on-minimum-sink-rate).

### Застосувати корекцію щільності до обрізання ручки газу

Регулювання обтічної дроселі встановлюється за допомогою [FW_THR_TRIM](../advanced_config/parameter_reference.md#FW_THR_TRIM).

Якщо базове налаштування не було виконано в стандартних умовах рівня моря, тоді значення для [FW_THR_TRIM](../advanced_config/parameter_reference.md#FW_THR_TRIM) повинно бути змінено шляхом множення на корекційний фактор $P$:

$$P = \sqrt{\rho\over{\rho_{sealevel}}}$$

Для отримання додаткової інформації див. [Ефект густини на обрізний регулятор](#effect-of-density-on-trim-throttle)

## Алгоритми насиченості ваги та щільності

У цьому розділі міститься інформація про операції масштабування, виконані PX4.
Це надається лише для цікавості, і може бути цікавим для розробників, які хочуть змінити код масштабування.

### Записка

У наступних розділах ми будемо використовувати позначення $\hat X$ для того, щоб вказати, що це значення є каліброваним значенням змінної $X$.
Під каліброваним ми маємо на увазі значення цієї змінної, виміряне на рівні моря в стандартних атмосферних умовах, коли вага транспортного засобу дорівнювала [WEIGHT_BASE](../advanced_config/parameter_reference.md#WEIGHT_BASE).

Наприклад, за $\hat{\dot{h}}_{max}$ ми вказуємо максимальну швидкість підйому, яку транспортний засіб може досягти при [WEIGHT_BASE](../advanced_config/parameter_reference.md#WEIGHT_BASE) на рівні моря в стандартних атмосферних умовах.

### Вплив ваги на максимальну швидкість підйому

Максимальна швидкість підйому ([FW_T_CLMB_MAX](../advanced_config/parameter_reference.md#FW_T_CLMB_MAX)) масштабується як функція відношення ваги.

З рівноважних рівнянь руху літака ми встановлюємо, що максимальна швидкість підйому може бути записана як:

$$\dot{h}_{max} = { V * ( Thrust - Drag ) \over{m*g}}$$

де `V` - це справжня швидкість повітря, а `m` - маса транспортного засобу.
З цього рівняння ми бачимо, що максимальні швидкості підйому масштабуються з масою транспортного засобу.

### Вплив ваги на мінімальну швидкість опускання

Мінімальна швидкість опускання ([FW_T_SINK_MIN](../advanced_config/parameter_reference.md#FW_T_SINK_MIN)) масштабується як функція відношення ваги

Мінімальна швидкість опускання може бути записана як:

$$\dot{h}_{min} = \sqrt{2mg\over{\rho S}} f(C_L, C_D)$$

де $\rho$ - щільність повітря, S - площа опорної поверхні крила, а $f(C_L, C_D)$ - функція полюсів, підйому та опору.

З цього рівняння бачимо, що мінімальна швидкість опускання масштабується з квадратним коренем відношення ваги.

### Вплив ваги на межі швидкості повітря

The minimum airspeed ([FW_AIRSPD_MIN](../advanced_config/parameter_reference.md#FW_AIRSPD_MIN)), the stall airspeed ([FW_AIRSPD_STALL](../advanced_config/parameter_reference.md#FW_AIRSPD_STALL)) and trim airspeed ([FW_AIRSPD_TRIM](../advanced_config/parameter_reference.md#FW_AIRSPD_TRIM)) are adjusted based on the weight ratio specified by [WEIGHT_BASE](../advanced_config/parameter_reference.md#WEIGHT_BASE) and [WEIGHT_GROSS](../advanced_config/parameter_reference.md#WEIGHT_GROSS).

У стані сталого польоту ми можемо вимагати, щоб підйом був рівним вазіллю транспортного засобу:

$$Lift = mg = {1\over{2}} \rho V^2 S C_L$$

перегруповування цього рівняння для швидкості повітря дає:

$$V = \\sqrt{\\frac{2mg}{\\rho S C_D }}$$

З цього рівняння ми бачимо, що якщо ми припускаємо постійний кут атаки (який, як правило, ми бажаємо), вага транспортного засобу впливає на швидкість повітря з квадратним коренем відношення.
Отже, обмеження швидкості повітря, згадані вище, масштабуються за допомогою квадратного кореня відношення ваги.

### Вплив щільності на максимальну швидкість підйому

Максимальна швидкість підйому встановлюється за допомогою [FW_T_CLMB_MAX](../advanced_config/parameter_reference.md#FW_T_CLMB_MAX).

Як ми вже бачили раніше, максимальна швидкість підйому може бути сформульована як:

$$\dot{h}_{max} = { V * ( Thrust - Drag ) \over{m*g}}$$

Густина повітря впливає на швидкість повітря, тягу та опір, і моделювання цих ефектів не є прямолінійним.
Проте ми можемо посилатися на літературу та досвід, які вказують, що для літака з гвинтовим пропелером максимальна швидкість підйому зменшується приблизно лінійно з густиною повітря.
Таким чином, ми можемо написати максимальну швидкість підйому як:

$$\dot{h}_{max} = \hat{\dot{h}} * {\rho_{sealevel} \over{\rho}} K$$

де$\rho_{sealevel}$ - щільність повітря на рівні моря в стандартній атмосфері, а К - масштабний фактор, який визначає нахил функції.
Замість спроби ідентифікувати ці константи, звичайною практикою в авіації є вказання висоти службового стелі, на якій транспортний засіб все ще може досягти мінімально вказаної швидкості підйому.

### Вплив щільності на мінімальну швидкість опускання

Мінімальна швидкість опускання встановлюється за допомогою [FW_T_SINK_MIN](../advanced_config/parameter_reference.md#FW_T_SINK_MIN).

У попередніх розділах ми бачили формулу для мінімальної швидкості опускання:

$$\dot{h}_{min} = \sqrt{2mg\over{\rho S}} f(C_L, C_D)$$

Це показує, що мінімальна швидкість опускання масштабується з квадратним коренем відношення оберненої густини повітря.

### Вплив щільності на обертовий регулятор обрізання

TODO: Додати тут похідну.
