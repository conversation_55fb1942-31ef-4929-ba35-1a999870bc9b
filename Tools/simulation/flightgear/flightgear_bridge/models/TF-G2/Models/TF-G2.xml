<?xml version="1.0" encoding="UTF-8"?>

<PropertyList>

  <path>TF-G2.ac</path>

  <offsets>
    <pitch-deg>0</pitch-deg>
    <heading-deg>0</heading-deg>
    <roll-deg>0</roll-deg>
    <x-m>0</x-m>
    <y-m>0</y-m>
    <z-m>0</z-m>
  </offsets>


	<nasal>
        <load>
            var livery_update = aircraft.livery_update.new("Aircraft/TF-G2/Models/Liveries", 10);
        </load>

        <unload>
            livery_update.stop();
        </unload>
    </nasal>


    <animation>
    <object-name>propeller</object-name>

    <object-name>rotorMiddle</object-name>
    <object-name>rotorBlades</object-name>

    <object-name>rudder</object-name>

    <object-name>gearFL</object-name>
    <object-name>gearFR</object-name>
    <object-name>gearR</object-name>

    <object-name>rotorHead</object-name>
    <object-name>rotorPitchBase</object-name>
    <object-name>rotorYawBase</object-name>

  </animation>

<effect>
	<inherits-from>Effects/model-transparent</inherits-from>
	<object-name>canopyGlass</object-name>

</effect>

<animation>
  <type>spin</type>
  <object-name>propeller</object-name>
   <condition>
       <less-than>
        <property>engines/engine[0]/rpm</property>
        <value>1000.000</value>
      </less-than>
       </condition>
 <property>engines/engine[0]/rpm</property>
  <factor>1</factor>
    <center>
        <x-m> -0.034 </x-m>
        <y-m> -0.00 </y-m>
        <z-m> 0.025 </z-m>
    </center>
    <axis>
      <x>  1 </x>
      <y>  0 </y>
      <z>  0 </z>
    </axis>
 </animation>

<animation>
  <type>spin</type>
  <object-name>propeller</object-name>
   <condition>
    <and>
      <greater-than>
        <property>engines/engine[0]/rpm</property>
        <value>1000.0001</value>
      </greater-than>
      <less-than>
        <property>engines/engine[0]/rpm</property>
        <value>1600.000</value>
      </less-than>
    </and>
    </condition>
     <property>engines/engine[0]/rpm</property>
  <factor>1</factor>
    <center>
      <x-m> -0.034 </x-m>
      <y-m> -0.00 </y-m>
      <z-m> 0.025 </z-m>
    </center>
    <axis>
      <x>  1 </x>
      <y>  0 </y>
      <z>  0 </z>
    </axis>
 </animation>



   <animation>
     <type>spin</type>
    <object-name>propeller</object-name>
      <condition>
         <greater-than>
           <property>engines/engine[0]/rpm</property>
           <value>1600.0001</value>
         </greater-than>
       </condition>
        <property>engines/engine[0]/rpm</property>
     <factor>0.02</factor>
       <center>
           <x-m> -0.034 </x-m>
           <y-m> -0.00 </y-m>
           <z-m> 0.025 </z-m>
       </center>
       <axis>
         <x>  -1 </x>
         <y>  0 </y>
         <z>  0 </z>
       </axis>
    </animation>


<!-- Rotor and RotorHead -->

   <animation>
    <type>rotate</type>
    <object-name>rotorMiddle</object-name>
    <object-name>rotorBlades</object-name>

    <object-name>rotorHead</object-name>
    <object-name>rotorPitchBase</object-name>
    <object-name>rotorYawBase</object-name>
    <property>rotors/main/tilt/roll-deg</property>
    <factor> -1 </factor>
    <center>
       <x-m> 0.1476  </x-m>
       <y-m> -0.0000  </y-m>
       <z-m> 0.202 </z-m>
    </center>
    <axis>
      <x>  -1 </x>
      <y>  0 </y>
      <z>  0 </z>
    </axis>
  </animation>

  <animation>
    <type>rotate</type>
    <object-name>rotorMiddle</object-name>
    <object-name>rotorBlades</object-name>

    <object-name>rotorHead</object-name>
    <object-name>rotorPitchBase</object-name>
    <property>rotors/main/tilt/pitch-deg</property>
    <factor> -1 </factor>
    <center>
       <x-m> 0.1476  </x-m>
       <y-m> -0.0000  </y-m>
       <z-m> 0.202 </z-m>
    </center>
    <axis>
      <x>  0 </x>
      <y>  -1 </y>
      <z>  0 </z>
    </axis>
  </animation>


 <animation>
	<type>spin</type>
	<object-name>rotorMiddle</object-name>
	<object-name>rotorBlades</object-name>

	<property>rotors/main/rpm</property>
	<factor>1</factor>
	<offset-deg>0</offset-deg>
	<axis>
		<x>0</x>
		<y>0</y>
		<z>1</z>
	</axis>
	<center>
       <x-m> 0.1744  </x-m>
       <y-m> -0.0000  </y-m>
       <z-m> 0.29 </z-m>
	</center>
</animation>




<!-- rudder -->

 <animation>
    <type>rotate</type>
    <object-name>rudder</object-name>
    <property>surface-positions/rudder-pos-norm</property>
    <factor> 40</factor>
    <center>
      <x-m> 0.6966 </x-m>
      <y-m>  -0.0 </y-m>
      <z-m> -0.02 </z-m>
    </center>
    <axis>
      <x> 0 </x>
      <y> 0 </y>
      <z> 1 </z>
    </axis>
  </animation>


<!-- gears -->

  <animation>
    <type>spin</type>
    <object-name>gearFL</object-name>
    <property>gear/gear[0]/rollspeed-ms</property>
    <factor> 30 </factor>
    <axis>
      <x1-m> 0.0419 </x1-m>
      <y1-m> 0.13 </y1-m>
      <z1-m> -0.105 </z1-m>
      <x2-m> 0.0419</x2-m>
      <y2-m> -0.13 </y2-m>
      <z2-m> -0.105 </z2-m>
    </axis>
  </animation>

  <animation>
    <type>spin</type>
    <object-name>gearFR</object-name>
    <property>gear/gear[1]/rollspeed-ms</property>
    <factor> 30 </factor>
    <axis>
      <x1-m> 0.0419 </x1-m>
      <y1-m> 0.13 </y1-m>
      <z1-m> -0.105 </z1-m>
      <x2-m> 0.0419</x2-m>
      <y2-m> -0.13 </y2-m>
      <z2-m> -0.105 </z2-m>
    </axis>
  </animation>

  <animation>
    <type>spin</type>
    <object-name>gearR</object-name>
    <property>gear/gear[2]/rollspeed-ms</property>
    <factor> 30 </factor>
    <axis>
      <x1-m> 0.7328 </x1-m>
      <y1-m> 0.0 </y1-m>
      <z1-m> -0.002 </z1-m>
      <x2-m> 0.7328 </x2-m>
      <y2-m> -0.0 </y2-m>
      <z2-m> -0.002 </z2-m>
    </axis>
  </animation>



  <animation>
    <type>noshadow</type>
	<object-name>rotorBlade1Fblur</object-name>
	<object-name>rotorBlade2Bblur</object-name>
  </animation>


	<model>
		<path>Aircraft/TF-G2/Models/Effects/shadow.xml</path>
	</model>

</PropertyList>
