Maintainers
===========

See [the documentation on Maintainers](https://docs.px4.io/main/en/contribute/maintainers.html) to learn about the role of the maintainers and the process to become one.

**Active Maintainers**

| Name                    | Sector | GitHub | Chat | email
|-------------------------|--------|--------|------|----------------
| <PERSON><PERSON><PERSON> | Founder | [@LorenzMeier](https://github.com/LorenzMeier) |  | <<EMAIL>>
| <PERSON> | Architecture | [@dagar](https://github.com/dagar) | daniel_agar | <<EMAIL>>
| Beat Küng | Architecture | [@bkueng](https://github.com/bkueng) | beatkueng | <<EMAIL>>
| <PERSON> | CI / Testing | [@mrpollo](https://github.com/mrpollo) | rroche | <<EMAIL>>
| <PERSON><PERSON> | State Estimation | [@bresch](https://github.com/bresch) | mbresch |
| <PERSON> Riseborough | State Estimation | [@priseborough](https://github.com/priseborough) |  |
| David Sidrane | RTOS / NuttX | [@davids5](https://github.com/davids5) | david_s5 | <<EMAIL>>
| Jayoung Lim | Simulation | [@Jaeyoung-Lim](https://github.com/Jaeyoung-Lim) | jaeyounglim. | <<EMAIL>>
| Beniamino Pozzan | ROS 2 | [@beniaminopozzan](https://github.com/beniaminopozzan) | beniaminopozzan | <<EMAIL>>
| Matthias Grob | Multirotor | [@MaEtUgR](https://github.com/MaEtUgR) | maetugr |
| Silvan Fuhrer | Fixed-Wing / VTOL | [@sfuhrer](https://github.com/sfuhrer) | sfuhrer |
| Christian Friedrich | Rover | [@chfriedrich98](https://github.com/chfriedrich98) | christian982564 |
| Pedro Roque | Spacecraft | [@Pedro-Roque](https://github.com/Pedro-Roque) | .pedroroque | <<EMAIL>>
| Jacob Dahl | Simulation | [@dakejahl](https://github.com/dakejahl) | dakejahl | <<EMAIL>>


**Documentation Maintainers**

| Name | GitHub | Chat | email
|------|--------|------|----------------------
| Hamish Willee  | [@hamishwillee](https://github.com/hamishwillee) | hamishwillee |

**Release Managers**

| Name | GitHub | Chat | email
|------|--------|------|----------------------
| Ramón Roche | [@mrpollo](https://github.com/mrpollo) | rroche | <<EMAIL>>
| Daniel Agar | [@dagar](https://github.com/dagar) | daniel_agar | <<EMAIL>>

**Retired Maintainers**

| Name | GitHub | Chat | email
|------|--------|------|----------------------
|  |  |  |
