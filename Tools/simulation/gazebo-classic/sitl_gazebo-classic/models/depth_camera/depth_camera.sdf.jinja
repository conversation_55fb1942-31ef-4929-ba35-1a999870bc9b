<!-- DO NOT EDIT: Generated from depth_camera.sdf.jinja -->
<?xml version="1.0" ?>
<sdf version="1.5">
  <model name="depth_camera">
    <pose>0 0 0.035 0 0 0</pose>
    <link name="link">
      <inertial>
        <pose>0.01 0.025 0.025 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://realsense_camera/meshes/realsense.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <sensor name="depth_camera" type="depth">
        {%- if ros_version == '2' -%}
        <always_on>1</always_on>
        <update_rate>10</update_rate>
        <camera name="camera_name">
          <horizontal_fov>1.5009831567</horizontal_fov>
          <image>
            <format>R8G8B8</format>
            <width>848</width>
            <height>480</height>
          </image>
          <clip>
            <near>0.001</near> <!-- Intel RealSense D455 -->
            <far>65.535</far> <!-- Intel RealSense D455 -->
          </clip>
          <distortion>
            <k1>0.0</k1>
            <k2>0.0</k2>
            <k3>0.0</k3>
            <p1>0.0</p1>
            <p2>0.0</p2>
            <center>0.5 0.5</center>
          </distortion>
        </camera>

        <plugin name="camera_controller" filename="libgazebo_ros_camera.so">
          <camera_name>camera</camera_name>
          <frame_name>camera_link</frame_name>
          <hack_baseline>0.07</hack_baseline>
          <min_depth>0.2</min_depth>
        </plugin>
        {%- else -%}
        <update_rate>10</update_rate>
        <camera>
          <horizontal_fov>1.5009831567</horizontal_fov>
          <image>
            <format>R8G8B8</format>
            <width>848</width>
            <height>480</height>
          </image>
          <clip>
            <near>0.001</near> <!-- Intel RealSense D455 -->
            <far>65.535</far> <!-- Intel RealSense D455 -->
          </clip>
        </camera>
        <plugin filename="libgazebo_ros_openni_kinect.so" name="camera_controller">
          <cameraName>camera</cameraName>
          <robotNamespace></robotNamespace>
          <alwaysOn>true</alwaysOn>
          <updateRate>10</updateRate>
          <pointCloudCutoff>0.2</pointCloudCutoff>
          <pointCloudCutoffMax>65.535</pointCloudCutoffMax>
          <imageTopicName>rgb/image_raw</imageTopicName>
          <cameraInfoTopicName>rgb/camera_info</cameraInfoTopicName>
          <depthImageTopicName>depth/image_raw</depthImageTopicName>
          <depthImageCameraInfoTopicName>depth/camera_info</depthImageCameraInfoTopicName>
          <pointCloudTopicName>depth/points</pointCloudTopicName>
          <frameName>camera_link</frameName>
          <distortion_k1>0.0</distortion_k1>
          <distortion_k2>0.0</distortion_k2>
          <distortion_k3>0.0</distortion_k3>
          <distortion_t1>0.0</distortion_t1>
          <distortion_t2>0.0</distortion_t2>
        </plugin>
        {%- endif -%}
      </sensor>
    </link>
  </model>
</sdf>
