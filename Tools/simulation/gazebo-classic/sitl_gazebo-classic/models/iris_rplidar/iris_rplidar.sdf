<?xml version="1.0" ?>
<sdf version='1.5'>
  <model name='iris_rplidar'>

    <include>
      <uri>model://iris</uri>
    </include> 

    <include>
      <uri>model://lidar</uri>
      <pose>-0.12 0 0 0 3.1415 0</pose>
    </include>
    <joint name="lidar_joint" type="fixed">
      <child>lidar::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

    <include>
      <uri>model://flow_cam</uri>
      <pose>-0.12 0.05 0 0 1.5708 1.5708</pose>
    </include>
    <joint name="flow_joint" type="revolute">
      <child>camera::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

    <include>
      <uri>model://rplidar</uri>
      <pose>0 0 0.1 0 0 0</pose>
    </include>
    <joint name="rplidar_joint" type="revolute">
      <child>rplidar::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

  </model>
</sdf>
<!-- vim: set noet fenc=utf-8 ff=unix sts=0 sw=4 ts=4 : -->

