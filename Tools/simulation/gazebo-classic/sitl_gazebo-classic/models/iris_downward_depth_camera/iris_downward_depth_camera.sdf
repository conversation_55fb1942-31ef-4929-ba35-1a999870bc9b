<?xml version="1.0" ?>
<sdf version="1.5">
  <model name='iris_downward_depth_camera'>
    <include>
      <uri>model://iris</uri>
    </include>

    <include>
      <uri>model://depth_camera</uri>
      <pose>0.108 0 -0.01 0 1.5708 0</pose>
    </include>
    <joint name="depth_camera_joint" type="revolute">
      <child>depth_camera::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>
  </model>
</sdf>
