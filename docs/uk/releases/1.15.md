# PX4-Autopilot v1.15 Release Notes

<Badge type="tip" text="Stable"/>

The v1.15 release brings lots of new upgrades and fixes, thanks in part to the tremendous community response to the 1.14 release.
In particular, PX4 v1.15 brings significant improvements for developers and integrators using PX4 as a target through [ROS 2](../ros2/index.md) and the [uXRCE-DDS middleware](../middleware/uxrce_dds.md).
In addition to networking and middleware updates, the new [PX4 ROS 2 Interface Library](../ros2/px4_ros2_interface_lib.md) allows flight modes written as ROS 2 applications to be peers of PX4 flight modes.

This release also brings new hardware support from sensors to drivers, thanks to our growing network of partners from across the industry.
In this release you will enjoy updated support for common hardware options, in part sponsored by the manufacturer themselves.
We are thankful for their support!

Lastly, there have been many improvements and bug fixes in the navigation code, which should result in a more enjoyable experience and straightforward debugging.

All of this makes PX4 v1.15 an ideal release, especially for anyone already enjoying the many benefits of version 1.14.
We hope more users try this release and report any issues to the developer team on GitHub. ([Learn how to raise an issue here](../contribute/support.md#issue-bug-reporting)).

Please continue reading for [upgrade instructions](#upgrade-guide).

## Основні зміни

- Major improvements to navigation
- New and updated hardware support
- Dynamic Modes API
- <Badge type="warning" text="Experimental"/>[PX4 ROS 2 Interface Library](../ros2/px4_ros2_interface_lib.md) that simplifies controlling PX4 from ROS 2.

## Інструкції для оновлення

- **Gazebo** has replaced the **jMAVSim** simulator as the default simulation engine for PX4-Autopilot.
  If you're using JMAVSim, please move to Gazebo, as that is where we will be focusing our future efforts.
- The `SYS_MC_EST_GROUP` parameter has been removed, and there are now dedicated parameters for selecting an estimator.
  Continue reading for a description of the changes.

## Інші зміни

### Підтримка обладнання

- **[Pixhawk Standards]** Pixhawk Autopilot Bus (PAB) Split Base ID and FMUM ID ([PX4-Autopilot#22639](https://github.com/PX4/PX4-Autopilot/pull/22639))
- **[Pixhawk Standards] [FMUv6X]** Increase TELEM2 RX buffer size to increase throughput ([PX4-Autopilot#21660](https://github.com/PX4/PX4-Autopilot/pull/21660))
- **[New Hardware]** Support for FMUv6XRT - Pixhawk 6X-RT ([PX4-Autopilot#22263](https://github.com/PX4/PX4-Autopilot/pull/22263))
- **[New Hardware]** MicoAir H743 ([PX4-Autopilot#23218](https://github.com/PX4/PX4-Autopilot/pull/23218))
- **[New Hardware]** ARK Mosaic-X5 RTK GPS ([PX4-Autopilot#22735](https://github.com/PX4/PX4-Autopilot/pull/22735))
- **[New Hardware]** ARK Pi6X Flow ([PX4-Autopilot#22829](https://github.com/PX4/PX4-Autopilot/pull/22829))
- **[Feature]** Add power module selector for Auterion power modules ([PX4-Autopilot#22073](https://github.com/PX4/PX4-Autopilot/pull/22073))
- **[Lights]** Add LP5562 RGBLED driver ([PX4-Autopilot#21649](https://github.com/PX4/PX4-Autopilot/pull/21649))
- **[Holybro]** Enable EKF2 on KakuteH7 / KakuteH7v2 / KakuteH7mini ([PX4-Autopilot#21961](https://github.com/PX4/PX4-Autopilot/pull/21961))
- **[Cubepilot]** Add support for 4. variant of Orange+ ([PX4-Autopilot#22039](https://github.com/PX4/PX4-Autopilot/pull/22039))
- **[3DR]** Control Zero Classic Support Improvements ([PX4-Autopilot#22971](https://github.com/PX4/PX4-Autopilot/pull/22971))

### Загальні

- `SYS_MC_EST_GROUP` has been removed and there are now dedicated parameters [EKF2_EN](../advanced_config/parameter_reference.md#EKF2_EN), [ATT_EN](../advanced_config/parameter_reference.md#ATT_EN), and [LPE_EN](../advanced_config/parameter_reference.md#LPE_EN) for [Switching State Estimators](../advanced/switching_state_estimators.md) ([PX4-Autopilot#22567](https://github.com/PX4/PX4-Autopilot/pull/22567))
  - Most setups should enable [EKF2_EN](../advanced_config/parameter_reference.md#EKF2_EN) (the default).
- **[Bootloader]** add force-erase option and bootloader version ([PX4-Autopilot#22777](https://github.com/PX4/PX4-Autopilot/pull/22777))
- **[uORB]** Allow for more than 255 uORB messages ([PX4-Autopilot#21923](https://github.com/PX4/PX4-Autopilot/pull/21923))
- **[Feature]** <Badge type="warning" text="Experimental"/> Zenoh Pico support ([PX4-Autopilot#22017](https://github.com/PX4/PX4-Autopilot/pull/22017))
- **[Feature]** Improve mission resume, e.g. replay gimbal and camera commands of a survey ([PX4-Autopilot#21710](https://github.com/PX4/PX4-Autopilot/pull/21710))
- **[Feature]** Resume mission with flight speed from previous mission items ([PX4-Autopilot#21714](https://github.com/PX4/PX4-Autopilot/pull/21714))
- **[Feature]** Warn user when navigation failure is imminent ([PX4-Autopilot#21876](https://github.com/PX4/PX4-Autopilot/pull/21876))
- **[Navigator]** Geofence improvements ([PX4-Autopilot#22394](https://github.com/PX4/PX4-Autopilot/pull/22394))
- **[Improvement]** Commander: trigger warning when arming denied due to check failure ([PX4-Autopilot#22885](https://github.com/PX4/PX4-Autopilot/pull/22885))
- **[Improvement]** Commander: make failsafe warnings shorter ([PX4-Autopilot#22886](https://github.com/PX4/PX4-Autopilot/pull/22886))
- Support for a remote parameter database ([PX4-Autopilot#22836](https://github.com/PX4/PX4-Autopilot/pull/22836))
- New platform independent UART interface ([PX4-Autopilot#21723](https://github.com/PX4/PX4-Autopilot/pull/21723))
- **[Improvement]** A one-way [Kill Gesture](../config/safety.md#kill-gesture) has been added, allowing you to stop the motors at any time. This is disabled by default. [PX4-Autopilot#18509](https://github.com/PX4/PX4-Autopilot/pull/18509).
- **[Feature]** [Compass calibration for large vehicles](../config/compass.md#large-vehicle-calibration).
  This method is designed for vehicles where full rotation is impractical or impossible.
  ([PX4-Autopilot#15235](https://github.com/PX4/PX4-Autopilot/pull/15235), [PX4-Autopilot#15687](https://github.com/PX4/PX4-Autopilot/pull/15687)).

### Управління

- **[Offboard]** [ros2 offboard control](../flight_modes/offboard.md#ros-2-messages) allows for direct motors and servo control ([PX4-Autopilot#22222](https://github.com/PX4/PX4-Autopilot/pull/22222))

### Оцінки

- Derivation changed to **Error-State Kalman Filter** ([PX4-Autopilot#22262](https://github.com/PX4/PX4-Autopilot/pull/22262))
  Main advantages of the "error state" derivation compared to the current "total state":
  - Better convergence properties (less sensitive to initial heading accuracy)
  - More stable numerically
  - Less computationally expensive
  - Correct way of describing the quaternion uncertainty using Lie group theory
- Use Joseph stabilized covariance update algorithm for better covariance stability and allow use of "consider states" (inactive states with non-zero variance) ([PX4-Autopilot#22770](https://github.com/PX4/PX4-Autopilot/pull/22770))
- Covariance prediction, measurement jacobians, state struct and covariance index auto-generated using SymForce
- Manual position update throught MAVLink (`MAV_CMD_EXTERNAL_POSITION_ESTIMATE`)
- Add Auxiliary Global Position (AGP) fusion (for e.g.: external map matching vision algorithm)

**Mag:**

- New mag type "**Init**" (`EKF2_MAG_TYPE`). In this mode, the magnetometer is only used to initialize the heading of the filter.
  The heading is then only estimated using gyro prediction and position/velocity updates (e.g.: **GNSS**).
- Mag checks rework (`EKF2_MAG_CHECK`):
  - Parameterize strength tolerance (`EKF2_MAG_CHK_STR`)
  - Add inclination check (`EKF2_MAG_CHK_INC`)
- Rework mag heading fusion to correctly consider mag bias and earth field uncertainties
- Allow mag bias estimation in all mag modes
- Improve heading convergence speed after bad heading initialization when using GNSS aiding

**IMU:**

- Improve gyro and accel bias estimation numerical stability (change delta angle and delta velocity bias to accel and gyro bias)

**Optical Flow:**

- Can now run before takeoff (useful for bench testing)

**Yaw Emergency Estimator:**

- Change to Error-State derivation for better performances
- Can now run before takeoff to initialize the heading of the EKF

### Датчики

- NXP UWB SR150 Driver fixes ([PX4-Autopilot#1123](https://github.com/PX4/PX4-Autopilot/pull/21124))
- Septentrio GPS updates ([PX4-Autopilot#23386](https://github.com/PX4/PX4-Autopilot/pull/23386))
- Ublox add UBX-RXM-RTCM for RTCM input status ([PX4-Autopilot#21595](https://github.com/PX4/PX4-Autopilot/pull/21595))
- Add TDK IIM42653 IMU driver ([PX4-Autopilot#21019](https://github.com/PX4/PX4-Autopilot/pull/21019))
- **[Feature]** Make airspeed sensor configuration easier and consistent with other sensors ([PX4-Autopilot#22510](https://github.com/PX4/PX4-Autopilot/pull/22510))
  - **[New Parameter]** `SYS_HAS_NUM_ASPD`, `FW_USE_AIRSPD`
- **[logger]**: log all GPS data ([PX4-Autopilot#22421](https://github.com/PX4/PX4-Autopilot/pull/22421))

### Симуляція

- **[[Gazebo](../sim_gazebo_gz/index.md)]** Support for [Advanced Plane](../sim_gazebo_gz/vehicles.md#advanced-plane), a simulated fixed-wing vehicle that provides better aerodynamic simulation than the regular plane ([PX4-Autopilot#22167](https://github.com/PX4/PX4-Autopilot/pull/22167), [gz-sim#2185](https://github.com/gazebosim/gz-sim/pull/2185))
- **[[Gazebo](../sim_gazebo_gz/index.md)]** The environment variable `PX4_SIM_MODEL` can now be used to indicate the simulation model ([PX4-Autopilot#22400](https://github.com/PX4/PX4-Autopilot/pull/22400))
  - This supersedes `PX4_GZ_MODEL`, which is now deprecated.
- **[[Gazebo](../sim_gazebo_gz/index.md)]** Separation of Gazebo and PX4 SITL ([PX4-Autopilot#22467](https://github.com/PX4/PX4-Autopilot/pull/22467))
  - The two parts of the simulation are now separated.
  - They can be independently launched in any order, and even run on different hosts across a network.
  - Gazebo additional supports drag-and-drop via the resource spawner in Gazebo GUI
- [JMAVSim Simulator](../sim_jmavsim/index.md) is now [community supported](../simulation/community_supported_simulators.md).
  - It has been deprecated as part of the official PX4 development toolchain in order to focus our efforts on [Gazebo](../sim_gazebo_gz/index.md)
- **[Tools/simulation/gz]**: load ApplyLinkWrench plugin by default ([PX4-Autopilot#21874](https://github.com/PX4/PX4-Autopilot/pull/21874))
- Allow user to specify Gazebo Garden/Harmony render engine in simulations ([PX4-Autopilot#22693](https://github.com/PX4/PX4-Autopilot/pull/22693))
- Add support for quad tailsitter VTOL in SITL Gazebo ([PX4-Autopilot#20558](https://github.com/PX4/PX4-Autopilot/pull/20558))
- **[Feature]** Parameter for enabling/disabling PX4's internal battery simulator in SITL ([PX4-Autopilot#22710](https://github.com/PX4/PX4-Autopilot/pull/22710))
  - **[New Parameter]** `SIM_BAT_ENABLE`
- **[gz-bridge]** Fix GZ timeout for slow starting simulations ([PX4-Autopilot#22194](https://github.com/PX4/PX4-Autopilot/pull/22194))

### Ethernet

- **[[Ethernet](../advanced_config/ethernet_setup.md)]** The default static IP address (used as a fallback for DHCP) has changed from `***********` to `**********` ([PX4-Autopilot#22517](https://github.com/PX4/PX4-Autopilot/pull/22517))
  - This avoids potential conflicts where the`192.168.0.x` range is used by routers for WIFI/ethernet networks

### MAVLink

- **[Gimbal]** fix auto RC and MAVLink mode ([PX4-Autopilot#23236](https://github.com/PX4/PX4-Autopilot/pull/23236))

### uXRCE-DDS Interface

- **[[uXRCE-DDS](../middleware/uxrce_dds.md)]** [DDS Topics YAML](../middleware/uxrce_dds.md#dds-topics-yaml) now allows the use of `subscription_multi` to specify that indicated ROS 2 topics are sent to a separate uORB topic instance reserved for ROS 2 ([PX4-Autopilot#22266](https://github.com/PX4/PX4-Autopilot/pull/22266))
  - This allows PX4 to differentiate between updates from ROS and those from PX4 uORB publishers
  - With this change ROS2 users can now decide if the messages that they are sending to PX4 will overlap with the existing uORB ones or be kept in separate instances.
- Move VOXL from microdds client to UXRCE-DDS client ([PX4-Autopilot#22997](https://github.com/PX4/PX4-Autopilot/pull/22997))
- Add parameter to disable time synchronization between Agent and Client ([PX4-Autopilot#21757](https://github.com/PX4/PX4-Autopilot/pull/21757))
  - **[New Parameter]** `UXRCE_DDS_SYNCT`
  - **[Documentation]** [Middleware uXRCE-DDS](../middleware/uxrce_dds.md#starting-the-client) / [ROS 2](../ros2/user_guide.md#ros-gazebo-and-px4-time-synchronization) / [Gazebo Simulation](../sim_gazebo_gz/index.md#change-simulation-speed)

### ROS 2

- **[Feature]** Dynamic Modes API ([PX4-Autopilot#20707](https://github.com/PX4/PX4-Autopilot/pull/20707))
- **[Feature]** **[[PX4 ROS 2 Interface Library](../ros2/px4_ros2_interface_lib.md)]**<Badge type="warning" text="Experimental"/>: A new C++ library that simplifies controlling PX4 from ROS 2
  - Supports adding flight modes in ROS 2 that are peers of the PX4 modes running on the flight controller.
  - Modes behave the same as an FMU-internal modes
  - Propagated to GCS and selectable by users
  - Interface for arming checks and mode requirements
  - Integrated into the failsafe state machine
- **[Feature]** Multicopter Go-to Interface ([PX4-Autopilot#22375](https://github.com/PX4/PX4-Autopilot/pull/22375))

## Platform Changes

### Мульти-Ротор

- **[New Mode]** [Throw launch](../flight_modes_mc/throw_launch.md)<Badge type="warning" text="Experimental"/>: Start a multicopter by throwing it into the air. [PX4-Autopilot#21170](https://github.com/PX4/PX4-Autopilot/pull/21170)
- **[New Mode]** [Position Slow Mode](../flight_modes_mc/position_slow.md): A slower version of _Position mode_, where the maximum horizontal velocity, vertical velocity and yaw-rate axes can be configured to lower values (using parameters, RC controller knobs/switches, or MAVLink). [PX4-Autopilot#22102](https://github.com/PX4/PX4-Autopilot/pull/22102)
- **[Helicopter]** add tail servo support [PX4-Autopilot#21274](https://github.com/PX4/PX4-Autopilot/pull/21274)
- **[Helicopter]** Add back support for coaxial helicopters with the new actuator allocation. [Documentation](../frames_helicopter/index.md), [PX4-Autopilot#22083](https://github.com/PX4/PX4-Autopilot/pull/22083)
- **[Improvement]** Multicopter position control parameter descriptions ([PX4-Autopilot#21729](https://github.com/PX4/PX4-Autopilot/pull/21729))
- **[Improvement]** Default motor PWM limits minimum 1100 maximum 1900. Default changed from 900us to 1000us! [Documentation](../advanced_config/esc_calibration.md), [PX4-Autopilot#21513](https://github.com/PX4/PX4-Autopilot/pull/21513)

### VTOL

- VTOL back-transition: expose tilting time as parameter and reduce overall duration ([PX4-Autopilot#21760](https://github.com/PX4/PX4-Autopilot/pull/21760))
  - **[New Parameter]** `VT_BT_TILT_DUR`
- **[Feature]** VTOL Tailsitter: add automatic pitch ramps also in Stabilized mode ([PX4-Autopilot#22584](https://github.com/PX4/PX4-Autopilot/pull/22584))
- **[Improvement]** VTOL forward thrust assist: treat Descend as Land mode ([PX4-Autopilot#22843](https://github.com/PX4/PX4-Autopilot/pull/22843))
- Refactor uncommanded descend Quad-Chute ([PX4-Autopilot#21598](https://github.com/PX4/PX4-Autopilot/pull/21598))
  - **[New Parameter]** `VT_QC_ALT_LOSS`

### Fixed-Wing

- [Simplified airspeed sensor configuration](../config_vtol/vtol_without_airspeed_sensor.md) ([PX4-Autopilot#22510](https://github.com/PX4/PX4-Autopilot/pull/22510)):
  - Replace parameter `CBRK_AIRSPD_CHK` with [SYS_HAS_NUM_ASPD](../advanced_config/parameter_reference.md#SYS_HAS_NUM_ASPD) and renamed parameter `FW_ARSP_MODE` to [FW_USE_AIRSPD](../advanced_config/parameter_reference.md#FW_USE_AIRSPD).
  - To be able to arm without an airspeed sensor set `SYS_HAS_NUM_ASPD` to 0.
  - To not use the airspeed sensor data in the controller, set `FW_USE_AIRSPD` to 0.
- **[Improvement]** Fixed-wing high (dynamic) wind hardening ([PX4-Autopilot#21764](https://github.com/PX4/PX4-Autopilot/pull/21764))
- Attitude controller cleanup ([PX4-Autopilot#22472](https://github.com/PX4/PX4-Autopilot/pull/22472))
- **[Feature]** FW TECS: rename `FW_T_THR_DAMP` to `FW_T_THR_DAMPING` and `FW_T_I_GAIN_THR` to `FW_T_THR_INTEG` as the interpretation of these parameters has changed ([PX4-Autopilot#22548](https://github.com/PX4/PX4-Autopilot/pull/22548))
- **[Feature]** Enable high rate logging for VTOL ([PX4-Autopilot#22700)](https://github.com/PX4/PX4-Autopilot/pull/22700))
- **[Feature]** Add functionality to disable rate control of certain axes in fixed-wing Acro mode ([PX4-Autopilot#21566](https://github.com/PX4/PX4-Autopilot/pull/21566))
  - **[New Parameter]** `FW_ACRO_AXS_CTRL`
- Add Fixedwing path following as a separate controller state ([PX4-Autopilot#21376](https://github.com/PX4/PX4-Autopilot/pull/21376))
- Added PerformanceModel for fixed wing ([PX4-Autopilot#22091](https://github.com/PX4/PX4-Autopilot/pull/22091))
  - **[New Parameter]** `FW_S_CEILING`

### Ровер

- [Aion R1](../frames_rover/aion_r1.md)<Badge type="warning" text="Experimental"/>: ESC Driver for Roboclaw motor controller.
  This comes with build instructions and support for the Aion R1, a new differential drive rover, along with information about integrating the Roboclaw motor controller.
- Add dedicated Rover build variants to px4/fmu-{v5,v5x,v6c,v6x} ([PX4-Autopilot#22675](https://github.com/PX4/PX4-Autopilot/pull/22675))

## Changelog

[Full changelog available on Github here. (v1.14.0...v1.15.0)](https://github.com/PX4/PX4-Autopilot/compare/v1.14.0...v1.15.0)
