# 액츄에이터

This section contains topics about the core actuators used for flight control (ESC/motors, and servos), and how they are assigned to the flight controller outputs, configured, and calibrated.

- [Actuator Allocation](../config/actuators.md) — Configure flight controller outputs for specific functions and ESC/servo types.

- [ESCs & Motors](../peripherals/esc_motors.md) — ESCs such as [DShot](../peripherals/dshot.md) (recommended) and DroneCAN.

- [ESC Calibration](../advanced_config/esc_calibration.md) — Calibration for PWM ESC (not required for DShot/CAN ESC/servos).

## See Also

- [Peripherals](../peripherals/index.md) - includes non-core actuators such as grippers, parachutes, etc.