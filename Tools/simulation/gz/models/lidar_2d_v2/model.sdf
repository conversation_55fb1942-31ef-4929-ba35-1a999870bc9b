<?xml version="1.0" ?>
<sdf version="1.6">
  <model name="lidar_2d_v2">
    <link name="link">
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 0.0435 0 0 0</pose>
        <mass>0.37</mass>
        <inertia>
          <ixx>0.00034437749999999994</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.00034437749999999994</iyy>
          <iyz>0</iyz>
          <izz>0.00022199999999999998</izz>
        </inertia>
      </inertial>
      <collision name="collision_base">
        <pose>0 0 0.0205 0 0 0</pose>
        <geometry>
          <box>
            <size>0.06 0.06 0.041</size>
          </box>
        </geometry>
      </collision>
      <collision name="collision_mid">
        <pose>0 0 0.055 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.025</radius>
            <length>0.028</length>
          </cylinder>
        </geometry>
      </collision>
      <collision name="collision_top">
        <pose>0 0 0.078 0 0 0</pose>
        <geometry>
          <box>
            <size>0.056 0.056 0.018</size>
          </box>
        </geometry>
      </collision>
      <visual name="visual">
        <geometry>
          <mesh>
            <uri>model://lidar_2d_v2/meshes/lidar_2d_v2.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <sensor name="lidar_2d_v2" type="gpu_lidar">
        <pose>0 0 0.055 0 0 0</pose>
        <ray>
          <scan>
            <horizontal>
              <samples>1080</samples>
              <resolution>1</resolution>
              <min_angle>-2.356195</min_angle>
              <max_angle>2.356195</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.1</min>
            <max>30</max>
            <resolution>0.01</resolution>
          </range>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.001</stddev>
          </noise>
        </ray>
        <always_on>0</always_on>
        <update_rate>30</update_rate>
        <visualize>false</visualize>
      </sensor>
    </link>
  </model>
</sdf>
