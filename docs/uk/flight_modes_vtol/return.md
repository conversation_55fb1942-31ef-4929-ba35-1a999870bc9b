# Режим Повернення (VTOL)

<img src="../../assets/site/position_fixed.svg" title="Position fix required (e.g. GPS)" width="30px" />

Режим _повернення_ використовується для _політів засобом перевезення до безпечного місця_ при наявності вільного шляху до безпечного пункту призначення, де він може зачекати (зависнути або обійти колом) або сісти.

Літальні апарати типу VTOL за замовчуванням використовують тип повернення до призначення [місії посадки/точка збору](../flight_modes/return.md#mission-landing-rally-point-return-type-rtl-type-1).
У цьому типі повернення апарат піднімається на мінімальну безпечну висоту над перешкодами (за необхідності), а потім напряму летить до точки збору або початкової точки призначення місії (яка є найближчою), або додому, якщо жодна з точок збору або місійний маршрут посадки не визначені.
Якщо призначенням є місійний маршрут посадки, апарат потім дотримуватиметься маршруту для посадки.
Якщо призначенням є точка збору або домашня позиція, апарат повернеться додому і сяде.

Літальний апарат повернеться за допомогою режиму польоту (MC або FW), який він використовував у той момент, коли був активований режим повернення.
Загалом, він буде дотримуватися того ж поведінкового зразка режиму повернення, що й відповідний тип транспортного засобу, але завжди перейде до режиму MC (якщо потрібно) перед посадкою.

VTOL підтримує [інші типи повернення PX4](../flight_modes/return.md#return-types-rtl-type), включаючи повернення до дому / точки збору, маршрут місії і найближче безпечне місце призначення. Рекомендується використовувати тип за замовчуванням.
За замовчуванням рекомендується використовувати цей тип.

::: info

- Режим автоматичний - для керування апаратом не потрібно втручання користувача.
- Режим вимагає глобальної оцінки 3D-позиції (з GPS або виведеної з [локальної позиції](../ros/external_position_estimation.md#enabling-auto-modes-with-a-local-position)).
  - Літаючі транспортні засоби не можуть переключатися на цей режим без глобального положення.
  - Літаючі транспортні засоби перейдуть в режим аварійної безпеки, якщо втратять оцінку положення.
- Режим вимагає встановленої домашньої позиції.
- Режим перешкоджає зброюванню (транспортний засіб повинен бути зброєний при переході на цей режим).
- Перемикачі керування RC можуть використовуватися для зміни режимів польоту на будь-якому транспортному засобі.
- Рух стіків радіокерування ігнорується.

<!-- https://github.com/PX4/PX4-Autopilot/blob/main/src/modules/commander/ModeUtil/mode_requirements.cpp -->

:::

## Технічний підсумок

Літальні апарати типу VTOL за замовчуванням використовують тип повернення до призначення [місії посадки/ точки збору](../flight_modes/return.md#mission-landing-rally-point-return-type-rtl-type-1), і повертаються, використовуючи режим польоту (MC або FW), який вони використовували в момент активації режиму повернення.

### Повернення режиму фіксованого крила (FW)

Якщо апарат повертається як літак, він:

- Піднімається на безпечну мінімальну висоту повернення, визначену за допомогою [RTL_RETURN_ALT](#RTL_RETURN_ALT) (безпечно вище будь-яких очікуваних перешкод).
  Транспортний засіб підтримує свою початкову висоту, якщо вона вище, ніж мінімальна висота повернення.
  <!-- Note that return altitude cannot be configured using the "cone" parameter in fixed-wing vehicles. -->

- Летить прямим шляхом на постійній висоті до призначення, яким буде найближча з точки старту місійного маршруту посадки та будь-яка точка збору, або домашня локація, якщо місійний маршрут посадки або точки збору не визначені.

- Якщо призначенням є місійний маршрут посадки, апарат буде дотримуватися маршруту для посадки.

  Місійний маршрут посадки для літального апарату типу VTOL складається з [MAV_CMD_DO_LAND_START](https://mavlink.io/en/messages/common.html#MAV_CMD_DO_LAND_START), однієї або кількох маршрутних точок розташування та [MAV_CMD_NAV_VTOL_LAND](https://mavlink.io/en/messages/common.html#MAV_CMD_NAV_VTOL_LAND).

- Якщо призначенням є точка збору або домашня локація, апарат:

  - Переводиться в режим ожидання/спірального спуску на висоту [RTL_DESCEND_ALT](#RTL_DESCEND_ALT).
  - Кружляє протягом короткого часу, визначеного [RTL_LAND_DELAY](#RTL_LAND_DELAY).
  - Повертається по напрямку до призначення (центр кругового руху).
  - Переходить в режим MC і сідає.

    Зауважте, що [NAV_FORCE_VT](../advanced_config/parameter_reference.md#NAV_FORCE_VT) ігнорується: апарат завжди сідає як мультикоптер для цих призначень.

## Режим мультикоптера (MC) Повернення

Якщо повертається у вигляді мультикоптера:

- Поведінка залишається такою самою, за винятком того, що транспортний засіб летить як багтороторний і дотримується налаштувань багтороторних засобів.
- Зокрема, якщо здійснюється посадка на точку збору або додому, транспортний засіб використовує [RTL_CONE_ANG](#RTL_CONE_ANG) замість простої [RTL_RETURN_ALT](#RTL_RETURN_ALT) для визначення мінімальної безпечної висоти повернення.
  Для отримання додаткової інформації див. пояснення "конуса" у [Режимі повернення (загальний транспортний засіб) > Мінімальна висота повернення](../flight_modes/return.md#minimum-return-altitude).

## Параметри

Параметри RTL перелічені в [Референсі параметрів > Режим повернення](../advanced_config/parameter_reference.md#return-mode).
Якщо використовується місійна посадка, значення [RTL_RETURN_ALT](#RTL_RETURN_ALT) та [RTL_DESCEND_ALT](#RTL_DESCEND_ALT) є важливими.
Інші параметри стають актуальними, якщо призначенням є точка збору або домашня локація.

| Параметр                                                                                                                                                                   | Опис                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a id="RTL_TYPE"></a>[RTL_TYPE](../advanced_config/parameter_reference.md#RTL_TYPE)                                                                   | Тип повернення.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| <a id="RTL_RETURN_ALT"></a>[RTL_RETURN_ALT](../advanced_config/parameter_reference.md#RTL_RETURN_ALT)                            | Висота повернення в метрах (за замовчуванням: 60 м). Якщо вже знаходиться вище цієї висоти, транспортний засіб повернеться на поточну висоту.                                                                                                                                                                                                                                                                                                                                      |
| <a id="RTL_CONE_ANG"></a>[RTL_CONE_ANG](../advanced_config/parameter_reference.md#RTL_CONE_ANG)                                  | Половина кута конуса, який визначає висоту повернення транспортного засобу RTL. Значення (у градусах): 0, 25, 45, 65, 80, 90. Зауважте, що 0 означає "без конуса" (завжди повертається на висоту `RTL_RETURN_ALT` або вище), тоді як 90 показує, що транспортний засіб повинен повертатися на поточну висоту або `RTL_DESCEND_ALT` (яка вище).                                                                               |
| <a id="RTL_DESCEND_ALT"></a>[RTL_DESCEND_ALT](../advanced_config/parameter_reference.md#RTL_DESCEND_ALT)                         | Мінімальна висота повернення і висота, на якій повітряне судно сповільнює або зупиняє своє початкове зниження з вищої висоти повернення (за замовчуванням: 30 м)                                                                                                                                                                                                                                                                                                                                                   |
| <a id="RTL_LAND_DELAY"></a>[RTL_LAND_DELAY](../advanced_config/parameter_reference.md#RTL_LAND_DELAY)                            | Час очікування на висоті `RTL_DESCEND_ALT` перед посадкою (за замовчуванням: 0.5 с) - за замовчуванням цей період короткий, щоб транспортний засіб просто сповільнився, а потім враз відразу приземлився. Якщо встановлено значення -1, система буде кружляти на висоті `RTL_DESCEND_ALT` замість посадки. Затримка надається для того, щоб ви могли налаштувати час для розгортання шасі для посадки (автоматично спрацьовує). |
| <a id="RTL_LOITER_RAD"></a>[RTL_LOITER_RAD](../advanced_config/parameter_reference.md#RTL_LOITER_RAD)                            | [Літаки з фіксованим крилом] Радіус кола очікування (на висоті [RTL_LAND_DELAY](#RTL_LAND_DELAY).                                                                                                                                                                                                                                                                                                                    |
| <a id="MIS_TKO_LAND_REQ"></a>[MIS_TKO_LAND_REQ](../advanced_config/parameter_reference.md#MIS_TKO_LAND_REQ) | Вказує, чи _необхідний_ місійний маршрут посадки або зльоту. Зазвичай літаки з фіксованим крилом встановлюють це для вимоги до посадкового маршруту, але VTOL - ні.                                                                                                                                                                                                                                                                                                                                                   |

## Дивіться також

- [Режим повернення (Загальний)](../flight_modes/return.md)
- [Режим повернення (Мультикоптер)](../flight_modes_mc/return.md)
- [Режим повернення (з нерухомим крилом)](../flight_modes_fw/return.md)
