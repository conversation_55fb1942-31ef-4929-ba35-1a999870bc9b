# NOTE: this workflow is now running on Dronecode / PX4 AWS account.
# - If you want to keep the tests running in GitHub Actions you need to uncomment the "runs-on: ubuntu-latest" lines
#   and comment the "runs-on: [runs-on,runner=..." lines.
# - If you would like to duplicate this setup try setting up "RunsOn" on your own AWS account try https://runs-on.com

name: SITL Tests

on:
  push:
    branches:
    - 'main'
    paths-ignore:
      - 'docs/**'
      - '.github/**'
  pull_request:
    branches:
    - '*'
    paths-ignore:
      - 'docs/**'
      - '.github/**'

jobs:
  build:
    name: Testing PX4 ${{ matrix.config.model }}
    runs-on: [runs-on,runner=16cpu-linux-x64,image=ubuntu22-full-x64,"run-id=${{ github.run_id }}",spot=false]
    container:
      image: px4io/px4-dev-simulation-focal:2021-09-08
      options: --privileged --ulimit core=-1 --security-opt seccomp=unconfined
    strategy:
      fail-fast: false
      matrix:
        config:
          - {model: "iris",          latitude:  "59.617693", longitude: "-151.145316", altitude:  "48", build_type: "RelWithDebInfo" } # Alaska
          - {model: "tailsitter" ,   latitude:  "29.660316", longitude:  "-82.316658", altitude:  "30", build_type: "RelWithDebInfo" } # Florida
          - {model: "standard_vtol", latitude:  "47.397742", longitude:    "8.545594", altitude: "488", build_type: "Coverage" } # Zurich

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Git Ownership Workaround
        run: git config --system --add safe.directory '*'

      - id: set-timestamp
        name: Set timestamp for cache
        run: echo "::set-output name=timestamp::$(date +"%Y%m%d%H%M%S")"

      - name: Cache Key Config
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: sitl-ccache-${{ steps.set-timestamp.outputs.timestamp }}
          restore-keys: sitl-ccache-${{ steps.set-timestamp.outputs.timestamp }}

      - name: Cache Conf Config
        run: |
            mkdir -p ~/.ccache
            echo "base_dir = ${GITHUB_WORKSPACE}" > ~/.ccache/ccache.conf
            echo "compression = true" >> ~/.ccache/ccache.conf
            echo "compression_level = 6" >> ~/.ccache/ccache.conf
            echo "max_size = 120M" >> ~/.ccache/ccache.conf
            echo "hash_dir = false" >> ~/.ccache/ccache.conf
            ccache -s
            ccache -z

      - name: Build PX4
        env:
          PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
        run: make px4_sitl_default

      - name: Cache Post-Run [px4_sitl_default]
        run: ccache -s

      - name: Build SITL Gazebo
        env:
          PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
        run: make px4_sitl_default sitl_gazebo-classic

      - name: Cache Post-Run [sitl_gazebo-classic]
        run: ccache -s

      - name: Download MAVSDK
        run: wget "https://github.com/mavlink/MAVSDK/releases/download/v$(cat test/mavsdk_tests/MAVSDK_VERSION)/libmavsdk-dev_$(cat test/mavsdk_tests/MAVSDK_VERSION)_ubuntu20.04_amd64.deb"

      - name: Install MAVSDK
        run: dpkg -i "libmavsdk-dev_$(cat test/mavsdk_tests/MAVSDK_VERSION)_ubuntu20.04_amd64.deb"

      - name: Check PX4 Environment Variables
        env:
          PX4_HOME_LAT: ${{matrix.config.latitude}}
          PX4_HOME_LON: ${{matrix.config.longitude}}
          PX4_HOME_ALT: ${{matrix.config.altitude}}
          PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
        run: |
            export
            ulimit -a

      - name: Build PX4 / MAVSDK tests
        env:
          PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
          DONT_RUN: 1
        run: make px4_sitl_default sitl_gazebo-classic mavsdk_tests

      - name: Cache Post-Run [px4_sitl_default sitl_gazebo-classic mavsdk_tests]
        run: ccache -s

      - name: Core Dump Settings
        run: |
            ulimit -c unlimited
            echo "`pwd`/%e.core" > /proc/sys/kernel/core_pattern

      - name: Run SITL / MAVSDK Tests
        env:
          PX4_HOME_LAT: ${{matrix.config.latitude}}
          PX4_HOME_LON: ${{matrix.config.longitude}}
          PX4_HOME_ALT: ${{matrix.config.altitude}}
          PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
        run: test/mavsdk_tests/mavsdk_test_runner.py --speed-factor 10 --abort-early --model ${{matrix.config.model}} --upload test/mavsdk_tests/configs/sitl.json --verbose --force-color
        timeout-minutes: 45

      - name: Upload failed logs
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: failed-${{matrix.config.model}}-logs.zip
          path: |
            logs/**/**/**/*.log
            logs/**/**/**/*.ulg
            build/px4_sitl_default/tmp_mavsdk_tests/rootfs/log/**/*.ulg

      - name: Look at Core files
        if: failure() && ${{ hashFiles('px4.core') != '' }}
        run: gdb build/px4_sitl_default/bin/px4 px4.core -ex "thread apply all bt" -ex "quit"

      - name: Upload PX4 coredump
        if: failure() && ${{ hashFiles('px4.core') != '' }}
        uses: actions/upload-artifact@v4
        with:
          name: coredump
          path: px4.core

      - name: Setup & Generate Coverage Report
        if: contains(matrix.config.build_type, 'Coverage')
        run: |
            git config --global credential.helper "" # disable the keychain credential helper
            git config --global --add credential.helper store # enable the local store credential helper
            echo "https://x-access-token:${{ secrets.ACCESS_TOKEN }}@github.com" >> ~/.git-credentials # add credential
            git config --global url."https://github.com/".insteadof **************: # credentials add credential
            mkdir -p coverage
            lcov --directory build/px4_sitl_default --base-directory build/px4_sitl_default --gcov-tool gcov --capture -o coverage/lcov.info

      - name: Upload Coverage Information to Codecov
        if: contains(matrix.config.build_type, 'Coverage')
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          flags: mavsdk
          file: coverage/lcov.info
