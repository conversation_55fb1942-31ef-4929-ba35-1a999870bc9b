# Release 1.12

- [Release 1.12](#release-1-12)
  - [Pre Releases](#pre-releases)
  - [Changes](#changes)
    - [Common](#common)
    - [Sensors](#sensors)
    - [Hardware](#hardware)
    - [MAVLink](#mavlink)
    - [Commander](#commander)
    - [Multicopter](#multicopter)
    - [VTOL](#vtol)
    - [Control](#control)
    - [GPS](#gps)
    - [NuttX](#nuttx)
    - [UAVCAN](#uavcan)

## Pre Releases

- [Beta 4](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta4)
- [Beta 3](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta3)
- [Beta 2](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta2)
- [Beta 1](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta1)

## Changes

### Common

- **RTL Trigger based on remaining flight range ([PR#16399](https://github.com/PX4/PX4-Autopilot/pull/16399))**
  - Calculates time to home, on RTL, taking into account vehicle speed, wind speed, and destination distance/direction
- **Pre-emptive geofence breach ([PR#16400](https://github.com/PX4/PX4-Autopilot/pull/16400))**
  - Triggers a breach if the _predicted_ current trajectory will result in a breach, allowing the vehicle to be re-routed to a safe hold position.
- **Airframe Scripts**
  - The syntax for setting defaults was changed and custom scripts require an update
  - See [PR#16796](https://github.com/PX4/PX4-Autopilot/pull/16796/files#diff-dcf2f5536f47f260e5e0ff3b3fd22eaef6b6c510126463d70affa0eb7bd4d3ddL20) for an example.
- Safety (switch) defaults to off (motors are disarmed, but servos / flaps can move)
- Safety switch is latching: Once it is disabled, it will stay disabled
- Land detector: Expand land detection to use the ground distance if a distance sensor is present
- Added support for IRC Ghost including telemetry

### 传感器

- Magnetometer calibration faster and more robust
  - new soft iron calibration coefficients
  - automatically determine the rotation of external sensors
- Optimized rate control sensor pipeline (minimal inner loop end-to-end latency)

### 硬件

The release includes new hardware support for the following boards, peripherals, and accessories:

- Pixhawk FMUv6U (Read more about this spec on the [Pixhawk GitHub Repository](https://github.com/pixhawk/Pixhawk-Standards))
- Pixhawk FMUv6X (Read more about this spec on the [Pixhawk GitHub Repository](https://github.com/pixhawk/Pixhawk-Standards))
- CUAV X7 / X7Pro ([Read more about this product on the manufacturers site](http://www.cuav.net/en/x7en/)]
- CUAV Nora ([Read more about this product on the manufacturers site](http://www.cuav.net/en/nora/))
- CUAV CAN GPS ([Read more about this product on the manufacturers site](http://www.cuav.net/en/neo-3-2/))
- SP Racing H7 Extreme ([Read more about this product on the manufacturers site](http://seriouslypro.com/spracingh7extreme))
- Bitcraze Crazyflie v2.1 ([Read more about this product on the manufacturers site](https://www.bitcraze.io/products/crazyflie-2-1/))
- ARK CAN Flow ([Read more about this product on the manufacturers site](https://arkelectron.com/product/ark-flow/))
- mRo Ctrl Zero H7 (Experimental) ([Read more about this product on the manufacturers site](https://store.mrobotics.io/mRo-Control-Zero-F7-p/mro-ctrl-zero-f7.htm))

These are removed:

- Removal of discontinued Intel AeroFC

### MAVLink

- **MAVLink Ethernet configuration ([PR#14460](https://github.com/PX4/PX4-Autopilot/pull/14460))**
  - MAVLink Ethernet channel settings such as UDP port, remote port and broadcast mode now can be changed dynamically via parameters.
- **Support for querying `COMPONENT_INFORMATION` ([PR#16039](https://github.com/PX4/PX4-Autopilot/pull/16039))**
  - Parameter metadata is now automatically synchronized in combination with QGC daily.
  - This new message allows any MAVLink system to request rich hierarchical information from an autopilot, i.e., to understand which commands are supported in missions or to get parameter metadata. This message was introduced primarily to help GCS better understand autopilots (RFC: [mavlink#1339](https://github.com/mavlink/mavlink/issues/1339))

### Commander

- **Commander: use control mode flags and cleanup arm/disarm ([PR#16266](https://github.com/PX4/PX4-Autopilot/pull/16266))**
  - Consolidate scattered arming requirements in arm_disarm(), and, keeps the `vehicle_control_mode` last state in commander
- **Commander: Separate out manual control setpoint processing ([PR#16878](https://github.com/PX4/PX4-Autopilot/pull/16878))**
  - Adds a new class `ManualControl` for handling `manual_control_setpoint` and handles RC loss, RC override, and RC arming/disarming

### 多旋翼

- **More intuitive stick feel in Position mode**

  - Horizontal stick input mapped to acceleration instead of velocity setpoints
  - Removes unexpected tilt changes upon reaching travel speed velocity
  - Intuitive shunting e.g. when landing
  - Opt out possible using [MPC_POS_MODE](../advanced_config/parameter_reference.md#MPC_POS_MODE)
  - Development: [First attempt](https://github.com/PX4/PX4-Autopilot/pull/12072), [Introduction](https://github.com/PX4/PX4-Autopilot/pull/16052), [Improvements](https://github.com/PX4/PX4-Autopilot/pull/16320), [Bugfix zero oscillation](https://github.com/PX4/PX4-Autopilot/pull/16786), [Bugfix position unlock](https://github.com/PX4/PX4-Autopilot/pull/16791), [Bugfix invalid setpoint](https://github.com/PX4/PX4-Autopilot/pull/17078), [Bugfix high velocity pre takeoff](https://github.com/PX4/PX4-Autopilot/pull/17437)

- **Hover thrust independent velocity control gains**

  - Parameters `MPC_{XY/Z}_VEL_{P/I/D}` were replaced with `MPC_{XY/Z}_VEL_{P/I/D}_ACC`, see:
    [MPC_XY_VEL_P_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_P_ACC), [MPC_XY_VEL_I_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_I_ACC), [MPC_XY_VEL_D_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_D_ACC), [MPC_Z_VEL_P_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_P_ACC), [MPC_Z_VEL_I_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_I_ACC), [MPC_Z_VEL_D_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_D_ACC)

:::warning
The gains have a new meaning

    - Scale from velocity error in $m/s$ to acceleration output in $m/s^2$
    - Existing gains need to roughly be rescaled by a factor of: $gravitational \_ constant / hover\_thrust$
    - Automatic parameter transition assumes 50% hover thrust: `~10m/s^2 / 50% = 20 m/s^2`.
      See [question](https://github.com/PX4/PX4-Autopilot/pull/14823#issuecomment-791357646)

:::

  - Development: [Logic introduction](https://github.com/PX4/PX4-Autopilot/pull/14749), [Parameter replacement](https://github.com/PX4/PX4-Autopilot/pull/14823)

- **Improve Rounded Turns ([PR#16376](https://github.com/PX4/PX4-Autopilot/pull/16376))**
  - Creates a more rounded turn at waypoints in multirotor missions (using L1-style guidance logic in corners)
  - See [Mission Mode > Inter-waypoint Trajectory](../flight_modes_fw/mission.md#rounded-turns-inter-waypoint-trajectory) and [Mission > Setting Acceptance/Turning Radius](../flying/missions.md#setting-acceptance-turning-radius)

- **Removal of Rattitude flight mode ([PR#17019](https://github.com/PX4/PX4-Autopilot/pull/17019))**
  - Let us know if you want it back.

### 垂直起降

- **RTL improvements ([PR#16377](https://github.com/PX4/PX4-Autopilot/pull/16377))**
  - Hardens the RTL safety failsafes taking into consideration the many edge cases when trying to land, depending on the current vehicle mode (Multicopter vs Fixed-wing)
- Fixed-wing / VTOL significant TECS improvements

### Control

- **Dynamic Notch Filter updated with Gyro FFT ([PR#16385](https://github.com/PX4/PX4-Autopilot/pull/16385))**
  - Adds dynamic notch filtering to the gyro control data resulting in much smoother control
- **Multi-EKF enabled by default** on stm32f7 and stm32h7 boards

### GPS

- The GPS protocol now defaults to u-blox for faster startup, and [GPS_x_PROTOCOL](../advanced_config/parameter_reference.md#GPS_1_PROTOCOL) needs to be changed if another GPS is used.

### NuttX

Nuttx was upgraded from [8.2+ to NuttX 10.10.0+](https://github.com/apache/incubator-nuttx/compare/nuttx-8.2..nuttx-10.0.1) (@ [904a602c74dc08a100b5c2bd490807de19e73e10](https://github.com/apache/incubator-nuttx/commit/904a602c74dc08a100b5c2bd490807de19e73e10))

- **SDCARD performance:** Results in better performance on H7 Targets
  - [**BACKPORT**] stm32:SDIO:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] stm32h7:SDMMC:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] stm32f7:SDMMC:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] Fixes race condition in event wait logic of SDMMC driver.
  - [**BACKPORT**] mmcsd:Stuck in 1-bit mode, Removed CONFIG_ARCH_HAVE_SDIO_DELAYED_INVLDT

- **Ethernet stability:**
  - [**BACKPORT**] stm32x7:Ethernet Fixed hardfaults, from too big frames
  - [**BACKPORT**] stm32:Ethernet Fix too big frames

- **Boot up stability** V5-V6X ensuring the LSE (RTC) oscillator is started

  - [**BACKPORT**] stm32h7:lse fix Kconfig help text
  - [**BACKPORT**] stm32f7:lse Use Kconfig values directly
  - [**BACKPORT**] stm32h7:Add DBGMCU
  - [**BACKPORT**] stm32f7:Add option to auto select LSE CAPABILITY
  - [**BACKPORT**] stm32h7:Add option to auto select LSE CAPABILITY

    ::: info
    This Knob will cycle through the correct\*
    values from low to high. To avoid damaging
    the crystal. We want to use the lowest setting
    that gets the OSC running. See app note AN2867
    \*It will take into account the rev of the silicon
    and use the correct code points to achieve the drive
    strength. See Eratta ES0392 Rev 7 2.2.14 LSE oscillator
    driving capability selection bits are swapped.

:::

- **Driver changes**

  - [**BACKPORT**] drivers/serial: fix Rx interrupt enable for cdcacm

  - [**BACKPORT**] binnfmt:Fix return before close ELF fd

  - [**BACKPORT**] stm32f7:Allow for reuse of the OTG_ID GPIO

  - [**BACKPORT**] stm32f7:SDMMC fix reset of do_gpio

  - [**BACKPORT**] stm32h7: serial: use dma tx semaphore as resource holder

  - [**BACKPORT**] stm32h7:SDMMC fix reset of do_gpio

  - [**BACKPORT**] stm32h7:Serial Add RX and TX DMA

  - [**BACKPORT**] stm32h7:Allow for reuse of the OTG_ID GPIO

  - [**BACKPORT**] Kinetis:kinetis:Replace DMA

  - [**BACKPORT**] kinetis:Serial use eDMA

  - [**BACKPORT**] kinetis:SPI use eDMA

  - [**BACKPORT**] Kinetis:Serail No DMA Poll needed

  - [**BACKPORT**] libc/stdio: Preallocate the stdin, stdout and stderr
    For targets without consoles.

- **FlexCan fixes**
  - [**BACKPORT**][flexcan] Correct reset state for CTRL1 register
  - [**BACKPORT**][flexcan] Fix TX drop #2792 and correctly set CAN timings to non-zeroed registers
  - [**BACKPORT**] FlexCAN Fix TX abort process

- **Support for CAN bootloader**
  - [**BACKPORT**] s32k1xx:Support ramfunc

- **STM32F412 cleanup**

- [**BACKPORT**] stm32f412:Corrected Pin count

- [**BACKPORT**] stm32f412:Replaced Kludged pinmap with one for SoC

- [**BACKPORT**] stm32412: Fixes pinmap CAN1

- **Security patches**

- [**BACKPORT**] tcp: Remove incomplete support for TCP reassembly

- [**BACKPORT**] net/tcp/tcp_input.c: Correct bad check of urgent data length

- [**BACKPORT**] libc: Add additional checks to malloc realloc and memalign

- **IMXRT fixes**

- Add Single wire and proper parity settings to IMXRT to support sbus etal.

- [**BACKPORT**] imxrt:serial support single-wire mode

- [**BACKPORT**] imxrt:imxrt_lowputc Fixed parity settings.

- **STM32H7 improvements**

- [**BACKPORT**] stm32h7:SPI Fix 16 bit SPI mode

- [**BACKPORT**] stm32h7:DMA BDMA does not auto disabled on completion

- [**BACKPORT**] Fix HEAP clobbering static data in SRAM4

- [**BACKPORT**] stm32h7:SDMMC fix reset of do_gpio

### UAVCAN

- UAVCANv0: Although the fundamental features like Firmware upgrades and parameter sync of CAN nodes have been implemented for over 5 years, we refreshed support now that finally, devices are on the market. Typical CAN GPS, airspeed and power modules are supported
- UAVCANv0 Node: PX4 supported building nodes for many years - now we support building specific targets like the CUAV GPS units
- UAVCANv1: Initial alpha of a complete end-to-end implementation
