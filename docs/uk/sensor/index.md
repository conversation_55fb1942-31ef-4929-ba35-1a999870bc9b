# Апаратне забезпечення та налаштування датчика

Цей розділ описує обов'язкові та необов'язкові датчики та їх налаштування/конфігурацію.

## Загальний огляд

Системи на основі PX4 використовують датчики для оцінки стану транспортного засобу, що необхідно для стабілізації та увімкнення автономного керування.
Інформація про стан транспортного засобу включає: позицію/висоту, курс, швидкість, швидкість польоту, орієнтацію (відносно чогось), швидкість обертання в різних напрямках, рівень заряду батареї тощо.

PX4 _мінімально потребує_ гіроскоп, акселерометр, магнетометр (компас) та барометр для вимірювання вищевказаних станів.
Дрони літакового типу, а також апарати VTOL _повинні_ також включати датчик швидкості польоту.
Для увімкнення усіх автоматичних режимів, а також деяких ручних/допоміжних режимів, потрібна GPS або інша позиційна система.

[Серія Pixhawk](../flight_controller/pixhawk_series.md) контролерів польоту вже має мінімальний набір сенсорів (часто також це мають і інші платформи контролерів).
До контролера можна підключити додаткові/зовнішні датчики — рекомендовано зовнішній GPS та компас, а також датчик швидкості повітря для ВПП та літаків з фіксованим крилом.

## Теми датчиків

Обов'язково (включено в Pixhawk серії FCs):

- [Акселерометр](../sensor/accelerometer.md) — Вимірює зміну прискорення.
- [Гіроскоп](../sensor/gyroscope.md) — Вимірює орієнтацію.
- [Магнітометр (Компас)](../gps_compass/magnetometer.md) — Вимірює напрямок/напрям.
  Рекомендується зовнішній компас!
- [Барометри](../sensor/barometer.md) — Вимірює висоту (за допомогою атмосферного тиску).

Рекомендовано:

- [Датчики швидкості повітря](../sensor/airspeed.md) — Вимірює швидкість повітря.
  Високо рекомендується для ВТОЛ та фіксованих крил, оскільки це єдиний механізм виявлення строба.
- [GNSS (GPS)](../gps_compass/index.md) — Вимірює глобальне положення.
  Потрібно для місій, а також деяких інших автоматичних та ручних/допоміжних режимів.
- [RTK GNSS (GPS)](../gps_compass/rtk_gps.md) — GNSS з точністю на рівні сантиметрів.
  Деякі налаштування також дозволяють визначати напрямок руху за допомогою GNSS, а не за допомогою магнітометра.

Необов'язково:

- [Датчики відстані (дальніміри)](../sensor/rangefinders.md) — Вимірює відстань до цілі.
  Посадка, уникнення об'єктів і слідування за місцевістю.
- [Оптичний потік](../sensor/optical_flow.md) — Оцінює швидкість за допомогою камери, спрямованої вниз, та датчика відстані, спрямованого вниз.
  Дозволяє отримати більш точне фіксування позиції, ніж просто GPS, і може використовуватися усередині приміщень, коли сигнал GPS недоступний.
- [Тахометри (лічильники обертів)](../sensor/tachometers.md) — Використовується лише для реєстрації.

Інші варіанти включають:

- [Калібрування фабрики IMU/Компасу](../advanced_config/imu_factory_calibration.md) — Збереження налаштувань калібрування в постійну пам'ять.
- [Компенсація Температурних Датчиків](../advanced_config/sensor_thermal_calibration.md) — Компенсувати датчики для змін температури.
