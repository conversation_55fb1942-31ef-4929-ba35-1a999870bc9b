# Режими польоту (Мультикоптер)

Режими польоту забезпечують автопілот підтримку для полегшення ручного польоту транспортного засобу, автоматизацію загальних завдань, таких як зльот та посадка, виконання автономних місій або делегування керування польотом зовнішній системі.

Ця тема надає загальний огляд доступних режимів польоту для мультикоптерів та вертольотів.

## Загальний огляд

Режими польоту можуть бути або _ручними_, або _автономними_.
Ручні режими забезпечують різні рівні підтримки автопілота при ручному керуванні (використовуючи керувальні палиці дистанційного керування або джойстик), тоді як _автономні_ режими можуть бути повністю контрольованими автопілотом.

Manual-Easy:

- [Режим позиції](../flight_modes_mc/position.md) — Найпростіший і найбезпечніший ручний режим для транспортних засобів, які мають фіксацію позиції/GPS.
  Палиці крена та тангажу керують _прискоренням_ по землі у напрямках вперед-назад та ліво-право у транспортному засобі (аналогічно до педалі газу в автомобілі), палиця розвороту керує горизонтальним обертанням, а ручка газу керує швидкістю підйому-спуску.
  Відпускання палиць вирівнює транспортний засіб, активно гальмує його до зупинки і фіксує його на поточній тривимірній позиції (навіть проти вітру та інших сил).
- [Повільний режим позиції](../flight_modes_mc/position_slow.md) — Версія режиму _Позиція_ з обмеженою швидкістю руху і обертовим кутом розвороту.
  Це використовується переважно для тимчасового обмеження швидкості під час польоту навколо перешкод або за вимогами регулятивних нормативних актів.
- [Режим висоти](../flight_modes_mc/altitude.md) — Найпростіший і найбезпечніший _непідтримуваний GPS_ ручний режим.
  Основна відмінність порівняно з режимом _Позиція_ полягає в тому, що під час відпускання палиць транспортний засіб вирівнюється і підтримує висоту, але відсутній активний гальмувальний або утримуючий горизонтальну позицію ефект (транспортний засіб рухається з поточним рухом і дрейфує з вітром).
- [Stabilized mode](../flight_modes_mc/manual_stabilized.md) — Releasing the sticks levels and maintains the vehicle horizontal posture (but not altitude or position).
  Транспортний засіб продовжить рухатися з імпульсом, і як висота, так і горизонтальна позиція можуть бути піддані впливу вітру.
  This mode is also used if "Manual mode" is selected in a ground station.

Manual-Acrobatic

- [Acro](../flight_modes_mc/acro.md) — Ручний режим для виконання акробатичних маневрів, таких як креніння та петлі.
  Відпускання палиць призупиняє обертання транспортного засобу в площині крена, тангажу та розвороту, але іншим чином не стабілізує транспортний засіб.

Автономний:

- [Утримання](../flight_modes_mc/hold.md) — Транспортний засіб зупиняється і утримується на своїй поточній позиції та висоті, утримуючи свою позицію проти вітру та інших сил.
- [Повернення](../flight_modes_mc/return.md) — Транспортний засіб піднімається на безпечну висоту, летить чітким маршрутом до безпечного місця (додому або резервної точки) і потім сідає.
  Для цього потрібна глобальна оцінка позиції (GPS).
- [Місія](../flight_modes_mc/mission.md) — Транспортний засіб виконує [попередньо визначену місію/план польоту](../flying/missions.md), яка була завантажена до керувальної системи польоту.
  Для цього потрібна глобальна оцінка позиції (GPS).
- [Зльот](../flight_modes_mc/takeoff.md) — Транспортний засіб злітає вертикально, а потім переходить у режим _Утримання_.
- [Посадка](../flight_modes_mc/land.md) — Транспортний засіб приземлюється негайно.
- [Обертання](../flight_modes_mc/orbit.md) - Транспортний засіб летить по колу, повертаючи рульові пристрої так, щоб завжди бути оберненим в бік центру.
  RC керування може опціонально використовуватися для зміни радіуса орбіти, напрямку, швидкості та іншого.
- [Follow Me/Дотримуйся Мене](../flight_modes_mc/follow_me.md) — Транспортний засіб слідує за маяком, який надає точки встановлення позиції.
  RC control може використовуватися для встановлення послідовної позиції.
- [Offboard/Офборд](../flight_modes_mc/offboard.md) — Транспортний засіб слідкує за встановленими точками позиції, швидкості або орієнтації, що надаються через MAVLink або ROS 2.

Пілоти переходять між режимами польоту за допомогою перемикачів на дистанційному керуванні або зі станції земного керування (див. [Конфігурацію режиму польоту](../config/flight_mode.md)).
Деякі режими польоту мають сенс тільки при певних передпольотних та польотних умовах (наприклад, блокування GPS, датчик швидкості повітря, виявлення орієнтації транспортного засобу по вісі).
PX4 не дозволить переходити до цих режимів, доки не будуть виконані відповідні умови.

Щоб отримати детальнішу технічну інформацію, виберіть розділи бічної панелі, що стосуються режиму.

## Подальша інформація

- [Базова конфігурація > Режими польоту](../config/flight_mode.md) - Як відповідати перемикачі керування RC конкретним режимам польоту
- [Flight Modes (Fixed-Wing)](../flight_modes_fw/index.md)
- [Flight Modes (VTOL)](../flight_modes_vtol/index.md)
- [Drive Modes (Differential Rover)](../flight_modes_rover/differential.md)
- [Drive Modes (Ackermann Rover)](../flight_modes_rover/ackermann.md)
