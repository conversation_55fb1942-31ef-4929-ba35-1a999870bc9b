module_name: Internal Combustion Engine Control

parameters:
  - group: ICE
    definitions:

      ICE_EN:
        description:
          short: Enable internal combustion engine
        type: boolean
        default: true

      ICE_ON_SOURCE:
        description:
          short: Engine start/stop input source
        type: enum
        default: 0
        values:
          0: On arming - disarming
          1: Aux1
          2: Aux2

      ICE_CHOKE_ST_DUR:
        description:
          short: Duration of choking during startup
        type: float
        unit: s
        min: 0
        max: 10
        decimal: 1
        increment: 0.1
        default: 5

      ICE_STRT_DUR:
        description:
          short: Duration of single starting attempt (excl. choking)
          long: |
              Maximum expected time for startup before declaring timeout.
        type: float
        unit: s
        min: 0
        max: 10
        decimal: 1
        increment: 0.1
        default: 5

      ICE_MIN_RUN_RPM:
        description:
          short: Minimum RPM for engine to be declared running
        type: float
        unit: rpm
        min: 0
        max: 10000
        decimal: 0
        increment: 1
        default: 2000

      ICE_STRT_ATTEMPT:
        description:
          short: Number attempts for starting the engine
          long: |
              Number of accepted attempts for starting the engine before declaring a fault.
        type: int32
        min: 0
        max: 10
        default: 3

      ICE_RUN_FAULT_D:
        description:
          short: Fault detection if it stops in running state
          long: |
              Enables restart if a fault is detected during the running state. Otherwise
              commands continues in running state until given an user request off.
        type: boolean
        default: true

      ICE_STRT_THR:
        description:
          short: Throttle value for starting engine
          long: |
              During the choking and the starting phase, the throttle value is set to this value.
        type: float
        unit: norm
        min: 0
        max: 1
        decimal: 0
        increment: 0.01
        default: 0.1

      ICE_STOP_CHOKE:
        description:
          short: Apply choke when stopping engine
        type: boolean
        default: true

      ICE_THR_SLEW:
        description:
          short: Throttle slew rate
          long: |
              Maximum rate of change of throttle value per second.
        type: float
        unit: 1/s
        min: 0
        max: 1
        decimal: 2
        increment: 0.01
        default: 0.5

      ICE_IGN_DELAY:
        description:
          short: Cold-start delay after ignition before engaging starter
          long: |
              In case that the ignition takes a moment to be up and running, this parameter can be set to account for that.
        type: float
        unit: s
        min: 0
        max: 10
        decimal: 1
        increment: 0.1
        default: 0
