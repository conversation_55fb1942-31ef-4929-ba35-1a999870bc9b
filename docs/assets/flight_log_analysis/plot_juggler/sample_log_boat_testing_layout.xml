<?xml version='1.0' encoding='UTF-8'?>
<root>
 <tabbed_widget parent="main_window" name="Main Window">
  <Tab containers="1" tab_name="tab17">
   <Container>
    <DockSplitter sizes="1" count="1" orientation="-">
     <DockSplitter sizes="0.5;0.5" count="2" orientation="|">
      <DockArea name="...">
       <plot style="Lines" flip_x="false" mode="XYPlot" flip_y="false">
        <range right="33.005088" bottom="-7.444404" left="-16.563412" top="45.853549"/>
        <limitY/>
        <curve color="#1ac938" curve_y="vehicle_local_position/y" name="vehicle_local_position/[x;y]" curve_x="vehicle_local_position/x"/>
       </plot>
      </DockArea>
      <DockSplitter sizes="0.500597;0.499403" count="2" orientation="-">
       <DockArea name="...">
        <plot style="Lines" flip_x="false" mode="TimeSeries" flip_y="false">
         <range right="495.096543" bottom="-3.086561" left="0.000000" top="5.092128"/>
         <limitY/>
         <curve color="#ff7f0e" name="vehicle_local_position/vx"/>
         <curve color="#f14cc1" name="vehicle_local_position/vy"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot style="Lines" flip_x="false" mode="TimeSeries" flip_y="false">
         <range right="495.096543" bottom="-1.145333" left="0.000000" top="1.339007"/>
         <limitY/>
         <curve color="#9467bd" name="vehicle_local_position/vz"/>
        </plot>
       </DockArea>
      </DockSplitter>
     </DockSplitter>
    </DockSplitter>
   </Container>
  </Tab>
  <currentTabIndex index="0"/>
 </tabbed_widget>
 <use_relative_time_offset enabled="1"/>
 <!-- - - - - - - - - - - - - - - -->
 <!-- - - - - - - - - - - - - - - -->
 <Plugins>
  <plugin ID="DataLoad CSV">
   <default time_axis="" delimiter="0"/>
  </plugin>
  <plugin ID="DataLoad Parquet">
   <default dateFromat="false" radioIndexChecked="0" prevTimestamp="" parseDateTime="0"/>
  </plugin>
  <plugin ID="DataLoad ULog"/>
  <plugin ID="MQTT Subscriber (Mosquitto)"/>
  <plugin ID="UDP Server"/>
  <plugin ID="WebSocket Server"/>
  <plugin ID="Fast Fourier Transform"/>
  <plugin ID="Quaternion to RPY"/>
  <plugin ID="Reactive Script Editor">
   <library code="--[[ Helper function to create a ScatterXY series from arrays.&#xa;&#xa; series_name: name of the created ScatterXY series&#xa; prefix:      prefix of the timeseries, before the index of the array&#xa; suffix_X:   suffix to complete the name of the series containing the X value.&#xa;             if [nil], the index of the array will be used.&#xa; suffix_Y:   suffix to complete the name of the series containing the Y value&#xa; timestamp:   usually the tracker_time variable&#xa;              &#xa; Example:&#xa; &#xa; Assuming we have multiple series in the form:&#xa; &#xa;   /trajectory/node.{N}/position/x&#xa;   /trajectory/node.{N}/position/y&#xa;   &#xa; where {N} is the index of the array (integer). We can create a reactive series from the array with:&#xa; &#xa;   CreateSeriesFromArray( &quot;my_trajectory&quot;, &quot;/trajectory/node&quot;,  &quot;position/x&quot;, &quot;position/y&quot;, tracker_time );&#xa;]]--&#xa;&#xa;function CreateSeriesFromArray( series_name, prefix, suffix_X, suffix_Y, timestamp )&#xa;  --- create a new series or overwite the previous one&#xa;  new_series = MutableScatterXY.new(series_name)&#xa;  &#xa;  --- Append points to new_series&#xa;  index = 0&#xa;  while(true) do&#xa;&#xa;    x = index;&#xa;    -- if not nil, get the X coordinate from a series&#xa;    if suffix_X ~= nil then &#xa;      series_x = TimeseriesView.find( string.format( &quot;%s.%d/%s&quot;, prefix, index, suffix_X) )&#xa;      if series_x == nil then break end&#xa;      x = series_x:atTime(timestamp)&#x9; &#xa;    end&#xa;    &#xa;    series_y = TimeseriesView.find( string.format( &quot;%s.%d/%s&quot;, prefix, index, suffix_Y) )&#xa;    if series_x == nil then break end &#xa;    y = series_y:atTime(timestamp)&#xa;    &#xa;    new_series:push_back(x,y)&#xa;    index = index+1&#xa;  end&#xa;end&#xa;"/>
   <scripts/>
  </plugin>
  <plugin ID="CSV Exporter"/>
  <plugin ID="Video Viewer">
   <config curve_name="" use_frame="true" video_file=""/>
  </plugin>
 </Plugins>
 <!-- - - - - - - - - - - - - - - -->
 <!-- - - - - - - - - - - - - - - -->
 <customMathEquations/>
 <snippets>
  <snippet name="1st_derivative">
   <global>var prevX = 0
var prevY = 0</global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="1st_order_lowpass">
   <global>var prevY = 0
var alpha = 0.1</global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="2D_velocity">
   <global>var prev_x = 0
var prev_y = 0
var prev_t = 0</global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="GPS lat">
   <global></global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="GPS lon">
   <global></global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="average_two_curves">
   <global></global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="integral">
   <global>var integral = 0</global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="rad_to_deg">
   <global></global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="remove_offset">
   <global>var is_first = true
var first_value = 0</global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
  <snippet name="yaw_from_quaternion">
   <global>// source: https://en.wikipedia.org/wiki/Conversion_between_quaternions_and_Euler_angles

function quaternionToYaw(x, y, z, w)
{
  // yaw (z-axis rotation)
  t1 = 2.0 * (w * z + x * y);
  t2 = 1.0 - 2.0 * (y * y + z * z);
  yaw = Math.atan2(t1, t2);

  return yaw
}</global>
   <function></function>
   <linked_source></linked_source>
  </snippet>
 </snippets>
 <!-- - - - - - - - - - - - - - - -->
</root>

