<?xml version="1.0" ?>
<sdf version='1.5'>
  <model name='iris_foggy_lidar'>

    <include>
      <uri>model://iris</uri>
    </include> 

    <include>
      <uri>model://foggy_lidar</uri>
      <pose>0 0 0.1 0 1.571 0</pose>
    </include>
    <joint name="foggy_lidar_joint" type="revolute">
      <parent>iris::base_link</parent>
      <child>foggy_lidar::link</child>
      <pose>0 0 0.1 0 1.571 0</pose>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

  </model>
</sdf>
<!-- vim: set noet fenc=utf-8 ff=unix sts=0 sw=4 ts=4 : -->

