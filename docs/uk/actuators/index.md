# Приводи

У цьому розділі містяться теми про основні приводи, використовувані для керування польотом (ЕСП/двигуни та сервоприводи), а також про те, як вони призначаються виводам керуючого контролера, налаштовуються та калібруються.

- [Розподіл приводів](../config/actuators.md) — Налаштування виводів керуючого контролера для конкретних функцій та типів ЕСП/сервоприводів.

- [ESCs & двигуни](../peripherals/esc_motors.md) — ESCs такі як[DShot](../peripherals/dshot.md) (рекомендовано) та DroneCAN.

- [ESC Калібрування](../advanced_config/esc_calibration.md) — Калібрування для ЕСП зі звичайним PWM (не потрібно для ESC/сервоприводів з DShot/CAN).

## Дивіться також

- [Периферійні пристрої](../peripherals/README.md) - включає неосновні приводи, такі як захвати, парашути, тощо.