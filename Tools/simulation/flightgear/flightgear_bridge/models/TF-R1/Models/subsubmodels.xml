<?xml version="1.0"?>
<!--  Submodels are objects which can be dropped or launched from the user aircraft.
      The trigger is a boolean property, which you define, which when "true" causes
      the submodel to be released/launched.
      A submodel will create an AIBallistic object which will follow a ballistic path.
      By default one submodel will be released when the corresponding trigger is "true".
      The initial conditions (IC) define the object's starting point (relative to the
      user aircraft's "reported position"), and its initial speed and direction
      (relative to the user aircraft).  If you want to release many similar objects
      with similar IC, then you may use the <repeat>, <delay> and <count> properties
      to define this.  The allowed properties are:

      <name>         The name of the submodel.
      <model>        The path to the visual model.
      <trigger>      The property which will act as the trigger.
      <speed>        Initial speed, in feet/sec, relative to user aircraft.
      <repeat>       Set "true" if you want multiple releases of this submodel. Defaults
                     to false
      <delay>        Time, in seconds, between repeated releases.
      <count>        Number of submodels available for multiple release.
      <x-offset>     Submodel's initial fore/aft position relative to user aircraft.
                     Fore is positive. (ft)
      <y-offset>     Submodel's initial left/right position relative to user aircraft.
                     Right is positive. (ft)
      <z-offset>     Submodel's initial up/down position relative to user aircraft.
                     Up is positive. (ft)
      <yaw-offset>   Submodel's initial azimuth, in degrees, relative to user aircraft's
                     nose.  Right is positive.
      <pitch-offset> Submodel's initial elevation, in degrees, relative to user aircraft's
                     pitch.  Up is positive.
      <buoyancy>     Submodel's upward acceleration (fps) due to buoyancy in air. 32 is
                     neutral (contrails),  > 32 exhaust gas, smoke.
      <wind>	   		 If true, Submodel is affected by local wind. Defaults to false.
      <cd>           The Coeffient of Drag. Varies with submodel shape - 0.295 for a bullet,
                 0.045 for an airfoil. Enter an appropriate value. Defaults to 0.295.
      <eda>          Effective drag area (sq ft). Usually the cross-sectional area of the
                     submodel normal to the airflow.
      <weight>       The weight of the submodel (lbs). Defaults to 0.25
      <aero-stabilised> If true, the submodel aligns with the local airflow. Defaults to true

-->
<PropertyList>

  <submodel>
    <name>impact smoke</name>
    <model>Aircraft/TF-R1/Models/Effects/impact.xml</model>
    <speed>0</speed>
    <repeat>false</repeat>
		<delay>0</delay>
    <count>-1</count>
    <x-offset>0</x-offset>
    <y-offset>0</y-offset>
    <z-offset>0</z-offset>
    <yaw-offset>0</yaw-offset>
    <pitch-offset>0</pitch-offset>
    <eda>10</eda>
    <wind>true</wind>
    <buoyancy>33</buoyancy>
    <life>0.7</life>
  </submodel>

</PropertyList>
