<?xml version="1.0"?>
<sdf version='1.5'>
  <model name='advanced_plane'>
    <pose>0 0 0.246 0 0 0</pose>
    <link name='base_link'>
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>1</mass>
        <inertia>
          <ixx>0.197563</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.1458929</iyy>
          <iyz>0</iyz>
          <izz>0.1477</izz>
        </inertia>
      </inertial>
      <collision name='base_link_collision'>
        <pose>0 0 -0.07 0 0 0</pose>
        <geometry>
          <box>
            <size>0.47 0.47 0.11</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <max_vel>10</max_vel>
              <min_depth>0.01</min_depth>
            </ode>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <visual name='base_link_visual'>
        <pose>0.07 0 -0.08 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://rc_cessna/meshes/body.dae</uri>
          </mesh>
        </geometry>
        <material>
	  <ambient>.175 .175 .175 1.0</ambient>
          <diffuse>.175 .175 .175 1.0</diffuse>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
      <self_collide>0</self_collide>
      <sensor name="imu_sensor" type="imu">
        <always_on>1</always_on>
        <update_rate>250</update_rate>
        <imu>
          <angular_velocity>
            <x>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.0003394</stddev>
                <dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
              </noise>
            </x>
            <y>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.0003394</stddev>
                <dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
              </noise>
            </y>
            <z>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.0003394</stddev>
                <dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
              </noise>
            </z>
          </angular_velocity>
          <linear_acceleration>
            <x>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.004</stddev>
                <dynamic_bias_stddev>0.006</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
              </noise>
            </x>
            <y>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.004</stddev>
                <dynamic_bias_stddev>0.006</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
              </noise>
            </y>
            <z>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.004</stddev>
                <dynamic_bias_stddev>0.006</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
              </noise>
            </z>
          </linear_acceleration>
        </imu>
      </sensor>
      <sensor name="air_pressure_sensor" type="air_pressure">
        <always_on>1</always_on>
        <update_rate>50</update_rate>
        <air_pressure>
          <pressure>
            <noise type="gaussian">
              <mean>0</mean>
              <stddev>0.01</stddev>
            </noise>
          </pressure>
        </air_pressure>
      </sensor>
      <sensor name="magnetometer_sensor" type="magnetometer">
        <always_on>1</always_on>
        <update_rate>100</update_rate>
        <magnetometer>
          <!-- TODO: update to fix units and coordinate system when we move past Harmonic -->
          <!-- See https://github.com/gazebosim/gz-sim/pull/2460 -->
          <!-- 3mgauss RMS: NOTE: noise is in tesla but sensor reports data in gauss -->
          <!-- Noise modeled after IIS2MDC -->
          <x>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </z>
        </magnetometer>
      </sensor>
      <sensor name="navsat_sensor" type="navsat">
        <always_on>1</always_on>
        <update_rate>30</update_rate>
      </sensor>
    </link>
    <link name='rotor_puller'>
      <pose>0.3 0 0.0 0 1.57 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.005</mass>
        <inertia>
          <ixx>9.75e-07</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.000166704</iyy>
          <iyz>0</iyz>
          <izz>0.000167604</izz>
        </inertia>
      </inertial>
      <collision name='rotor_puller_collision'>
        <pose>0.0 0 0.0 0 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.005</length>
            <radius>0.1</radius>
          </cylinder>
        </geometry>
        <surface>
          <contact>
            <ode/>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <visual name='rotor_puller_visual'>
        <pose>0 0 -0.09 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://rc_cessna/meshes/iris_prop_ccw.dae</uri>
          </mesh>
        </geometry>
        <material>
	  <ambient>.175 .175 .175 1.0</ambient>
          <diffuse>.175 .175 .175 1.0</diffuse>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
      <self_collide>0</self_collide>
    </link>
    <joint name='rotor_puller_joint' type='revolute'>
      <child>rotor_puller</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>

    <link name="left_elevon">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 0.3 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='left_elevon_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://rc_cessna/meshes/left_aileron.dae</uri>
          </mesh>
        </geometry>
        <material>
          <ambient>1 0 0 1.0</ambient>
          <diffuse>1 0 0 1.0</diffuse>
        </material>
      </visual>
    </link>
    <link name="right_elevon">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 -0.3 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='right_elevon_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://rc_cessna/meshes/right_aileron.dae</uri>
          </mesh>
        </geometry>
        <material>
          <ambient>1 0 0 1.0</ambient>
          <diffuse>1 0 0 1.0</diffuse>
        </material>
      </visual>
    </link>
    <link name="left_flap">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 0.15 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='left_flap_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://rc_cessna/meshes/left_flap.dae</uri>
          </mesh>
        </geometry>
        <material>
          <ambient>1 0 0 1.0</ambient>
          <diffuse>1 0 0 1.0</diffuse>
        </material>
      </visual>
    </link>
    <link name="right_flap">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 -0.15 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='right_flap_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://rc_cessna/meshes/right_flap.dae</uri>
          </mesh>
        </geometry>
        <material>
          <ambient>1 0 0 1.0</ambient>
          <diffuse>1 0 0 1.0</diffuse>
        </material>
      </visual>
    </link>
    <link name="elevator">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose> -0.5 0 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='elevator_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://rc_cessna/meshes/elevators.dae</uri>
          </mesh>
        </geometry>
        <material>
          <ambient>1 0 0 1.0</ambient>
          <diffuse>1 0 0 1.0</diffuse>
        </material>
      </visual>
    </link>
    <link name="rudder">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>-0.5 0 0.05 0 0 0 </pose>
      </inertial>
      <visual name='rudder_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://rc_cessna/meshes/rudder.dae</uri>
          </mesh>
        </geometry>
        <material>
          <ambient>1 0 0 1.0</ambient>
          <diffuse>1 0 0 1.0</diffuse>
        </material>
      </visual>
    </link>
    <joint name='servo_0' type='revolute'>
      <parent>base_link</parent>
      <child>left_elevon</child>
      <pose>-0.07 0.4 0.08 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-0.78</lower>
          <upper>0.78</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <include merge='true'>
      <uri>model://LW20</uri>
      <pose relative_to="base_link">0.05 0 -0.1 0 1.57 0</pose>
    </include>

    <joint name="lidar_model_joint" type="fixed">
      <parent>base_link</parent>
      <child>lw20_link</child>
      <pose relative_to="base_link">0 0 0 0 0 0</pose>
    </joint>

    <joint name="lidar_sensor_joint" type="fixed">
      <parent>base_link</parent>
      <child>lidar_sensor_link</child>
    </joint>

    <link name="lidar_sensor_link">
      <pose relative_to="base_link">0.05 0 0 0 1.57 0</pose>
      <inertial>
        <mass>0.001</mass>
        <inertia>
          <ixx>0.00001</ixx>
          <iyy>0.00001</iyy>
          <izz>0.00001</izz>
          <ixy>0.0</ixy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
        </inertia>
      </inertial>
      <sensor name='lidar' type='gpu_lidar'>
        <pose>0 0 0 3.14 0 0</pose>
        <update_rate>50</update_rate>
        <ray>
          <scan>
            <horizontal>
              <samples>1</samples>
              <resolution>1</resolution>
              <min_angle>0</min_angle>
              <max_angle>0</max_angle>
            </horizontal>
            <vertical>
              <samples>1</samples>
              <resolution>1</resolution>
              <min_angle>0</min_angle>
              <max_angle>0</max_angle>
            </vertical>
          </scan>
          <range>
            <min>0.1</min>
            <max>100.0</max>
            <resolution>0.01</resolution>
          </range>
        </ray>
        <always_on>1</always_on>
        <visualize>true</visualize>
      </sensor>
    </link>
    <plugin
      filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
      <joint_name>servo_0</joint_name>
      <sub_topic>servo_0</sub_topic>
      <p_gain>10</p_gain>
      <i_gain>0</i_gain>
      <d_gain>0</d_gain>
    </plugin>
    <joint name='servo_1' type='revolute'>
      <parent>base_link</parent>
      <child>right_elevon</child>
      <pose>-0.07 -0.4 0.08 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-0.78</lower>
          <upper>0.78</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <plugin
      filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
      <joint_name>servo_1</joint_name>
      <sub_topic>servo_1</sub_topic>
      <p_gain>10</p_gain>
      <i_gain>0</i_gain>
      <d_gain>0</d_gain>
    </plugin>
    <joint name='servo_4' type='revolute'>
      <parent>base_link</parent>
      <child>left_flap</child>
      <pose>-0.07 0.2 0.08 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-0.78</lower>
          <upper>0.78</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <plugin
      filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
      <joint_name>servo_4</joint_name>
      <sub_topic>servo_4</sub_topic>
      <p_gain>10</p_gain>
      <i_gain>0</i_gain>
      <d_gain>0</d_gain>
    </plugin>
    <joint name='servo_5' type='revolute'>
      <parent>base_link</parent>
      <child>right_flap</child>
      <pose>-0.07 -0.2 0.08 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-0.78</lower>
          <upper>0.78</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <plugin
      filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
      <joint_name>servo_5</joint_name>
      <sub_topic>servo_5</sub_topic>
      <p_gain>10</p_gain>
      <i_gain>0</i_gain>
      <d_gain>0</d_gain>
    </plugin>
    <joint name='servo_2' type='revolute'>
      <parent>base_link</parent>
      <child>elevator</child>
      <pose> -0.5 0 0 0 0 0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-0.78</lower>
          <upper>0.78</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <plugin
      filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
      <joint_name>servo_2</joint_name>
      <sub_topic>servo_2</sub_topic>
      <p_gain>10</p_gain>
      <i_gain>0</i_gain>
      <d_gain>0</d_gain>
    </plugin>
    <joint name='servo_3' type='revolute'>
      <parent>base_link</parent>
      <child>rudder</child>
      <pose>-0.5 0 0.05 0.00 0 0.0</pose>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-0.78</lower>
          <upper>0.78</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <plugin
      filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
      <joint_name>servo_3</joint_name>
      <sub_topic>servo_3</sub_topic>
      <p_gain>10</p_gain>
      <i_gain>0</i_gain>
      <d_gain>0</d_gain>
    </plugin>

    <plugin filename="gz-sim-advanced-lift-drag-system" name="gz::sim::systems::AdvancedLiftDrag">
      <a0>0.0</a0>
      <CL0>0.15188</CL0>
      <AR>6.5</AR>
      <eff>0.97</eff>
      <CLa>5.015</CLa>
      <CD0>0.029</CD0>
      <Cem0>0.075</Cem0>
      <Cema>-0.463966</Cema>
      <CYb>-0.258244</CYb>
      <Cellb>-0.039250</Cellb>
      <Cenb>0.100826</Cenb>
      <CDp>0.0</CDp>
      <CYp>0.065861</CYp>
      <CLp>0.0</CLp>
      <Cellp>-0.487407</Cellp>
      <Cemp>0.0</Cemp>
      <Cenp>-0.040416</Cenp>
      <CDq>0.055166</CDq>
      <CYq>0.0</CYq>
      <CLq>7.971792</CLq>
      <Cellq>0.0</Cellq>
      <Cemq>-12.140140</Cemq>
      <Cenq>0.0</Cenq>
      <CDr>0.0</CDr>
      <CYr>0.230299</CYr>
      <CLr>0.0</CLr>
      <Cellr>0.078165</Cellr>
      <Cemr>0.0</Cemr>
      <Cenr>-0.089947</Cenr>
      <alpha_stall>0.3391428111</alpha_stall>
      <CLa_stall>-3.85</CLa_stall>
      <CDa_stall>-0.9233984055</CDa_stall>
      <Cema_stall>0</Cema_stall>
      <cp>-0.12 0.0 0.0</cp>
      <area>0.34</area>
      <mac>0.22</mac>
      <air_density>1.2041</air_density>
      <forward>1 0 0</forward>
      <upward>0 0 1</upward>
      <link_name>base_link</link_name>
      <num_ctrl_surfaces>4</num_ctrl_surfaces>
      <control_surface>
        <name>servo_0</name>
        <index>0</index>
        <direction>1</direction>
        <CD_ctrl>-0.000059</CD_ctrl>
        <CY_ctrl>0.000171</CY_ctrl>
        <CL_ctrl>-0.011940</CL_ctrl>
        <Cell_ctrl>-0.003331</Cell_ctrl>
        <Cem_ctrl>0.001498</Cem_ctrl>
        <Cen_ctrl>-0.000057</Cen_ctrl>
      </control_surface>
      <control_surface>
        <name>servo_1</name>
        <direction>1</direction>
        <index>1</index>
        <CD_ctrl>-0.000059</CD_ctrl>
        <CY_ctrl>-0.000171</CY_ctrl>
        <CL_ctrl>-0.011940</CL_ctrl>
        <Cell_ctrl>0.003331</Cell_ctrl>
        <Cem_ctrl>0.001498</Cem_ctrl>
        <Cen_ctrl>0.000057</Cen_ctrl>
      </control_surface>
      <control_surface>
        <name>servo_2</name>
        <direction>-1</direction>
        <index>2</index>
        <CD_ctrl>0.000274</CD_ctrl>
        <CY_ctrl>0</CY_ctrl>
        <CL_ctrl>0.010696</CL_ctrl>
        <Cell_ctrl>0.0</Cell_ctrl>
        <Cem_ctrl>-0.025798</Cem_ctrl>
        <Cen_ctrl>0.0</Cen_ctrl>
      </control_surface>
      <control_surface>
        <name>servo_3</name>
        <direction>1</direction>
        <index>3</index>
        <CD_ctrl>0.0</CD_ctrl>
        <CY_ctrl>-0.003913</CY_ctrl>
        <CL_ctrl>0.0</CL_ctrl>
        <Cell_ctrl>-0.000257</Cell_ctrl>
        <Cem_ctrl>0.0</Cem_ctrl>
        <Cen_ctrl>0.001613</Cen_ctrl>
      </control_surface>
    </plugin>
    <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
      <jointName>rotor_puller_joint</jointName>
      <linkName>rotor_puller</linkName>
      <turningDirection>cw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>3500</maxRotVelocity>
      <motorConstant>8.54858e-06</motorConstant>
      <momentConstant>0.01</momentConstant>
      <commandSubTopic>command/motor_speed</commandSubTopic>
      <motorNumber>0</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <motorType>velocity</motorType>

    </plugin>
  </model>
</sdf>
