<?xml version="1.0"?>
 
<!-- Generic Autopilot Configuration -->

<!-- Each component is evaluated in the order specified.  You can make up -->
<!-- property names to pass the result of one component on to a subsequent -->
<!-- component. -->


<PropertyList>

  <!-- =============================================================== -->
  <!-- Roll Axis Modes                                                 -->
  <!-- =============================================================== -->

  <!-- Wing leveler --> 
  <pid-controller>
    <name>Wing Leveler (Turn Coordinator based)</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/heading</prop>
      <value>wing-leveler</value>
    </enable>
    <input>
      <prop>/orientation/roll-deg</prop>
    </input>
    <reference>
      <value>0.0</value>
    </reference>
    <output>
      <prop>/controls/flight/aileron</prop>
    </output>
    <config>
      <Kp>0.05</Kp>       <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.01</Td>       <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Bank hold --> 
  <pid-controller>
    <name>Bank Hold</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/heading</prop>
      <value>bank-hold</value>
    </enable>
    <input>
      <prop>/orientation/roll-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-bank-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/aileron</prop>
    </output>
    <config>
      <Kp>0.05</Kp>       <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.01</Td>       <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Heading Bug Hold.  2 stage cascade controller. -->

  <!-- Stage #1 sets target roll based on diff between current heading -->
  <!-- and heading bug. -->
  <pid-controller>
    <name>Heading Bug Hold (DG based) Stage 1</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/heading</prop>
      <value>dg-heading-hold</value>
    </enable>
    <input>
      <prop>/autopilot/internal/fdm-heading-bug-error-deg</prop>
    </input>
    <reference>
      <value>0.0</value>
    </reference>
    <output>
      <prop>/autopilot/internal/target-roll-deg</prop>
    </output>
    <config>
      <Kp>-0.1</Kp>        <!-- proportional gain -->
      <beta>1.0</beta>     <!-- input value weighing factor -->
      <alpha>0.1</alpha>   <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>   <!-- input value weighing factor for -->
                           <!-- unfiltered derivative error -->
      <Ti>10.0</Ti>        <!-- integrator time -->
      <Td>0.00001</Td>     <!-- derivator time -->
      <u_min>-20.0</u_min> <!-- minimum output clamp -->
      <u_max>20.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Stage #2 drives the ailerons to achieve the desired roll deg. -->
  <pid-controller>
    <name>Heading Bug Hold (DG based) Stage 2</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/heading</prop>
      <value>dg-heading-hold</value>
    </enable>
    <input>
      <prop>/orientation/roll-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/internal/target-roll-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/aileron</prop>
    </output>
    <config>
      <Kp>0.05</Kp>       <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.01</Td>       <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- True Heading hold.  2 stage cascade controller. -->

  <!-- Stage #1 sets target roll based on diff between current heading -->
  <!-- and target heading. -->
  <pid-controller>
    <name>True Heading Hold (DG based) Stage 1</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/heading</prop>
      <value>true-heading-hold</value>
    </enable>
    <input>
      <prop>/autopilot/internal/true-heading-error-deg</prop>
    </input>
    <reference>
      <value>0.0</value>
    </reference>
    <output>
      <prop>/autopilot/internal/target-roll-deg</prop>
    </output>
    <config>
      <Kp>-1.0</Kp>        <!-- proportional gain -->
      <beta>1.0</beta>     <!-- input value weighing factor -->
      <alpha>0.1</alpha>   <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>   <!-- input value weighing factor for -->
                           <!-- unfiltered derivative error -->
      <Ti>10.0</Ti>        <!-- integrator time -->
      <Td>0.00001</Td>     <!-- derivator time -->
      <u_min>-20.0</u_min> <!-- minimum output clamp -->
      <u_max>20.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Stage #2 drives the ailerons to achieve the desired roll deg. -->
  <pid-controller>
    <name>True Heading Hold (DG based) Stage 2</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/heading</prop>
      <value>true-heading-hold</value>
    </enable>
    <input>
      <prop>/orientation/roll-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/internal/target-roll-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/aileron</prop>
    </output>
    <config>
      <Kp>0.05</Kp>       <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.01</Td>       <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Nav1 hold.  2 stage cascade controller. -->

  <!-- Stage #1 sets target roll based on diff between current heading -->
  <!-- and target heading. -->
  <pid-controller>
    <name>Nav1 Hold Stage 1</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/heading</prop>
      <value>nav1-hold</value>
    </enable>
    <input>
      <prop>/autopilot/internal/nav1-heading-error-deg</prop>
    </input>
    <reference>
      <value>0.0</value>
    </reference>
    <output>
      <prop>/autopilot/internal/target-roll-deg</prop>
    </output>
    <config>
      <Kp>-1.0</Kp>        <!-- proportional gain -->
      <beta>1.0</beta>     <!-- input value weighing factor -->
      <alpha>0.1</alpha>   <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>   <!-- input value weighing factor for -->
                           <!-- unfiltered derivative error -->
      <Ti>10.0</Ti>        <!-- integrator time -->
      <Td>0.00001</Td>     <!-- derivator time -->
      <u_min>-20.0</u_min> <!-- minimum output clamp -->
      <u_max>20.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Stage #2 drives the ailerons to achieve the desired roll deg. -->
  <pid-controller>
    <name>Nav1 Hold Stage 2</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/heading</prop>
      <value>nav1-hold</value>
    </enable>
    <input>
      <prop>/orientation/roll-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/internal/target-roll-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/aileron</prop>
    </output>
    <config>
      <Kp>0.05</Kp>        <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.01</Td>       <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>


 <!-- =============================================================== -->
 <!-- Pitch Axis Modes                                                -->
 <!-- =============================================================== -->

 <!-- Simple pitch hold --> 
 <pid-controller>
    <name>Pitch hold</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>pitch-hold</value>
    </enable>
    <input>
      <prop>/orientation/pitch-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-pitch-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/elevator-trim</prop>
    </output>
    <config>
      <Kp>-0.05</Kp>      <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.001</Td>    <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
   </pid-controller>

 <!-- Simple pitch hold with yoke --> 
 <pid-controller>
    <name>Pitch hold w/ yoke</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>pitch-hold-yoke</value>
    </enable>
    <input>
      <prop>/orientation/pitch-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-pitch-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/elevator</prop>
    </output>
    <config>
      <Kp>-0.05</Kp>      <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.001</Td>    <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
   </pid-controller>

  <!-- Simple angle of attack hold --> 
  <pid-controller>
    <name>AOA hold</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>aoa-hold</value>
    </enable>
    <input>
      <prop>/orientation/alpha-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-aoa-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/elevator-trim</prop>
    </output>
    <config>
      <Kp>-0.05</Kp>      <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.001</Td>    <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Altitude hold.  3 stage cascade controller. -->

  <!-- Stage #1 sets target rate of climb based on diff between current alt -->
  <!-- and target altitude. -->
  <pi-simple-controller>
    <name>Altitude Hold (Altimeter based) Stage 1</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>altitude-hold</value>
    </enable>
    <input>
      <prop>/position/altitude-ft</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-altitude-ft</prop>
    </reference>
    <output>
      <prop>/autopilot/internal/target-climb-rate-fps</prop>
    </output>
    <config>
      <Kp>0.3</Kp>          <!-- proportional gain -->
      <Ki>0.0</Ki>          <!-- integral gain -->
      <u_min>-8.33</u_min>  <!-- minimum output clamp -->
      <u_max>8.33</u_max>   <!-- maximum output clamp -->
    </config>
  </pi-simple-controller>

  <!-- Stage #2 drives the target-pitch to achieve the desired climb -->
  <!-- rate. -->
  <pid-controller>
    <name>Altitude Hold (Altimeter based) Stage 2</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>altitude-hold</value>
    </enable>
    <input>
      <prop>/velocities/vertical-speed-fps</prop>
    </input>
    <reference>
      <prop>/autopilot/internal/target-climb-rate-fps</prop>
    </reference>
    <output>
      <prop>/autopilot/settings/target-pitch-deg</prop>
    </output>
    <config>
      <Kp>0.5</Kp>        <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>1.0</Ti>       <!-- integrator time -->
      <Td>0.00001</Td>    <!-- derivator time -->
      <u_min>-5.0</u_min> <!-- minimum output clamp -->
      <u_max>5.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Stage #3 drives the elevator trim to achieve the desired pitch -->
  <!-- angle -->
  <pid-controller>
    <name>Pitch hold</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>altitude-hold</value>
      <honor-passive>true</honor-passive>
    </enable>
    <input>
      <prop>/orientation/pitch-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-pitch-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/elevator-trim</prop>
    </output>
    <config>
      <Kp>-0.05</Kp>      <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.001</Td>    <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>


  <!-- AGL hold.  2 stage cascade controller. -->

  <!-- Stage #1 sets target rate of climb based on diff between current agl -->
  <!-- and target agl. -->
  <pid-controller>
    <name>AGL Hold (Altimeter based) Stage 1</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>agl-hold</value>
    </enable>
    <input>
      <prop>/position/altitude-agl-ft</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-agl-ft</prop>
    </reference>
    <output>
      <prop>/autopilot/internal/target-climb-rate-fps</prop>
    </output>
    <config>
      <Kp>1.0</Kp>        <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>25.0</Ti>     <!-- integrator time -->
      <Td>0.000000001</Td>    <!-- derivator time -->
      <u_min>-16.67</u_min> <!-- minimum output clamp -->
      <u_max>8.33</u_max> <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Stage #2 drives the elevator-trim to achieve the desired climb rate. -->
  <pid-controller>
    <name>Altitude Hold (Altimeter based) Stage 2</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>agl-hold</value>
    </enable>
    <input>
      <prop>/velocities/vertical-speed-fps</prop>
    </input>
    <reference>
      <prop>/autopilot/internal/target-climb-rate-fps</prop>
    </reference>
    <output>
      <prop>/controls/flight/elevator-trim</prop>
    </output>
    <config>
      <Kp>-0.05</Kp>      <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>       <!-- integrator time -->
      <Td>0.001</Td>    <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

  <!-- Glideslope hold. -->

  <!-- Stage #2 drives the elevator-trim to achieve the desired climb rate. -->
  <pid-controller>
    <name>Glideslop Hold</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/altitude</prop>
      <value>gs1-hold</value>
    </enable>
    <input>
      <prop>/velocities/vertical-speed-fps</prop>
    </input>
    <reference>
      <prop>/instrumentation/nav[0]/gs-rate-of-climb</prop>
    </reference>
     <output>
      <prop>/controls/flight/elevator-trim</prop>
    </output>
    <config>
      <Kp>-0.05</Kp>      <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>       <!-- integrator time -->
      <Td>0.001</Td>    <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>


 <!-- =============================================================== -->
 <!-- Velocity Modes                                                  -->
 <!-- =============================================================== -->

 <!-- Auto throttle --> 
 <pid-controller>
    <name>Auto Throttle (5 sec lookahead)</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/speed</prop>
      <value>speed-with-throttle</value>
    </enable>
    <input>
      <!-- <prop>/autopilot/internal/lookahead-5-sec-airspeed-kt</prop> -->
      <prop>/velocities/airspeed-kt</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-speed-kt</prop>
    </reference>
    <output>
      <prop>/controls/engines/engine[0]/throttle</prop>
      <prop>/controls/engines/engine[1]/throttle</prop>
      <prop>/controls/engines/engine[2]/throttle</prop>
      <prop>/controls/engines/engine[3]/throttle</prop>
      <prop>/controls/engines/engine[4]/throttle</prop>
      <prop>/controls/engines/engine[5]/throttle</prop>
      <prop>/controls/engines/engine[6]/throttle</prop>
      <prop>/controls/engines/engine[7]/throttle</prop>
    </output>
    <config>
      <Kp>0.1</Kp>        <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>1.0</Ti>       <!-- integrator time -->
      <Td>0.001</Td>      <!-- derivator time -->
      <u_min>0.0</u_min>  <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

 <!-- Hold speed by varying pitch trim (Two stage cascading controller) --> 
 <pid-controller>
    <name>Speed hold (vary pitch trim) Stage #1</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/speed</prop>
      <value>speed-with-pitch-trim</value>
    </enable>
    <input>
      <!-- <prop>/autopilot/internal/lookahead-5-sec-airspeed-kt</prop> -->
      <prop>/velocities/airspeed-kt</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-speed-kt</prop>
    </reference>
    <output>
      <prop>/autopilot/settings/target-pitch-deg</prop>
    </output>
    <config>
      <Kp>-1.0</Kp>       <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>2.0</Ti>        <!-- integrator time -->
      <Td>0.00001</Td>    <!-- derivator time -->
      <u_min>-15.0</u_min><!-- minimum output clamp -->
      <u_max>15.0</u_max> <!-- maximum output clamp -->
    </config>
  </pid-controller>

 <pid-controller>
    <name>Speed hold (vary pitch trim) Stage #2</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/speed</prop>
      <value>speed-with-pitch-trim</value>
    </enable>
    <input>
      <prop>/orientation/pitch-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-pitch-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/elevator-trim</prop>
    </output>
    <config>
      <Kp>-0.05</Kp>      <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.001</Td>    <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

 <!-- Hold speed by varying elevator (2 stage cascading controller)--> 

  <!-- Stage 1, command a forward acceleration rate based on velocity error -->
  <pi-simple-controller>
    <name>Speed hold (vary elevator)</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/speed</prop>
      <value>speed-with-pitch-yoke</value>
    </enable>
    <input>
      <prop>/velocities/airspeed-kt</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-speed-kt</prop>
    </reference>
    <output>
      <prop>/autopilot/settings/target-accel-ktps</prop>
    </output>
    <config>
      <Kp>0.2</Kp>	       <!-- proportional gain -->
      <Ki>0.0</Ki>             <!-- integral gain -->
      <u_min>-1.0</u_min>      <!-- minimum output clamp -->
      <u_max>1.0</u_max>       <!-- maximum output clamp -->
    </config>
  </pi-simple-controller>

  <!-- Stage 2, command elevator to achieve target acceleration rate -->
  <pid-controller>
    <name>Speed hold (vary elevator)</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/speed</prop>
      <value>speed-with-pitch-yoke</value>
    </enable>
    <input>
      <prop>/accelerations/airspeed-ktps</prop>
    </input>
    <reference>
      <prop>//autopilot/settings/target-accel-ktps</prop>
    </reference>
    <output>
      <prop>/controls/flight/elevator</prop>
    </output>
    <config>
      <Kp>0.02</Kp>        <!-- proportional gain -->
      <beta>1.0</beta>     <!-- input value weighing factor -->
      <alpha>0.1</alpha>   <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>   <!-- input value weighing factor for -->
                           <!-- unfiltered derivative error -->
      <Ti>8.0</Ti>        <!-- integrator time -->
      <Td>0.001</Td>       <!-- derivator time -->
      <u_min>-0.25</u_min> <!-- minimum output clamp -->
      <u_max>0.25</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

 <!-- Hold speed by varying pitch via yoke (Two stage cascading controller) --> 
 <pid-controller>
    <name>Speed hold (vary pitch via yoke) Stage #1</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/speed</prop>
      <value>speed-with-pitch-yoke-pitch</value>
    </enable>
    <input>
      <!-- <prop>/autopilot/internal/lookahead-5-sec-airspeed-kt</prop> -->
      <prop>/velocities/airspeed-kt</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-speed-kt</prop>
    </reference>
    <output>
      <prop>/autopilot/settings/target-pitch-deg</prop>
    </output>
    <config>
      <Kp>-1.0</Kp>       <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>2.0</Ti>        <!-- integrator time -->
      <Td>0.00001</Td>    <!-- derivator time -->
      <u_min>-15.0</u_min><!-- minimum output clamp -->
      <u_max>15.0</u_max> <!-- maximum output clamp -->
    </config>
  </pid-controller>

 <pid-controller>
    <name>Speed hold (vary pitch via yoke) Stage #2</name>
    <debug>false</debug>
    <enable>
      <prop>/autopilot/locks/speed</prop>
      <value>speed-with-pitch-yoke-pitch</value>
    </enable>
    <input>
      <prop>/orientation/pitch-deg</prop>
    </input>
    <reference>
      <prop>/autopilot/settings/target-pitch-deg</prop>
    </reference>
    <output>
      <prop>/controls/flight/elevator</prop>
    </output>
    <config>
      <Kp>-0.05</Kp>      <!-- proportional gain -->
      <beta>1.0</beta>    <!-- input value weighing factor -->
      <alpha>0.1</alpha>  <!-- low pass filter weighing factor -->
      <gamma>0.0</gamma>  <!-- input value weighing factor for -->
                          <!-- unfiltered derivative error -->
      <Ti>0.5</Ti>        <!-- integrator time -->
      <Td>0.001</Td>      <!-- derivator time -->
      <u_min>-1.0</u_min> <!-- minimum output clamp -->
      <u_max>1.0</u_max>  <!-- maximum output clamp -->
    </config>
  </pid-controller>

</PropertyList>
