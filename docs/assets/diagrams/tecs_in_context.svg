<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background-color: rgb(255, 255, 255);" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="421px" height="349px" viewBox="-0.5 -0.5 421 349" content="&lt;mxfile modified=&quot;2019-04-16T06:51:38.688Z&quot; host=&quot;www.draw.io&quot; agent=&quot;Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:66.0) Gecko/20100101 Firefox/66.0&quot; version=&quot;10.6.2&quot; etag=&quot;C_2MTIzyA-F2sybKVfGG&quot; type=&quot;google&quot;&gt;&lt;diagram id=&quot;mGTZ6eMru1IxK4K17boz&quot;&gt;5VnbcpswEP0aPzYD4urH1HHah3TGU3cmzSMGGdTKiAoR2/36LiAQiuzUzfiSJi+BPdLqcs4uK8UjZ7LafOJRkX1hCaYjZCWbkXMzQsge+2N41MhWInaHpJwkElPAnPzGErQkWpEEl1pHwRgVpNDBmOU5joWGRZyztd5tyag+axGl2ADmcURN9J4kImvREAUK/4xJmsmZw257q6jrKzdSZlHC1gPImY6cCWdMtG+rzQTTmryOltbvdk9rvy6Oc3GIA5LLENtuaziBnUqTcZGxlOURnSr0I2dVnuB6AAss1eeOsQJAG8AfWIitlC2qBAMoEysqW/GGiO+1+5UnrYdBy81GjtwYW2m066wXp+20ZBWPJeSYm7d7SiEWMVthwbfQhWMaCfKoDxXJmEj7fr3rjBEYEVkygF1bTiXD1wksfQgR8RQL6aXYh5fBMhTUaLJbHznRY0QruVpDMCVHzeA6IwLPi6ghZQ3pp1O/JJROGGW88XWSCIfLGPBScPYTD1r8OMSLZU/8I+YCb/YG2R6epQOynvAl6Vqr1HG7hMgGaYN8a780GqnPMOheNsLR4SEObPJt6+V7nf3Q2KEVdoDybSzNeYY5AYIwPzxpQvn1bCL2ZHl0qFjeZcUKXiRWMHbep1r+ZdXyh2pZV47rH6oY0tPrb2LBKC/QyjG1Ci+nVXDhzGpz5CViBe9PrNCo+t+mk/kzld/658q/XC5RvLPyJ/7C9/zjVH7X9bXKb3tm6bd3lf5jVP7x/3K2HX6ZnHB0plrS3aVeSTHpljMI+ju7vcJBhFIKWztz/DMYi4iaZtc6TjogVz8InzUdbNsgeIR8KmpuWHO9UdT6vyrWNXwom1i/hg72uNioRnhL6+ct2YAIyLoneQqPGSuJICyH18lAu3YmWGQ7WetqSArcCl03XZ+c5fiJmBKKKElzMGPQok6Dj7VSBK7s17JhRZKkyfFdgaKH0jGkDnWpdyjdq6rdeY6hNDLrR8aZELB9YBuLor+ZvknuXU/nfhf57qm4N2/sMyLi7F0Q79kXJN41iP/Kmh6vnHdVZk706TmrCt7eIrPoPvrXQhBRJfW3CKBoVVPS/q1PIxmvyrYYYamaBWcsWJXyio2ysuCqovSFZvH2q4yL9PO1g0ypw8CU2j2G1L4h9TXhZYGbw8ArT7oTVJnxGbMsMKmnfXq8feqf1pnTUQ+m+jGm/f+9+knLmf4B&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><path d="M 210 260 L 210 333.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 210 338.88 L 206.5 331.88 L 210 333.63 L 213.5 331.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="0" y="0" width="420" height="260" rx="39" ry="39" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><path d="M 273 180 L 273 203.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 273 208.88 L 269.5 201.88 L 273 203.63 L 276.5 201.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 333 180 L 333 203.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 333 208.88 L 329.5 201.88 L 333 203.63 L 336.5 201.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 273 90 L 273 113.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 273 118.88 L 269.5 111.88 L 273 113.63 L 276.5 111.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 333 90 L 333 113.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 333 118.88 L 329.5 111.88 L 333 113.63 L 336.5 111.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="243" y="120" width="120" height="60" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(286.5,143.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="32" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 33px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">TECS</div></div></foreignObject><text x="16" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">TECS</text></switch></g><path d="M 100 180 L 100 203.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 100 208.88 L 96.5 201.88 L 100 203.63 L 103.5 201.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="40" y="120" width="120" height="60" fill-opacity="0.4" fill="#fff2cc" stroke="#d6b656" stroke-opacity="0.4" pointer-events="none"/><g transform="translate(65.5,143.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 69px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">L1 controller</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">L1 controller</text></switch></g><g transform="translate(80.5,7.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="258" height="44" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 258px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 19px">Fixed Wing Position Controller</font></div></div></foreignObject><text x="129" y="28" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;font style="font-size: 19px"&gt;Fixed Wing Position Controller&lt;/font&gt;</text></switch></g><g transform="translate(247.5,216.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="44" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 44px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Throttle setpoint</div></div></foreignObject><text x="22" y="20" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Throttle setpoint</text></switch></g><g transform="translate(307.5,216.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="44" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 44px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Pitch setpoint</div></div></foreignObject><text x="22" y="20" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Pitch setpoint</text></switch></g><g transform="translate(77.5,216.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="44" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 44px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Roll setpoint</div></div></foreignObject><text x="22" y="20" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Roll setpoint</text></switch></g><g transform="translate(223.5,281.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="85" height="56" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 85px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b>Attitude &amp; thrust stepoint to attitude controller<br /></b></div></div></foreignObject><text x="43" y="34" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(244.5,56.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="50" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 50px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Airspeed setpoint</div></div></foreignObject><text x="25" y="20" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Airspeed setpoint</text></switch></g><g transform="translate(307.5,56.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="44" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 44px; white-space: normal; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Altitude setpoint</div></div></foreignObject><text x="22" y="20" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Altitude setpoint</text></switch></g></g></svg>