<?xml version="1.0" ?>
<sdf version="1.5">

  <model name="aruco_cam">

    <link name="link">

      <pose>0 0 0 -1.5707963259 1.5707963259 0</pose>

      <inertial>
        <mass>0.05</mass>
        <inertia>
          <ixx>4.166666666666667e-06</ixx>
          <iyy>5.208333333333334e-07</iyy>
          <izz>3.854166666666666e-06</izz>
        </inertia>
      </inertial>

      <visual name="visual">
        <geometry>
          <box>
            <size>0.005 0.03 0.01</size>
          </box>
        </geometry>
      </visual>

      <collision name="collision">
        <geometry>
          <box>
            <size>0.005 0.03 0.01</size>
          </box>
        </geometry>
      </collision>

      <sensor name="aruco_cam" type="camera">
        <always_on>true</always_on>
        <update_rate>10</update_rate>
        <visualize>true</visualize>
        <topic>/arucoMarker</topic>
        <camera>
          <horizontal_fov>1.308997</horizontal_fov> <!-- 75 deg  -->
          <lens>
            <intrinsics>
              <fx>398.77559882561974</fx>
              <fy>396.51789974056226</fy>
              <cx>325.46639260681752</cx>
              <cy>239.06162259594086</cy>
              <s>0</s>
            </intrinsics>
            <scale_to_hfov>false</scale_to_hfov>
          </lens>
          <distortion>
            <k1>-0.074500551440667862</k1>
            <k2>-0.15464660896318899</k2>
            <k3>-0.00047134015104217627</k3>
            <p1>0.0036767321906851489</p1>
            <p2>0.28738933210835571</p2>
            <center>0.5 0.5</center>
          </distortion>
          <image>
            <width>640</width>
            <height>480</height>
            <format>L8</format>
          </image>
          <clip>
            <near>0.1</near>
            <far>100</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.001</stddev>
          </noise>
        </camera>

        <plugin name="aruco_marker_plugin" filename="libgazebo_aruco_plugin.so">
            <robotNamespace></robotNamespace>
            <landPadName>land_pad</landPadName> <!-- name of the model on which the ArUco marker is printed (as defined in the sdf)-->
        </plugin>
        
      </sensor>
    </link>
  </model>

</sdf>

<!-- vim: set et ft=xml fenc=utf-8 ff=unix sts=0 sw=2 ts=2 : -->
