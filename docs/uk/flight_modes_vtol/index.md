# Режими польоту (VTOL)

Режими польоту забезпечують автопілот підтримку для полегшення ручного польоту транспортного засобу, автоматизацію загальних завдань, таких як зльот та посадка, виконання автономних місій або делегування керування польотом зовнішній системі.

Вертикально-взлітно-посадкові (VTOL) транспортні засоби можуть літати як багтроторні апарати, так і літаки, і, як правило, мають точно таке ж саме поведінку, як і відповідний тип транспортного засобу:

Поведінка режимів польоту, специфічних для VTOL, розглядається в таких випадках:

- [Режим «Посадка»](../flight_modes_vtol/land.md): VTOL, що літає як літак, перейде в режим багтроторного літака перед посадкою.
- [Режим «Місія»](../flight_modes_vtol/mission.md): Місії підтримують специфічні для ВВП команди місій для зльоту, посадки та переходу між типами транспортних засобів.
- [Режим «Повернення»](../flight_modes_vtol/return.md): VTOL транспортні засоби діють трохи інакше при поверненні, якщо вони повертаються як багтроторний літак або літак, ніж відповідні транспортні засоби.

Для інших режимів польоту дивіться специфічну для транспортного засобу поведінку:

- [Flight Modes (Multicopter)](../flight_modes_mc/index.md)
- [Flight Modes (Fixed-Wing)](../flight_modes_fw/index.md)

## Подальша інформація

- [Базова конфігурація > Режими польоту](../config/flight_mode.md) - Як відповідати перемикачі керування RC конкретним режимам польоту
