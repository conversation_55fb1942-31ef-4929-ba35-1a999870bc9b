# Modules & Commands Reference

以下页面介绍了 PX4 模块， 驱动和命令。
They describe the provided functionality, high-level implementation overview and how
to use the command-line interface.

:::info
**This is auto-generated from the source code** and contains the most recent modules documentation.
:::

It is not a complete list and NuttX provides some additional commands
as well (such as `free`). Use `help` on the console to get a list of all
available commands, and in most cases `command help` will print the usage.

Since this is generated from source, errors must be reported/fixed
in the [PX4-Autopilot](https://github.com/PX4/PX4-Autopilot) repository.
文档页可以在固件目录的根目录下运行如下命令生成：

```
make module_documentation
```

The generated files will be written to the `modules` directory.

## 分类

- [Autotune](modules_autotune.md)
- [Command](modules_command.md)
- [Communication](modules_communication.md)
- [Controller](modules_controller.md)
- [Driver](modules_driver.md)
- [Estimator](modules_estimator.md)
- [Simulation](modules_simulation.md)
- [System](modules_system.md)
- [Template](modules_template.md)
