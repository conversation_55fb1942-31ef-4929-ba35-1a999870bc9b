<?xml version="1.0"?>
<PropertyList>

  <name>breakage</name>
  <layout>vbox</layout>
  

  <group>
    <layout>hbox</layout>
    <text>
      <label>Rope Breakage (winch)</label>
    </text>

    <empty>
      <stretch>true</stretch>
    </empty>

    <button>
      <legend/>
      <key>Esc</key>
      <pref-width>16</pref-width>
      <pref-height>16</pref-height>
      <border>2</border>
      <binding>
        <command>dialog-close</command>
      </binding>
    </button>
  </group>

  <hrule/>

  <group>
    <stretch>true</stretch>
    <layout>vbox</layout>
    <halign>center</halign>
    <valign>top</valign>

    <checkbox>
      <halign>left</halign>
      <label>Enable Rope Breakage</label>
      <property>/controls/breakage/enabled</property>
      <binding>
        <command>property-toggle</command>
        <property>/controls/breakage/enabled</property>
      </binding>
    </checkbox>

    <group>
      <layout>hbox</layout>

      <text>
        <label>Breakage altitude:</label>
      </text>

      <slider>
        <row>0</row>
        <col>2</col>
        <min>0</min> 
        <max>500</max>
        <property>/controls/breakage/altitude-m</property>
        <binding>
          <command>dialog-apply</command>
        </binding>
      </slider>

      <text>
        <pref-width>16</pref-width>
        <property>/controls/breakage/altitude-m</property>
        <format>%2.0f</format>
        <live>true</live>
      </text>

      <text>
        <label>m</label>
      </text>

    </group>
    
  </group> </PropertyList> 
