<sdf version='1.6'>
  <model name='believer'>
    <pose>0 0 0.246 0 0 0</pose>
    <link name='base_link'>
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0.15 0 0.1 0 0 0</pose>
        <mass>2.05</mass>
        <inertia>
          <ixx>0.16632</ixx>
          <ixy>0</ixy>
          <ixz>0.0755</ixz>
          <iyy>0.3899</iyy>
          <iyz>0</iyz>
          <izz>0.5243</izz>
        </inertia>
      </inertial>
      <collision name='base_link_collision'>
        <pose>0 0 -0.07 0 0 0</pose>
        <geometry>
          <box>
            <size>1.088 2.591 0.103</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <max_vel>10</max_vel>
              <min_depth>0.01</min_depth>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name='base_link_inertia_visual'>
        <pose>0 0 0 0 0 3.141</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://believer/meshes/believer_body.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/DarkGray</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
      <self_collide>0</self_collide>
    </link>
    <link name='imu_link'>
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.015</mass>
        <inertia>
          <ixx>1e-05</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>1e-05</iyy>
          <iyz>0</iyz>
          <izz>1e-05</izz>
        </inertia>
      </inertial>
    </link>
    <joint name='imu_joint' type='revolute'>
      <child>imu_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>0</lower>
          <upper>0</upper>
          <effort>0</effort>
          <velocity>0</velocity>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <include>
      <uri>model://airspeed</uri>
      <pose>0 0 0 0 0 0</pose>
      <name>airspeed</name>
    </include>
    <joint name='airspeed_joint' type='fixed'>
      <child>airspeed::link</child>
      <parent>base_link</parent>
    </joint>
    <link name='rotor_right'>
      <pose>0.05 -0.25 0.05 0 1.57 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.005</mass>
        <inertia>
          <ixx>9.75e-07</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.000166704</iyy>
          <iyz>0</iyz>
          <izz>0.000167604</izz>
        </inertia>
      </inertial>
      <collision name='rotor_right_collision'>
        <pose>0.0 0 0.0 0 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.005</length>
            <radius>0.1</radius>
          </cylinder>
        </geometry>
        <surface>
          <contact>
            <ode/>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <visual name='propeller__visual'>
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1.0 1.0 1.0</scale>
            <uri>model://believer/meshes/propeller_cw.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
      <self_collide>0</self_collide>
    </link>
    <link name='rotor_left'>
      <pose>0.05 0.25 0.05 0 1.57 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.005</mass>
        <inertia>
          <ixx>9.75e-07</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.000166704</iyy>
          <iyz>0</iyz>
          <izz>0.000167604</izz>
        </inertia>
      </inertial>
      <collision name='rotor_left_collision'>
        <pose>0.0 0 -0.0 0 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.005</length>
            <radius>0.1</radius>
          </cylinder>
        </geometry>
        <surface>
          <contact>
            <ode/>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <visual name='propeller__visual'>
        <pose>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1.0 1.0 1.0</scale>
            <uri>model://believer/meshes/propeller_cw.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
      <self_collide>0</self_collide>
    </link>
    <joint name='rotor_right_joint' type='revolute'>
      <child>rotor_right</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <joint name='rotor_left_joint' type='revolute'>
      <child>rotor_left</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <link name="left_elevon">
      <pose>-0.225 0.6 0.03 0 -0 0</pose>
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 0.3 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='left_elevon_visual'>
        <pose>0.225 -0.60 -0.03 0 -0 3.1415</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://believer/meshes/believer_aileron_left.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
    </link>
    <link name="right_elevon">
      <pose>-0.225 -0.6 0.03 0 -0 0</pose>
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 -0.3 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='right_evelon_visual'>
        <pose>0.225 0.60 -0.03 0 -0 3.141</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://believer/meshes/believer_aileron_right.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
    </link>
    <link name="taileron_left">
      <pose>0.0 0 0.0 0 0 0</pose>
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose> -0.5 0 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='taileron_left_visual'>
        <pose>0 0 0 0 0.0 3.141592</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://believer/meshes/believer_taileron_left.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
    </link>
    <link name="taileron_right">
      <pose>0.0 0 0.0 0 0 0</pose>
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>-0.5 0 0.05 0 0 0 </pose>
      </inertial>
      <visual name='taileron_right_visual'>
        <pose>0 0 0 0 0.0 3.141592</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://believer/meshes/believer_taileron_right.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
    </link>
    <joint name='left_elevon_joint' type='revolute'>
      <parent>base_link</parent>
      <child>left_elevon</child>
      <pose>-0.0 0.0 0.0 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <joint name='right_elevon_joint' type='revolute'>
      <parent>base_link</parent>
      <child>right_elevon</child>
      <pose>-0.0 -0.0 0.0 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <joint name='taileron_left_joint' type='revolute'>
      <parent>base_link</parent>
      <child>taileron_left</child>
      <pose>-0.825 0.082 0.05 0.00 0 0.0</pose>
      <axis>
        <xyz>0.12 0.5 0.4</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <joint name='taileron_right_joint' type='revolute'>
      <parent>base_link</parent>
      <child>taileron_right</child>
      <pose>-0.825 -0.082 0.05 0.00 0 0.0</pose>
      <axis>
        <xyz>-0.12 -0.5 0.4</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <include>
      <uri>model://gps</uri>
      <pose>0 0 0 0 0 0</pose>
      <name>gps</name>
    </include>
    <joint name='gps_joint' type='fixed'>
      <child>gps::link</child>
      <parent>base_link</parent>
    </joint>
    <plugin name="left_wing" filename="libLiftDragPlugin.so">
      <a0>0.05984281113</a0>
      <cla>4.752798721</cla>
      <cda>0.6417112299</cda>
      <cma>0.0</cma>
      <alpha_stall>0.3391428111</alpha_stall>
      <cla_stall>-3.85</cla_stall>
      <cda_stall>-0.9233984055</cda_stall>
      <cma_stall>0</cma_stall>
      <cp>-0.05 0.45 0.05</cp>
      <area>0.6</area>
      <air_density>1.2041</air_density>
      <forward>1 0 0</forward>
      <upward>0 0 1</upward>
      <link_name>base_link</link_name>
      <control_joint_name>
        left_elevon_joint
      </control_joint_name>
      <control_joint_rad_to_cl>-0.5</control_joint_rad_to_cl>
      <robotNamespace></robotNamespace>
      <windSubTopic>world_wind</windSubTopic>
    </plugin>
    <plugin name="right_wing" filename="libLiftDragPlugin.so">
      <a0>0.05984281113</a0>
      <cla>4.752798721</cla>
      <cda>0.6417112299</cda>
      <cma>0.0</cma>
      <alpha_stall>0.3391428111</alpha_stall>
      <cla_stall>-3.85</cla_stall>
      <cda_stall>-0.9233984055</cda_stall>
      <cma_stall>0</cma_stall>
      <cp>-0.05 -0.45 0.05</cp>
      <area>0.6</area>
      <air_density>1.2041</air_density>
      <forward>1 0 0</forward>
      <upward>0 0 1</upward>
      <link_name>base_link</link_name>
      <control_joint_name>
        right_elevon_joint
      </control_joint_name>
      <control_joint_rad_to_cl>-0.5</control_joint_rad_to_cl>
      <robotNamespace></robotNamespace>
      <windSubTopic>world_wind</windSubTopic>
    </plugin>
    <plugin name="taileron_left" filename="libLiftDragPlugin.so">
      <a0>-0.2</a0>
      <cla>4.752798721</cla>
      <cda>0.6417112299</cda>
      <cma>0.0</cma>
      <alpha_stall>0.3391428111</alpha_stall>
      <cla_stall>-3.85</cla_stall>
      <cda_stall>-0.9233984055</cda_stall>
      <cma_stall>0</cma_stall>
      <cp>-0.5 0 0</cp>
      <area>0.01</area>
      <air_density>1.2041</air_density>
      <forward>1 0 0</forward>
      <upward>0 0 1</upward>
      <link_name>base_link</link_name>
      <control_joint_name>
        taileron_left_joint
      </control_joint_name>
      <control_joint_rad_to_cl>-4.0</control_joint_rad_to_cl>
      <robotNamespace></robotNamespace>
      <windSubTopic>world_wind</windSubTopic>
    </plugin>
    <plugin name="taileron_right" filename="libLiftDragPlugin.so">
      <a0>0.0</a0>
      <cla>4.752798721</cla>
      <cda>0.6417112299</cda>
      <cma>0.0</cma>
      <alpha_stall>0.3391428111</alpha_stall>
      <cla_stall>-3.85</cla_stall>
      <cda_stall>-0.9233984055</cda_stall>
      <cma_stall>0</cma_stall>
      <cp>-0.5 0 0.05</cp>
      <area>0.02</area>
      <air_density>1.2041</air_density>
      <forward>1 0 0</forward>
      <upward>0 1 0</upward>
      <link_name>base_link</link_name>
      <control_joint_name>
         taileron_right_joint
      </control_joint_name>
      <control_joint_rad_to_cl>4.0</control_joint_rad_to_cl>
      <robotNamespace></robotNamespace>
      <windSubTopic>world_wind</windSubTopic>
    </plugin>
    <plugin name='right' filename='libgazebo_motor_model.so'>
      <robotNamespace></robotNamespace>
      <jointName>rotor_right_joint</jointName>
      <linkName>rotor_right</linkName>
      <turningDirection>cw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>3500</maxRotVelocity>
      <motorConstant>8.54858e-06</motorConstant>
      <momentConstant>0.01</momentConstant>
      <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
      <motorNumber>4</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
      <motorSpeedPubTopic>/motor_speed/4</motorSpeedPubTopic>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <robotNamespace></robotNamespace>
      <windSubTopic>world_wind</windSubTopic>
    </plugin>
    <plugin name='left' filename='libgazebo_motor_model.so'>
      <robotNamespace></robotNamespace>
      <jointName>rotor_left_joint</jointName>
      <linkName>rotor_left</linkName>
      <turningDirection>cw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>3500</maxRotVelocity>
      <motorConstant>8.54858e-06</motorConstant>
      <momentConstant>0.01</momentConstant>
      <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
      <motorNumber>4</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
      <motorSpeedPubTopic>/motor_speed/4</motorSpeedPubTopic>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <robotNamespace></robotNamespace>
      <windSubTopic>world_wind</windSubTopic>
    </plugin>
    <plugin name='gazebo_imu_plugin' filename='libgazebo_imu_plugin.so'>
      <robotNamespace></robotNamespace>
      <linkName>imu_link</linkName>
      <imuTopic>/imu</imuTopic>
    </plugin>
    <plugin name='groundtruth_plugin' filename='libgazebo_groundtruth_plugin.so'>
      <robotNamespace/>
    </plugin>
    <plugin name='magnetometer_plugin' filename='libgazebo_magnetometer_plugin.so'>
      <robotNamespace/>
      <pubRate>100</pubRate>
      <noiseDensity>0.0004</noiseDensity>
      <randomWalk>6.4e-06</randomWalk>
      <biasCorrelationTime>600</biasCorrelationTime>
      <magTopic>/mag</magTopic>
    </plugin>
    <plugin name='barometer_plugin' filename='libgazebo_barometer_plugin.so'>
      <robotNamespace/>
      <pubRate>50</pubRate>
      <baroTopic>/baro</baroTopic>
    </plugin>
    <plugin name='mavlink_interface' filename='libgazebo_mavlink_interface.so'>
      <robotNamespace></robotNamespace>
      <imuSubTopic>/imu</imuSubTopic>
      <magSubTopic>/mag</magSubTopic>
      <baroSubTopic>/baro</baroSubTopic>
      <mavlink_addr>INADDR_ANY</mavlink_addr>
      <mavlink_tcp_port>4560</mavlink_tcp_port>
      <mavlink_udp_port>14560</mavlink_udp_port>
      <serialEnabled>false</serialEnabled>
      <serialDevice>/dev/ttyACM0</serialDevice>
      <baudRate>921600</baudRate>
      <qgc_addr>INADDR_ANY</qgc_addr>
      <qgc_udp_port>14550</qgc_udp_port>
      <sdk_addr>INADDR_ANY</sdk_addr>
      <sdk_udp_port>14540</sdk_udp_port>
      <hil_mode>false</hil_mode>
      <hil_state_level>false</hil_state_level>
      <enable_lockstep>true</enable_lockstep>
      <use_tcp>true</use_tcp>
      <motorSpeedCommandPubTopic>/gazebo/command/motor_speed</motorSpeedCommandPubTopic>
      <control_channels>
        <channel name="taileron_right">
          <input_index>2</input_index>
          <input_offset>0</input_offset>
          <input_scaling>1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>taileron_right_joint</joint_name>
        </channel>
        <channel name="rotor4">
          <input_index>4</input_index>
          <input_offset>0</input_offset>
          <input_scaling>3500</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_name>rotor_right_joint</joint_name>
        </channel>
        <channel name="left_elevon">
          <input_index>5</input_index>
          <input_offset>0</input_offset>
          <input_scaling>1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>left_elevon_joint</joint_name>
        </channel>
        <channel name="right_elevon">
          <input_index>6</input_index>
          <input_offset>0</input_offset>
          <input_scaling>1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>right_elevon_joint</joint_name>
        </channel>
        <channel name="taileron_left">
          <input_index>7</input_index>
          <input_offset>0</input_offset>
          <input_scaling>1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>taileron_left_joint</joint_name>
        </channel>
      </control_channels>
    </plugin>
    <static>0</static>
  </model>
</sdf>
