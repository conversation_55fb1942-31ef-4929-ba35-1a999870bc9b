# Режим повернення (фіксоване крило)

<img src="../../assets/site/position_fixed.svg" title="Position fix required (e.g. GPS)" width="30px" />

Режим польоту _Return_ використовується для _повернення транспортного засобу до безпеки_ по вільному шляху до безпечного пункту призначення, де він може приземлитися.

Літаки з фіксованим крилом за замовчуванням використовують тип повернення до призначення [місії посадки/точка збору](../flight_modes/return.md#mission-landing-rally-point-return-type-rtl-type-1), і завжди передбачається, що у них буде завдання з шаблоном посадки.
З цією конфігурацією режим повернення зумовлює підняття транспортного засобу на мінімальну безпечну висоту над перешкодами (якщо потрібно), польот до початку схеми посадки, визначеної у плані місії, і далі слідує за нею для посадки.

Фіксоване крило підтримує [інші типи повернення PX4](../flight_modes/return.md#return-types-rtl-type), включаючи повернення додому/радільної точки, маршрут місії та найближче безпечне місце призначення.
За замовчуванням рекомендується використовувати цей тип.

::: info

- Режим автоматичний - для керування апаратом не потрібно втручання користувача.
- Режим вимагає глобальної оцінки 3D-позиції (з GPS або виведеної з [локальної позиції](../ros/external_position_estimation.md#enabling-auto-modes-with-a-local-position)).
  - Літаючі транспортні засоби не можуть переключатися на цей режим без глобального положення.
  - Літаючі транспортні засоби перейдуть в режим аварійної безпеки, якщо втратять оцінку положення.
- Режим вимагає встановленої домашньої позиції.
- Режим перешкоджає зброюванню (транспортний засіб повинен бути зброєний при переході на цей режим).
- Перемикачі керування RC можуть використовуватися для зміни режимів польоту на будь-якому транспортному засобі.
- Рух стіків радіокерування ігнорується.

<!-- https://github.com/PX4/PX4-Autopilot/blob/main/src/modules/commander/ModeUtil/mode_requirements.cpp -->

:::

## Технічний підсумок

Літальні апарати з фіксованим крилом за замовчуванням використовують тип повернення до призначення _місії посадки/точка збору_.
У цьому типі повернення транспортний засіб:

- Піднімається на безпечну мінімальну висоту повернення, визначену за допомогою [RTL_RETURN_ALT](#RTL_RETURN_ALT) (безпечно вище будь-яких очікуваних перешкод).
  Транспортний засіб підтримує свою початкову висоту, якщо вона вище, ніж мінімальна висота повернення.
  Зверніть увагу, що висоту повернення не можна налаштувати, використовуючи параметр "cone" в літаках з фіксованим крилом.
- Летить прямим шляхом на постійній висоті до призначення, яким буде найближча з точки старту місійного маршруту посадки та будь-яка точка збору, або домашня локація, якщо місійний маршрут посадки або точки збору не визначені.
- Якщо призначення - це шаблон посадки місії, воно буде слідувати за шаблоном для посадки.
- Якщо місце призначення - це точка ралі або дім, воно спуститься на висоту спуску, а потім обережно почекає або приземлиться (в залежності від параметрів посадки).

Очікується, що літальний апарат з фіксованим крилом буде використовувати схему посадки, визначену у місії як пункт повернення, оскільки це найбезпечніший спосіб автоматичної посадки.
Це вимога зазвичай виконується параметром [MIS_TKO_LAND_REQ](#MIS_TKO_LAND_REQ).

Місійний маршрут посадки складається з команди [MAV_CMD_DO_LAND_START](https://mavlink.io/en/messages/common.html#MAV_CMD_DO_LAND_START), однієї або кількох позиційних точок та точки посадки [MAV_CMD_NAV_LAND](https://mavlink.io/en/messages/common.html#MAV_CMD_NAV_LAND).

Коли пункт призначення - ралі-пункт або домашнє місце, по прибутті транспортний засіб швидко знизиться на висоту, визначену [RTL_DESCEND_ALT](#RTL_DESCEND_ALT), за замовчуванням обертається відповідно навколо пункту призначення нескінчено при радіусі [RTL_LOITER_RAD](#RTL_LOITER_RAD).
Транспортний засіб може бути змушений приземлитися у пункті призначення, змінивши [RTL_LAND_DELAY](#RTL_LAND_DELAY) так, щоб він не був -1.
У цьому випадку транспортний засіб приземлиться так само, як [Режим посадки](../flight_modes_fw/land.md).

## Параметри

Параметри RTL перелічені в [Референсі параметрів > Режим повернення](../advanced_config/parameter_reference.md#return-mode).
Якщо використовується місійна посадка, значення [RTL_RETURN_ALT](#RTL_RETURN_ALT) та [RTL_DESCEND_ALT](#RTL_DESCEND_ALT) є важливими.
Інші параметри стають актуальними, якщо призначенням є точка збору або домашня локація.

| Параметр                                                                                                                                                                   | Опис                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a id="RTL_TYPE"></a>[RTL_TYPE](../advanced_config/parameter_reference.md#RTL_TYPE)                                                                   | Тип повернення.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| <a id="RTL_RETURN_ALT"></a>[RTL_RETURN_ALT](../advanced_config/parameter_reference.md#RTL_RETURN_ALT)                            | Висота повернення в метрах (за замовчуванням: 60 м). Якщо вже знаходиться вище цієї висоти, транспортний засіб повернеться на поточну висоту.                                                                                                                                                                                                                                                                                                                                      |
| <a id="RTL_DESCEND_ALT"></a>[RTL_DESCEND_ALT](../advanced_config/parameter_reference.md#RTL_DESCEND_ALT)                         | Мінімальна висота повернення і висота, на якій повітряне судно сповільнює або зупиняє своє початкове зниження з вищої висоти повернення (за замовчуванням: 30 м)                                                                                                                                                                                                                                                                                                                                                   |
| <a id="RTL_LAND_DELAY"></a>[RTL_LAND_DELAY](../advanced_config/parameter_reference.md#RTL_LAND_DELAY)                            | Час очікування на висоті `RTL_DESCEND_ALT` перед посадкою (за замовчуванням: 0.5 с) - за замовчуванням цей період короткий, щоб транспортний засіб просто сповільнився, а потім враз відразу приземлився. Якщо встановлено значення -1, система буде кружляти на висоті `RTL_DESCEND_ALT` замість посадки. Затримка надається для того, щоб ви могли налаштувати час для розгортання шасі для посадки (автоматично спрацьовує). |
| <a id="RTL_LOITER_RAD"></a>[RTL_LOITER_RAD](../advanced_config/parameter_reference.md#RTL_LOITER_RAD)                            | [Тільки фіксоване крило] Радіус круга обертання (у значенні [RTL_LAND_DELAY](#RTL_LAND_DELAY)).                                                                                                                                                                                                                                                                                                                      |
| <a id="MIS_TKO_LAND_REQ"></a>[MIS_TKO_LAND_REQ](../advanced_config/parameter_reference.md#MIS_TKO_LAND_REQ) | Вкажіть, чи потрібна місія для посадки або злітної траєкторії необхідна. Фіксовані крила, як правило, потребують цього.                                                                                                                                                                                                                                                                                                                                                                                               |

## Дивіться також

- [Режим повернення (Загальний)](../flight_modes/return.md)
- [Режим повернення (Мультикоптер)](../flight_modes_mc/return.md)
- [Режим повернення (VTOL)](../flight_modes_vtol/return.md)
