/****************************************************************************
 *
 *   Copyright (c) 2020-2022 ThunderFly s.r.o.. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name PX4 nor the names of its contributors may be
 *    used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 * ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 ****************************************************************************/

/**
 * @file fg_communicator.h
 *
 * <AUTHOR> s.r.o., Vít Hanousek <<EMAIL>>
 * @url https://github.com/ThunderFly-aerospace
 *
 * FlightGear communication socket.
 */

#ifndef FG_COMMUNICATOR_H
#define FG_COMMUNICATOR_H

#include "vehicle_state.h"


#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <netdb.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <poll.h>


union temp64 {
	int64_t ll;
	int32_t l[2];
};


class FGCommunicator
{

private:
	VehicleState *vehicle;
	struct sockaddr_in  fg_addr_out, my_addr_out;
	int fgSockOut;
	int fgSockIn;
	struct sockaddr_in  fg_addr_in;

	double *sendBuff;

    const int FGOutPortBase=15200;
    const int FGInPortBase=15300;

	void swap64(void *p);
public:
	FGCommunicator(VehicleState *v);
	~FGCommunicator();
	int Init(int portOffset);
	int Clean();

	int Send();
	int Recieve(bool blocking);

};



#endif
