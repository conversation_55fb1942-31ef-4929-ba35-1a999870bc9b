syntax = "proto2";
package sensor_msgs.msgs;

message SITLGps
{
  required uint64  time_usec            = 1;
  optional uint64  time_utc_usec        = 2;
  required double latitude_deg          = 3;
  required double longitude_deg         = 4;
  required double altitude              = 5;
  optional double eph                   = 6;
  optional double epv                   = 7;
  optional double velocity              = 8;
  optional double velocity_east         = 9;
  optional double velocity_north        = 10;
  optional double velocity_up           = 11;
}
