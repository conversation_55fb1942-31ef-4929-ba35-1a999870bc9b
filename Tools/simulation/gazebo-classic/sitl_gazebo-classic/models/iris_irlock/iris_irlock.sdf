<sdf version='1.5'>
  <model name='iris_irlock'>

    <include>
      <uri>model://iris</uri>
    </include>

    <include>
      <uri>model://lidar</uri>
      <pose>-0.12 0 0 0 0 0</pose>
    </include>
    <joint name="lidar_joint" type="revolute">
      <child>lidar::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

    <include>
      <uri>model://irlock</uri>
      <pose>-0 0 0 0 1.5708 0</pose>
    </include>
    <joint name="irlock_joint" type="revolute">
      <child>camera::link</child>
      <parent>iris::base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

    <!--aruco camera-->
    <include>
      <uri>model://aruco_cam</uri>
      <pose>0.0 0 0.0 0 0 0</pose>
    </include>
    <joint name="aruco_cam_joint" type="revolute">
      <parent>iris::base_link</parent>
      <child>aruco_cam::link</child>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <upper>0</upper>
          <lower>0</lower>
        </limit>
      </axis>
    </joint>

  </model>
</sdf>
