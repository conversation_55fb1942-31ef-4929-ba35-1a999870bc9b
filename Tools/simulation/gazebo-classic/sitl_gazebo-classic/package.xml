<?xml version="1.0"?>
<package>
  <name>mavlink_sitl_gazebo</name>
  <version>1.0.0</version>
  <description>The mavlink_sitl_gazebo package</description>

  <!-- One maintainer tag required, multiple allowed, one person per tag -->
  <!-- Example: -->
  <!-- <maintainer email="<EMAIL>"><PERSON></maintainer> -->
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>

  <!-- One license tag required, multiple allowed, one license per tag -->
  <!-- Commonly used license strings: -->
  <!-- BSD, MIT, Boost Software License, GPLv2, GPLv3, LGPLv2.1, LGPLv3 -->
  <license>BSD</license>

  <!-- Url tags are optional, but mutiple are allowed, one per tag -->
  <!-- Optional attribute type can be: website, bugtracker, or repository -->
  <!-- Example: -->
  <!-- <url type="website">http://wiki.ros.org/mavlink_sitl_gazebo</url> -->
  <url type="repository">https://github.com/PX4/sitl_gazebo</url>

  <!-- Author tags are optional, mutiple are allowed, one per tag -->
  <!-- Authors do not have to be maintainers, but could be -->
  <!-- Example: -->
  <author email="<EMAIL>">Lorenz Meier</author>
  <author email="<EMAIL>">James Goppert</author>

  <!-- The *_depend tags are used to specify dependencies -->
  <!-- Dependencies can be catkin packages or system dependencies -->
  <!-- Examples: -->
  <!-- Use build_depend for packages you need at compile time: -->
  <!-- <build_depend>message_generation</build_depend> -->
  <!-- Use buildtool_depend for build tool packages: -->
  <!-- <buildtool_depend>catkin</buildtool_depend> -->
  <!-- Use run_depend for packages you need at runtime: -->
  <!-- <run_depend>message_runtime</run_depend> -->
  <!-- Use test_depend for packages you need only for testing: -->
  <test_depend>gtest</test_depend>
  <buildtool_depend>catkin</buildtool_depend>
  <buildtool_depend>gazebo_ros</buildtool_depend>
  <build_depend>eigen</build_depend>
  <build_depend>gazebo_ros</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>libgstreamer-plugins-base1.0-dev</build_depend>
  <build_depend>mavlink</build_depend>
  <build_depend>mavros</build_depend>
  <build_depend>mavros_msgs</build_depend>
  <build_depend>protobuf-dev</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <run_depend>eigen</run_depend>
  <run_depend>gazebo_ros</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>gstreamer1.0-plugins-bad</run_depend>
  <run_depend>gstreamer1.0-plugins-good</run_depend>
  <run_depend>gstreamer1.0-plugins-ugly</run_depend>
  <run_depend>mavlink</run_depend>
  <run_depend>mavros</run_depend>
  <run_depend>mavros_msgs</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>std_msgs</run_depend>

  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- Other tools can request additional information be placed here -->
    <gazebo_ros plugin_path="${prefix}/lib" gazebo_media_path="${prefix}" gazebo_model_path="${prefix}/models"/>
  </export>
</package>
