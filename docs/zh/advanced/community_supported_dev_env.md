# 社区支持的开发者工具

本节包含 _社区支持的_ 开发搭建、集成开发环境、模拟器和其他工具的信息。

:::warning
这些设置没有核心开发团队的维护、测试或支持
他们可能与当前版本的 PX4 工作, 也可能不工作。

关于核心开发团队支持的环境和工具的信息，请参阅 [工具链安装](../dev_setup/dev_env.md)
:::

这些工具有来自其社区的不同程度的支持 (有些得到很好的支持，有些则没有) 。
关于这些工具的问题应该在 [讨论论坛](../contribute/support.md#forums-and-chat)提出

- 开发平台/设置
  - [CentOS Linux](../dev_setup/dev_env_linux_centos.md)
  - [Arch Linux](../dev_setup/dev_env_linux_arch.md)
  - [Windows VM Toolchain](../dev_setup/dev_env_windows_vm.md) — 在 Windows 虚拟机中运行的 Ubuntu 。
  - [Windows Cygwin Toolchain](../dev_setup/dev_env_windows_cygwin.md) — 仅适用于 PX4 v1.12 的 Windows 设置
    - [Windows Cygwin 工具链维护](../dev_setup/dev_env_windows_cygwin_packager_setup.md)
- 集成开发环境
  - [Qt Creator IDE](../dev_setup/qtcreator.md)
- [仿真模拟器](../simulation/community_supported_simulators.md) — [Simulation-In-Hardware](../sim_sih/index.md), [FlightGear](../sim_flightgear/index.md), [JSBSim](../sim_jsbsim/index.md), [AirSim](../sim_airsim/index.md), [HITL](../simulation/hitl.md)
