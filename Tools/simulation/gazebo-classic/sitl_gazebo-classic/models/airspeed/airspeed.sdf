<?xml version="1.0" ?>
<sdf version="1.6">
  <model name="airspeed">
    <link name="link">
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.015</mass>
        <inertia>
          <ixx>1e-05</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>1e-05</iyy>
          <iyz>0</iyz>
          <izz>1e-05</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <pose>0 0 0 0 1.57 0</pose>
        <geometry>
          <cylinder>
            <radius>0.004</radius>
            <length>0.08</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Black</name>
          </script>
        </material>
      </visual>
      <sensor name="airspeed" type="gps">
        <pose>0 0 0 0 0 0</pose>
        <update_rate>5.0</update_rate>
        <always_on>true</always_on>
        <visualize>false</visualize>
        <plugin name='airspeed_plugin' filename='libgazebo_airspeed_plugin.so'>
          <robotNamespace/>
        </plugin>
      </sensor>
    </link>
  </model>
</sdf>
