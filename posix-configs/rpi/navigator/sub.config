#!/bin/sh
. px4-alias.sh

param select parameters.bson
param import

. ./etc/init.d/rc.board_defaults

# Submarine
set VEHICLE_TYPE uuv
param set MAV_TYPE 12
param set SYS_AUTOSTART 60002

# Disable GPS check and use barometer as reference
param set EKF2_GPS_CTRL 0
param set EKF2_GPS_CHECK 0

param set EKF2_BARO_CTRL 1
param set EKF2_HGT_REF 0

param set EKF2_RNG_CTRL 0
param set EKF2_OF_CTRL 0
param set EKF2_EV_CTRL 0

dataman start
load_mon start
battery_status start

. ./etc/init.d/rc.board_sensors

. ./etc/init.d/rc.board_extras

# Sensoring and core modules
sensors start
commander start
navigator start
ekf2 start
flight_mode_manager start
control_allocator start

# Sub specific modules
land_detector start rover
uuv_att_control start
uuv_pos_control start

mavlink start -x -u 14556 -r 1000000 -p
logger start -t -b 200

mavlink boot_complete
