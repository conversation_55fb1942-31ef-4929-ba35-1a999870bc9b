# Community Supported & Experimental Autopilots

:::tip
For more information about PX4 project autopilot board support levels see: [px4.io/autopilots/](https://px4.io/autopilots/).
:::

## Калібрування рівня горизонту

This category is for experimental autopilots (and autopilot "platforms") that are _not supported_ by either the PX4 project team or by a manufacturer.

- [BeagleBone Blue](../flight_controller/beaglebone_blue.md)
- [Raspberry Pi 2/3 Navio2](../flight_controller/raspberry_pi_navio2.md)
- [Raspberry Pi 2/3/4 PilotPi Shield](../flight_controller/raspberry_pi_pilotpi.md)

Плати цієї категорії повинні працювати принаймні з одним релізом PX4 для певного типу апарату.
Вони можуть бути несумісними з поточним випуском PX4, і проект не підтримує сумісність для майбутніх випусків.

## Експериментальні апарати

These are [complete vehicles](../complete_vehicles_mc/index.md) that have a fully integrated autopilot and other hardware (i.e. unlike the other autopilots listed, you can't use them in your own builds).
Вони перераховані на цій сторінці, тому що з точки зору програмного забезпечення PX4 вони є ще одним автопілотом.

- [Bitcraze Crazyflie 2.0](../complete_vehicles_mc/crazyflie2.md) ([Complete Vehicle](../complete_vehicles_mc/index.md))
- [Bitcraze Crazyflie 2.1](../complete_vehicles_mc/crazyflie21.md) ([Complete Vehicle](../complete_vehicles_mc/index.md))
