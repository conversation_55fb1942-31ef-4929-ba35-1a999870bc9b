﻿<?xml version="1.0" encoding="UTF-8"?>
<sdf version="1.9">
  <model name="zero_turn_mower">
    <pose>0 0 0.246 0 0 0</pose>
    <!-- pose>0 0 0.6 0 0 0</pose -->
    <link name="base_link">
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>220.0</mass>
        <inertia>
          <ixx>40</ixx>
          <ixy>0.0</ixy>
          <iyy>150</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>150</izz>
        </inertia>
      </inertial>
      <collision name="base_collision">
        <pose>0 0 0.2 0 0 0</pose>
        <geometry>
          <box>
            <size>1.8 0.65 0.55</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode/>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <visual name="base_visual_base">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <box>
            <size>1.81 0.66 0.1</size>
          </box>
        </geometry>
        <material>
          <ambient>0.9 0.36 0 1</ambient>
          <diffuse>0.9 0.36 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
      <visual name="base_visual">
        <pose>-0.5 0 0.2 0 0 0</pose>
        <geometry>
          <box>
            <size>0.8 0.65 0.55</size>
          </box>
        </geometry>
        <material>
          <ambient>1.0 0.4 0 1</ambient>
          <diffuse>1.0 0.4 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0.2 0.08 0 1</emissive>
        </material>
      </visual>
      <!-- Cutter board -->
      <visual name="base_visual_cutter_r">
        <pose>0.2 -0.5 -0.1 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.25</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>1.0 0.4 0 1</ambient>
          <diffuse>1.0 0.4 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0.2 0.08 0 1</emissive>
        </material>
      </visual>
      <visual name="base_visual_cutter_c">
        <pose>0.23 0 -0.1 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.25</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>1.0 0.4 0 1</ambient>
          <diffuse>1.0 0.4 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0.2 0.08 0 1</emissive>
        </material>
      </visual>
      <visual name="base_visual_cutter_l">
        <pose>0.2 0.5 -0.1 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.25</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>1.0 0.4 0 1</ambient>
          <diffuse>1.0 0.4 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0.2 0.08 0 1</emissive>
        </material>
      </visual>
      <!-- Casters -->
      <collision name="left_caster_collision">
        <pose>0.85 0.38 -0.1 0 0 0</pose>
        <geometry>
          <sphere>
            <radius>.1</radius>
          </sphere>
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>0</mu>
              <mu2>0</mu2>
              <slip1>1.0</slip1>
              <slip2>1.0</slip2>
            </ode>
          </friction>
        </surface>
      </collision>
      <visual name="left_caster_visual_base">
        <pose>0.85 0.38 -0.01 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.1</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>0.9 0.36 0 1</ambient>
          <diffuse>0.9 0.36 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
      <visual name="left_caster_visual">
        <pose>0.85 0.38 -0.1 0 0 0</pose>
        <geometry>
          <sphere>
            <radius>.1</radius>
          </sphere>
        </geometry>
        <material>
          <ambient>1.0 0.2 0.2 1</ambient>
          <diffuse>1.0 0.1 0.1 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
      <collision name="right_caster_collision">
        <pose>0.85 -0.38 -0.1 0 0 0</pose>
        <geometry>
          <sphere>
            <radius>.1</radius>
          </sphere>
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>0</mu>
              <mu2>0</mu2>
              <slip1>1.0</slip1>
              <slip2>1.0</slip2>
            </ode>
          </friction>
        </surface>
      </collision>
      <visual name="right_caster_visual_base">
        <pose>0.85 -0.38 -0.01 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.1</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>0.9 0.36 0 1</ambient>
          <diffuse>0.9 0.36 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
      <visual name="right_caster_visual">
        <pose>0.85 -0.38 -0.1 0 0 0</pose>
        <geometry>
          <sphere>
            <radius>.1</radius>
          </sphere>
        </geometry>
        <material>
          <ambient>0.2 1.0 0.2 1</ambient>
          <diffuse>0.1 1.0 0.1 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
      <sensor name="air_pressure_sensor" type="air_pressure">
        <always_on>1</always_on>
        <update_rate>50</update_rate>
        <air_pressure>
          <pressure>
            <noise type="gaussian">
              <mean>0</mean>
              <stddev>0.01</stddev>
            </noise>
          </pressure>
        </air_pressure>
      </sensor>
      <sensor name="magnetometer_sensor" type="magnetometer">
        <always_on>1</always_on>
        <update_rate>100</update_rate>
        <magnetometer>
          <!-- TODO: update to fix units and coordinate system when we move past Harmonic -->
          <!-- See https://github.com/gazebosim/gz-sim/pull/2460 -->
          <!-- 3mgauss RMS: NOTE: noise is in tesla but sensor reports data in gauss -->
          <!-- Noise modeled after IIS2MDC -->
          <x>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </z>
        </magnetometer>
      </sensor>
      <sensor name="navsat_sensor" type="navsat">
        <always_on>1</always_on>
        <update_rate>30</update_rate>
      </sensor>
      <sensor name="imu_sensor" type="imu">
        <always_on>1</always_on>
        <update_rate>250</update_rate>
        <imu>
          <angular_velocity>
            <x>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.0003394</stddev>
                <dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
              </noise>
            </x>
            <y>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.0003394</stddev>
                <dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
              </noise>
            </y>
            <z>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.0003394</stddev>
                <dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
              </noise>
            </z>
          </angular_velocity>
          <linear_acceleration>
            <x>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.004</stddev>
                <dynamic_bias_stddev>0.006</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
              </noise>
            </x>
            <y>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.004</stddev>
                <dynamic_bias_stddev>0.006</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
              </noise>
            </y>
            <z>
              <noise type="gaussian">
                <mean>0</mean>
                <stddev>0.004</stddev>
                <dynamic_bias_stddev>0.006</dynamic_bias_stddev>
                <dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
              </noise>
            </z>
          </linear_acceleration>
        </imu>
      </sensor>
    </link>
    <!-- Wheels -->
    <link name="left_wheel_link">
      <pose>-0.45 0.45 0.0215 0 1.57079632679 1.57079632679</pose>
      <inertial>
        <mass>8.0</mass>
        <!-- estimated from http://www.rzrforums.net/wheels-tires/1729-tire-wheel-weights-most-sizes.html -->
        <inertia>
          <ixx>0.5</ixx>
          <ixy>0.0</ixy>
          <iyy>0.5</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>1.0</izz>
        </inertia>
      </inertial>
      <collision name="left_wheel_link_collision">
        <geometry>
          <cylinder>
            <radius>0.22</radius>
            <length>0.22</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <ode>
              <mu>0.4</mu>
              <mu2>0.6</mu2>
            </ode>
          </friction>
          <bounce/>
          <contact>
            <ode>
              <min_depth>0.005</min_depth>
              <kp>1e8</kp>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name="left_wheel_link_visual">
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <mesh>
            <scale>2.8 2.0 2.8</scale>
            <uri>model://lawnmower/meshes/wheel_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name="left_wheel_marker_visual">
        <pose>0.17 0 0 0 0 0</pose>
        <geometry>
          <box>
            <size>.10 .01 .21</size>
          </box>
        </geometry>
        <material>
          <ambient>1.0 0.2 0.2 1</ambient>
          <diffuse>1.0 0.1 0.1 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
    </link>
    <joint name="left_wheel_joint" type="revolute">
      <child>left_wheel_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name="right_wheel_link">
      <pose>-0.45 -0.45 0.0215 0 1.57079632679 1.57079632679</pose>
      <inertial>
        <mass>8.0</mass>
        <!-- estimated from http://www.rzrforums.net/wheels-tires/1729-tire-wheel-weights-most-sizes.html -->
        <inertia>
          <ixx>0.5</ixx>
          <ixy>0.0</ixy>
          <iyy>0.5</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>1.0</izz>
        </inertia>
      </inertial>
      <collision name="right_wheel_link_collision">
        <geometry>
          <cylinder>
            <radius>0.22</radius>
            <length>0.22</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <ode>
              <mu>0.4</mu>
              <mu2>0.6</mu2>
            </ode>
          </friction>
          <bounce/>
          <contact>
            <ode>
              <min_depth>0.005</min_depth>
              <kp>1e8</kp>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name="right_wheel_link_visual">
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <mesh>
            <scale>2.8 2.0 2.8</scale>
            <uri>model://lawnmower/meshes/wheel_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name="right_wheel_marker_visual">
        <pose>0.17 0 0 0 0 0</pose>
        <geometry>
          <box>
            <size>.10 .01 .21</size>
          </box>
        </geometry>
        <material>
          <ambient>0.2 1.0 0.2 1</ambient>
          <diffuse>0.1 1.0 0.1 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
    </link>
    <joint name="right_wheel_joint" type="revolute">
      <child>right_wheel_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name="engine_visual_link">
      <pose>-0.9 0 0.5 0 0 0</pose>
      <visual name="engine_visual_link_visual">
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.1</radius>
            <length>0.08</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>0.3 0.3 0.2 1</ambient>
          <diffuse>1.0 1.0 0.1 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
    </link>
    <joint name="engine_visual_joint" type="revolute">
      <child>engine_visual_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name="cutter_blades_link">
      <pose>0.2 0 -0.1 0 0 0</pose>
      <visual name="cutter_blades_marker_visual">
        <pose>0.8 0 0 0 0 0</pose>
        <geometry>
          <box>
            <size>.20 .01 .15</size>
          </box>
        </geometry>
        <material>
          <ambient>1.0 0 0 1</ambient>
          <diffuse>1.0 0 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
    </link>
    <joint name="cutter_blades_joint" type="revolute">
      <child>cutter_blades_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name="alarm_visual_link">
      <pose>-0.45 0 0.65 0 0 0</pose>
      <visual name="alarm_visual_sphere_visual">
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <sphere>
            <radius>0.07</radius>
          </sphere>
        </geometry>
        <material>
          <ambient>0.7 0.7 0.0 1</ambient>
          <diffuse>0.7 0.7 0.0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
      <visual name="alarm_visual_belt_visual">
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.08</radius>
            <length>0.02</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>1.0 0 0 1</ambient>
          <diffuse>1.0 0 0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
      <visual name="alarm_visual_support_visual">
        <pose>0 0 -0.1 0 0 0</pose>
        <geometry>
          <box>
            <size>.05 .05 .2</size>
          </box>
        </geometry>
        <material>
          <ambient>0.7 0.7 0.0 1</ambient>
          <diffuse>0.7 0.7 0.0 1</diffuse>
          <specular>0 0 0 0</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
    </link>
    <joint name="alarm_visual_joint" type="revolute">
      <child>alarm_visual_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
      <joint_name>right_wheel_joint</joint_name>
      <sub_topic>command/motor_speed</sub_topic>
      <use_actuator_msg>true</use_actuator_msg>
      <actuator_number>0</actuator_number>
      <initial_velocity>0</initial_velocity>
      <use_force_commands>true</use_force_commands>
      <p_gain>5.0</p_gain>
      <i_gain>2.0</i_gain>
      <d_gain>0</d_gain>
      <i_max>1.0</i_max>
      <i_min>-1.0</i_min>
      <cmd_max>1000.0</cmd_max>
      <cmd_min>-1000.0</cmd_min>
      <cmd_offset>0.0</cmd_offset>
    </plugin>
    <plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
      <joint_name>left_wheel_joint</joint_name>
      <sub_topic>command/motor_speed</sub_topic>
      <use_actuator_msg>true</use_actuator_msg>
      <actuator_number>1</actuator_number>
      <initial_velocity>0</initial_velocity>
      <use_force_commands>true</use_force_commands>
      <p_gain>5.0</p_gain>
      <i_gain>2.0</i_gain>
      <d_gain>0</d_gain>
      <i_max>1.0</i_max>
      <i_min>-1.0</i_min>
      <cmd_max>1000.0</cmd_max>
      <cmd_min>-1000.0</cmd_min>
      <cmd_offset>0.0</cmd_offset>
    </plugin>
    <!-- Note: SIM_GZ_SV_FAIL1 defines servo_0 etc. -->
    <plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
      <joint_name>cutter_blades_joint</joint_name>
      <sub_topic>servo_0</sub_topic>
      <!-- p_gain>1.0</p_gain>
      <cmd_max>2000.0</cmd_max>
      <cmd_min>1000.0</cmd_min>
      <cmd_offset>0.0</cmd_offset -->
    </plugin>
    <plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
      <joint_name>engine_visual_joint</joint_name>
      <sub_topic>servo_1</sub_topic>
      <p_gain>10.0</p_gain>
      <!-- use_actuator_msg>false</use_actuator_msg>
      <initial_velocity>0</initial_velocity>
      <cmd_max>2000.0</cmd_max>
      <cmd_min>1000.0</cmd_min>
      <cmd_offset>0.0</cmd_offset -->
    </plugin>
    <plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
      <joint_name>alarm_visual_joint</joint_name>
      <sub_topic>servo_4</sub_topic>
      <p_gain>1.0</p_gain>
      <cmd_max>2000.0</cmd_max>
      <cmd_min>1000.0</cmd_min>
      <cmd_offset>0.0</cmd_offset>
    </plugin>
    <plugin filename="gz-sim-joint-state-publisher-system" name="gz::sim::systems::JointStatePublisher">
      <joint_name>left_wheel_joint</joint_name>
      <joint_name>right_wheel_joint</joint_name>
    </plugin>
    <static>0</static>
  </model>
</sdf>
