# Камери

Камери є важливими для багатьох [використання вантажів](../payloads/use_cases.md), включаючи картографування та обстеження, нагляд, пошук та рятування, визначення стану та виявлення шкідників у посівах і.т.д.
Вони зазвичай монтувалися на [шарнірну підвіску](../advanced/gimbal_control.md), яка може забезпечувати стабілізацію камери, відстеження об'єкту та рух незалежно від транспортного засобу, на якому вони розміщені.

## Типи камер

PX4 інтегрується з трьома типами камер:

- [Камери MAVLink](../camera/mavlink_v2_camera.md), які підтримують [Протокол Камери v2](https://mavlink.io/en/services/camera.html) (**РЕКОМЕНДОВАНО**).
- [Прості камери MAVLink](../camera/mavlink_v1_camera.md), які підтримують старший [Протокол камери v1](https://mavlink.io/en/services/camera.html).
- [Камери, підключені до виходів контролера польоту](../camera/fc_connected_camera.md), які керуються з використанням [протоколу камери v1](https://mavlink.io/en/services/camera.html).

[Камери MAVLink](../camera/mavlink_v2_camera.md) рекомендовані, оскільки вони забезпечують широкий доступ до функцій камери, використовуючи простий та послідовний набір команд/повідомлень.
Якщо камера не підтримує цей протокол, можна використовувати [менеджер камери](../camera/mavlink_v2_camera.md#camera-managers), який працює на супутниковому комп'ютері, щоб забезпечити взаємодію між MAVLink та власним протоколом камери.

## Дивіться також

- [Gimbal (кріплення для камери)](../advanced/gimbal_control.md)
- [Інтеграція/Архітектура камери](../camera/camera_architecture.md) (Розробники PX4)