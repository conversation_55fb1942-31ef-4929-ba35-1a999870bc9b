# PX4-Autopilot v1.14 Release Notes

## Read Before Upgrading

The v1.14 release includes a few breaking changes for users upgrading from previous versions, in particular we are moving away from using mixer files to define the vehicle geometry, motor mappings and actuators.

Additionally, we deprecated the Fast-RTPS interface used by ROS 2 in favor of a much cleaner solution that doesn't require a custom build target, and goes away with the additional message generation step.

Please continue reading for [upgrade instructions](#upgrade-guide).

## Major Changes

- Dynamic Control Allocation
- Improved Preflight Failure Check Reporting
- Failsafe Simplification and Simulation
- Default Simulation is now New Gazebo
- Improved ROS 2 Interface thanks to uXRCE-DDS

### Dynamic Control Allocation

We are very excited to enable the new [dynamic control allocation](../concept/control_allocation.md) by default; it allows users to define vehicle configurations at runtime without needing a mixer file, thanks to the new [vehicle setup dashboard](../config/actuators.md) in QGroundControl.

:::info
The new actuator configuration UI is available on QGroundControl 4.2.0 or newer.
:::

### Improved Preflight Failure Check Reporting (QGC Arming Check Report)

PX4 v1.14 adds much improved preflight failure _reporting_ through the [events interface](../concept/events_interface.md).
If the vehicle won't arm, you can more easily find out why in the [QGC Arming Check Report](https://docs.qgroundcontrol.com/master/en/qgc-user-guide/fly_view/fly_view.html#arm).
No more wondering if it's a problem with the safety switch, a poor calibration, or something in the internals of the estimator!

:::info
The QGC Arming Check Report UI is available in the **QGC Daily Build** (QGC v4.2.0 and later)
:::

As part of this change, it is now possible to switch to any mode when disarmed (previously you could not switch to a mode that required GPS if you didn't have a good position estimate).
PX4 will only allow you to arm when the conditions for the current mode are met, and will report failure checks that are not relevant to the current mode as warnings.

For more information see: [QGroundControl Flight-Readiness Status](../flying/pre_flight_checks.md).

### Failsafe Simplification

[Safety Failsafe](../config/safety.md) handling has been simplified, in particular with respect to what happens if a failsafe is triggered when another failsafe is already in progress.

- There is now a hold delay before the action is performed, giving the user time to override the failsafe if needed.
- If multiple failsafes are triggered, the more severe _action_ is taken.
  For example if both RC and GPS are lost, and manual control loss is set to Return mode and GCS link loss to Land, Land is executed.

The new [Failsafe State Machine Simulation](../config/safety_simulation.md) allows you to test failsafe behaviour under all possible configurations and conditions.

### New Gazebo

Given [the recent changes](https://discourse.ros.org/t/a-new-era-for-gazebo-cross-post/25012) by the Open Robotics simulation team, we are introducing name changes for our gazebo simulations, mirroring Open Robotics naming scheme, starting with v1.14:

- [Ignition Gazebo](https://docs.px4.io/v1.13/en/simulation/ignition_gazebo.html) to [Gazebo](../sim_gazebo_gz/index.md)
- [Gazebo](https://docs.px4.io/v1.13/en/simulation/gazebo.html) to [Gazebo Classic](../sim_gazebo_classic/index.md).

Most importantly this affects the PX4 build target names as well:

- Gazebo targets are prefixed with `gz_` (e.g. `make px4_sitl gz_x500`).
- Gazebo Classic `make` targets are now prefixed with `gazebo-classic_` (e.g., `make px4_sitl gazebo-classic_cloudship`).

### Improved ROS 2 Interface via uXRCE-DDS

We updated the ROS 2 interface, replacing [Fast-RTPS](https://docs.px4.io/v1.13/en/middleware/micrortps.html) with [uXRCE-DDS](../ros2/user_guide.md), resulting in an improved experience across the board.
The change also avoids the need for `_rtps` build targets, enabling the interface on even more targets by default.

## Upgrade Guide

For users upgrading from previous versions, please take a moment to review the following:

1. The actuator changes require you to verify vehicle geometry and motors/servos mappings match your vehicle. In QGC, find the [Actuator Configuration Dashboard](../config/actuators.md), and make sure to confirm the airframe geometry matches actuals from your vehicle, as well as update motor and ensure motors and servos are mapped to outputs as they are wired to the frame and with the correct ESC type specified. Note: take advantage of the sliders in the UI. They can be used to confirm motor wiring.

  We highly recommend running an [ESC Calibration](../advanced_config/esc_calibration.md) if using PWM ESC motors and then setting appropriate disarmed minimum and maximum values for the motors (in the actuator UI).

  The calibration is critical if you are using a custom mixer file or the airframe you assigned in an earlier version is not present in PX4 v1.14.

  However, an ESC Calibration is still recommended **even if** you are using an airframe that precisely matches a specific vehicle in the [Airframe Reference](../airframes/airframe_reference.md) (such as the [Holybro X500 V2](../airframes/airframe_reference.md#copter_quadrotor_x_holybro_x500_v2)) as your wiring and ESCs calibration may not match the defaults.

2. Default disarmed PWM was changed from 900us to 1000us.
  Verify if you previously used the default PWM disarmed values and if the changes impact your setup.
  For details, you can find related information in the [step 7 of ESC calibration](../advanced_config/esc_calibration.md#steps) document.

3. Fast-RTPS users must port their code to the new uXRCE-DDS interface.
  Application code should only require minor modifications. These include (minimally):

Modifying topic names to match the new naming pattern, which changed from `fmu/<topic_name>/out` to `fmu/out/<topic_name>`, and [Adusting the QoS settings](../ros2/user_guide.md#ros-2-subscriber-qos-settings).

For more information see [Fast-RTPS to uXRCE-DDS Migration Guidelines](../middleware/uxrce_dds.md#fast-rtps-to-uxrce-dds-migration-guidelines)

## Other changes

### Hardware Support

- Cubepilot Cube Orange+ - [PX4-Autopilot#20304](https://github.com/PX4/PX4-Autopilot/pull/20304)
- Unicore GPS: Support for Holybro Unicore UM982 GPS - [PX4-Autopilot#21214](https://github.com/PX4/PX4-Autopilot/pull/21214))
- VOXL2 Improved support - [PX4-Autopilot#21426](https://github.com/PX4/PX4-Autopilot/pull/21426)
- Cubepilot Cube Black detection: Fixed Cube Black vs. Pixhawk 2 detection with CAN1 connected - [PX4-Autopilot#21342](https://github.com/PX4/PX4-Autopilot/pull/21342)

### 공통

- Failsafe state machine rewrite and [web simulation](../config/safety_simulation.md)
- Improved preflight failure check reporting (requires QGC [v4.2.0](https://github.com/mavlink/qgroundcontrol/releases/tag/v4.2.0) or later): [PX4-Autopilot#20030](https://github.com/PX4/PX4-Autopilot/pull/20030) and [qgroundcontrol#10362](https://github.com/mavlink/qgroundcontrol/pull/10362)
- [Package delivery in mission](../advanced/package_delivery.md): For package delivery applications, inital support for payload delivery in mission for gripper actuator was added
- Manual control setpoint message redefinition: `manual_control_setpoint.x`, `y`, `z`, `w` -> `roll`, `pitch`, `yaw`, `throttle`; `throttle scale [0,1] -> [-1,1]` - [PX4-Autopilot#15949](https://github.com/PX4/PX4-Autopilot/pull/15949)
- Default motor PWM configuration - [PX4-Autopilot#21800](https://github.com/PX4/PX4-Autopilot/pull/21800)
- Fix PWM/Oneshot calibration - [PX4-Autopilot#21726](https://github.com/PX4/PX4-Autopilot/pull/21726)

### 제어

- Land and take off again in mission: Landing doesn't disarm if the mission is not done - [PX4-Autopilot#19659](https://github.com/PX4/PX4-Autopilot/pull/19659)
- Separate trajectory setpoint message: Message is now separate from vehicle_local_position_setpoint message. - [PX4-Autopilot#19622](https://github.com/PX4/PX4-Autopilot/pull/19622)
- Bugfix: "Invalid setpoint" message on takeoff or mode switch: The message popped up as false positive depending on timing - [PX4-Autopilot#20581](https://github.com/PX4/PX4-Autopilot/pull/20581)
- Bugfix: Step switching Altitude to Position: Switching from Altitude to Position with speed produced a jerk step - [PX4-Autopilot#20905](https://github.com/PX4/PX4-Autopilot/pull/20905)

### Estimation

- [Multiple height source fusion](../advanced_config/tuning_the_ecl_ekf.md#height)
- EKF2 start without magnetometer and barometer data: Until now, a mag and a baro was mandatory to run EKF2 - [PX4-Autopilot#20021](https://github.com/PX4/PX4-Autopilot/pull/20021)
- Multirotor wind estimate improvements: More accurate bluff body drag model - [PX4-Autopilot#20848](https://github.com/PX4/PX4-Autopilot/pull/20848)

### 센서

- [DroneCAN sensors now need to be individually enabled via `UAVCAN_SUB_*` parameters](../dronecan/index.md#dronecan-subscriptions-publications) - [PX4-Autopilot#18471](https://github.com/PX4/PX4-Autopilot/pull/18471)
- Battery estimation defaults, reporting improvements: Defaults: current based load compensation, higher empty cell voltage, shorter action delay - [PX4-Autopilot#19429](https://github.com/PX4/PX4-Autopilot/pull/19429), [PX4-Autopilot#19700](https://github.com/PX4/PX4-Autopilot/pull/19700), [PX4-Autopilot#19594](https://github.com/PX4/PX4-Autopilot/pull/19594), https://github.com/PX4/PX4-Autopilot/pull/19679
- SF45 rotation Lidar serial driver - [PX4-Autopilot#19891](https://github.com/PX4/PX4-Autopilot/pull/19891)

### 시뮬레이션

- [Gazebo] Ignition Fuel for PX4: PX4 now has a repository for gazebo models in ignition fuel - https://app.gazebosim.org/PX4
- [Gazebo] Support for Gazebo garden with native ignition transport support: Added native ignition transport support for direct interaction with Gazebo and the simulation. Supported vehicles in Gazebo are X500 (Multicopter), Plane, Standard VTOL - [PX4-Autopilot#20319](https://github.com/PX4/PX4-Autopilot/pull/20319)
- [Gazebo-classic] Serializer for Gazebo sniffer: Added a sniffer for serializing gazebo related information - [PX4-SITL_gazebo-classic#937](https://github.com/PX4/PX4-SITL_gazebo-classic/pull/937)
- [Gazebo-classic] Motor failure plugin as a ros2 plugin: Motor failure plugin has been updated to be a ros2 plugin - [PX4-SITL_gazebo-classic#862](https://github.com/PX4/PX4-SITL_gazebo-classic/pull/862)
- [Gazebo-classic] Addition of Omnicopter model: A fully actuated omnidirectional vehicle model has been added to Gazebo SITL - https://github.com/PX4/PX4-SITL_gazebo-classic/pull/866
- [Gazebo-classic] Addition of Advanced liftdrag plugin: Advanced liftdrag plugin that models nonlinear aerodynamics based on AVL - [PX4-SITL_gazebo-classic#901](https://github.com/PX4/PX4-SITL_gazebo-classic/pull/901)
- [Gazebo-classic] Addition of Safe landing world: Addition of safe landing world, for testing safe landing - [PX4-SITL_gazebo-classic#93](https://github.com/PX4/PX4-SITL_gazebo-classic/pull/93)
- [Gazebo-classic] Depricated Ubuntu Bionic reated tests: Removed testing due to EOL of Ubuntu Bionic - [PX4-SITL_gazebo-classic#974](https://github.com/PX4/PX4-SITL_gazebo-classic/pull/974)
- [SIH] Standalone sensor simulations in tree: Ability to simulate sensors in tree that was part of SIH is now a stand alone sensor module. Sensors include magnetometer, GPS, Barometer, Airspeed - [PX4-Autopilot#20137](https://github.com/PX4/PX4-Autopilot/pull/20137), https://github.com/PX4/PX4-Autopilot/tree/main/src/modules/simulation/sensor_airspeed_sim
- [SIH] Failure injection for battery simulation - https://github.com/PX4/PX4-Autopilot/commit/ebc1d7544e8146788c9e7cf5e8b64f60199240e4

### MAVLink

- MAVLink forwarding over USB: Forward MAVLink traffic to/from USB by default - [PX4-Autopilot#20745](https://github.com/PX4/PX4-Autopilot/pull/20745)

### Multi-Rotor

- Improved Follow-Me flight mode: Follow-me mode was improved for better user experience - [PX4-Autopilot#18026](https://github.com/PX4/PX4-Autopilot/pull/18026) | [PX4 Dev Summit presentation](https://www.youtube.com/watch?v=rYYso87cmxA)
- Improved Helicopter Support: [Helicopter Configuration](../config_heli/index.md)
- Direction-based speed constraints for multirotors: Can define a different forward, backward and lateral speed - [PX4-Autopilot#19257](https://github.com/PX4/PX4-Autopilot/pull/19257)
- Allow yawing in hold mode: Let the pilot yaw the drone while staying in hold mode - [PX4-Autopilot#21399](https://github.com/PX4/PX4-Autopilot/pull/21399)

### 수직이착륙기(VTOL)

- Quad-chute: enable to reset failure state. Reset failure state once operator trigger transition to FW again - [PX4-Autopilot#20913](https://github.com/PX4/PX4-Autopilot/pull/20913)
- Simplify Tailsitter transitions to FW in Stabilized: Adapt the tilt threshold automatically on configured max tilt setting - [PX4-Autopilot#21582](https://github.com/PX4/PX4-Autopilot/pull/21582)
- Refactor uncommanded descend Quad-Chute: Enables to set altitude error threshold to trigger a Quad-chute - [PX4-Autopilot#21598](https://github.com/PX4/PX4-Autopilot/pull/21598)
- Enable control-surface-less Quad-tailsitters: Enables to use differential thrust to control full body attitude in FW mode on tailsitters - [PX4-Autopilot#20511](https://github.com/PX4/PX4-Autopilot/pull/20511)
- Skip VTOL_Takeoff mission item if already in-air - [PX4-Autopilot#19985](https://github.com/PX4/PX4-Autopilot/pull/19985)

### Fixed-wing

- Flaps/Spoiler refactor - [PX4-Autopilot#19329](https://github.com/PX4/PX4-Autopilot/pull/19329)
- FW Position control: switch from L1 to NPFG guidance - [PX4-Autopilot#19603](https://github.com/PX4/PX4-Autopilot/pull/19603) | [PX4 Dev Summit presentation video](https://www.youtube.com/watch?v=LY6hYBCdy-0)
- Automatically apply learned airspeed scale in-air - [PX4-Autopilot#19787](https://github.com/PX4/PX4-Autopilot/pull/19787)
- Takeoff refactor/enhancements - [PX4-Autopilot#19869](https://github.com/PX4/PX4-Autopilot/pull/19869)
- FW landing refactor/enhancements - [PX4-Autopilot#19871](https://github.com/PX4/PX4-Autopilot/pull/19871)
- Scale minimum airspeed with wind strength FW Position control: add option to scale min airspeed with wind magnitude - [PX4-Autopilot#20259](https://github.com/PX4/PX4-Autopilot/pull/20259)
- Improve FW hand-launch takeoff - [PX4-Autopilot#20557](https://github.com/PX4/PX4-Autopilot/pull/20557)
- Improve accelerated stall protection - [PX4-Autopilot#20636](https://github.com/PX4/PX4-Autopilot/pull/20636)
- Add option to disable manual yaw inputs - [PX4-Autopilot#20647](https://github.com/PX4/PX4-Autopilot/pull/20647)
- Disable airspeed rate input into TECS airspeed rate estimation has shown to be unreliable, so we decided to disable it for now - [PX4-Autopilot#21317](https://github.com/PX4/PX4-Autopilot/pull/21317)
- Support flying at different airspeeds Add airspeed to trim throttle mapping in TECS to support flying at different airspeeds - [PX4-Autopilot#21345](https://github.com/PX4/PX4-Autopilot/pull/21345)
- FWPositionControl: navigateWaypoints: fix logic if already past waypoint - [PX4-Autopilot#21642](https://github.com/PX4/PX4-Autopilot/pull/21642)
