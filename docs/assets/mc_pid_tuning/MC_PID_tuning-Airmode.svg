<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="807px" height="624px" version="1.1" content="&lt;mxfile userAgent=&quot;Mozilla/5.0 (X11; Fedora; Linux x86_64; rv:60.0) Gecko/20100101 Firefox/60.0&quot; version=&quot;8.8.1&quot; editor=&quot;www.draw.io&quot; type=&quot;google&quot;&gt;&lt;diagram id=&quot;917fb50d-1c87-2e6e-62c8-8f76e3f3c1b0&quot; name=&quot;Airmode&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;b0f054c4-85e4-c33c-17c7-2b8b56cee276&quot; name=&quot;TPA&quot;&gt;7Vpbc6M2FP41ftwOEiDDoy9J24edyWwy0/ZRAdkwwcgVcmz31/cAEjfhOPFCNvHEmYnhA46k852rzMRebA6/C7qNvvOQJRNshYeJvZxg7CEX/ufAsQQcREpgLeKwhFAN3Mf/MQVaCt3FIctaN0rOExlv22DA05QFsoVRIfi+fduKJ+1Rt3TNDOA+oImJ/hWHMlLLwtMa/4PF60iPjIhfXtlQfbNaSRbRkO8bkH0zsReCc1kebQ4LluS603opn7s9cbWamGCpfM0D1PGnHrEeMSGUePSbUwp4pslOrVXNUx714lkaznIdwlmQ0CyLg4k9j+QmAQDBIVy/jRN9Vj7OQkOh9QxRtW6wF8Y3TIoj3LKvNesqbUUNpWpMsITK+LktniqC15W4aoQ7HsPA2FK2iOzpb8oalTFi32oLyfhOBEw919RlR5TjnBUlqVgzaYiCg8bSa6gg63XEuUMQB6P/DScWLEOd/vNGGsERiyX2T5JcCdOmKOS8G9PEYPruz2UFdEmX7CDbNGdS8Ce24AkXgKQ8hTvnK/DYDkSTeJ3mtgIMM8Dnz0zIGILgTF3YxGGYDzPfR7Fk91sa5GPuIeIDJvguDVm+BKsyn1wAO7xsQKZ16Acs0ta4TgYN6yGOaT3YOm0oLVbeQMHUoABZMIx7vdr3vH5zbyjf6XHdMZTvGcq/btX7Tjtoeb9O9Qi/KcsofYY0iwqFoDYfK55KrfoJtsnCu5nfGjzBFav4dMgCfFV8PkWR0XEf99ISQ7N6StB4aQfZl3A/Ct3lE7ryxp/AAGx0hrdLDQBb72cAr+gNoJ3Z5ofBTiTHuaDBU14K9sbJhmXUIdMM08C7T6b2jJj2wFDosqlpWc4y/yvkSiCN5yEbTf1horGD3X4mG4ak29s+Qxo0HJtF/w8qP2oN2GHJW5KFvxiIFKfjXsQ1SLF7SBklR5r1ydvi5AvxsK3Byi8+evRDtj1M9EPOGUEjRj//i9YrpFVHgC9ar4tW1EMrSYosiFrskn93+V5rwda3rNhpnsENyNkeCq70dTha598PdzMt6VH8lCQtBtaWz0mDg+XuDJJxnK4BcOuzBw6JeZmbfX/K5pCHV0lh6xGkdpaeS+Mtc1c79VApjtH5emZSR7ojbfqBPUJW12Hhq/v5le3v2TZqxIhitr8PETQYMvmo9XZr26XpnfgFLx7Aayv/O2p37OmQrB5rG6UaN1izromuMBYsUC0uOFGu3kH22nE7c2O/p596Jwb7Am+ZOMP4WadN1EimDfiaqB6AVgfhU7+ANYjFPcSOkVK/Gipzc8/kx70sU/aI8jqiRuyVL2qqfvpnAhJ47HFlVk4hZd4q0K6mpmB9ykrKKIC6jnlpJfWObyogMyfPBaNP22qszx2xie3Z86FqKf8E360eaJhMDKf1e0clz/XLW/bN/w==&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g transform="translate(0.5,0.5)"><rect x="527" y="130" width="20" height="100" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><path d="M 487 170 L 797 170" fill="none" stroke="#b85450" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><rect x="527.5" y="470" width="20" height="100" fill="none" stroke="#82b366" stroke-dasharray="3 3" pointer-events="none"/><rect x="199.5" y="350" width="20" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><rect x="309.5" y="390" width="20" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><rect x="89.5" y="290" width="20" height="100" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><path d="M 49.5 390 L 359.5 390" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 49.5 390 L 49.82 236.03" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 49.83 230.78 L 53.32 237.79 L 49.82 236.03 L 46.32 237.78 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(32.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 35px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Thrust</div></div></foreignObject><text x="17" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Thrust</text></switch></g><g transform="translate(3.5,243.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="32" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 33px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">100%</div></div></foreignObject><text x="16" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">100%</text></switch></g><g transform="translate(20.5,383.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="18" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 19px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">0%</div></div></foreignObject><text x="9" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">0%</text></switch></g><g transform="translate(83.5,416.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="31" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(106, 145, 83); line-height: 1.2; vertical-align: top; width: 32px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Left</div><div>Motor</div></div></div></foreignObject><text x="16" y="20" fill="#6A9153" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(303.5,416.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="31" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(106, 145, 83); line-height: 1.2; vertical-align: top; width: 32px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Right</div><div>Motor</div></div></div></foreignObject><text x="16" y="20" fill="#6A9153" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(173.5,416.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="71" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(89, 118, 158); line-height: 1.2; vertical-align: top; width: 72px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Commanded</div><div>Thrust<br /></div></div></div></foreignObject><text x="36" y="20" fill="#59769E" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 49.5 350 L 359.5 350" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 99.5 290 L 319.5 410" fill="none" stroke="#b3b3b3" stroke-miterlimit="10" pointer-events="none"/><path d="M 349.5 350 L 344.5 350 Q 339.5 350 339.5 360 L 339.5 370 Q 339.5 380 334.5 380 L 332 380 Q 329.5 380 334.5 380 L 337 380 Q 339.5 380 339.5 390 L 339.5 400 Q 339.5 410 344.5 410 L 349.5 410" fill="none" stroke="#9673a6" stroke-miterlimit="10" transform="rotate(179,339.5,380)" pointer-events="none"/><g transform="translate(354.5,373.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="8" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(150, 115, 166); line-height: 1.2; vertical-align: top; width: 9px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">-r</div></div></foreignObject><text x="4" y="12" fill="#9673A6" text-anchor="middle" font-size="12px" font-family="Helvetica">-r</text></switch></g><path d="M 89.5 290 L 84.5 290 Q 79.5 290 79.5 300 L 79.5 310 Q 79.5 320 74.5 320 L 72 320 Q 69.5 320 74.5 320 L 77 320 Q 79.5 320 79.5 330 L 79.5 340 Q 79.5 350 84.5 350 L 89.5 350" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(53.5,313.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="11" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(150, 115, 166); line-height: 1.2; vertical-align: top; width: 12px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+r</div></div></foreignObject><text x="6" y="12" fill="#9673A6" text-anchor="middle" font-size="12px" font-family="Helvetica">+r</text></switch></g><path d="M 45 250 L 55 250" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 45 250 L 55 250" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 45 250 L 55 250" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="637" y="190" width="20" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><path d="M 487 230 L 797 230" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 487 230 L 487.32 76.03" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 487.33 70.78 L 490.82 77.79 L 487.32 76.03 L 483.82 77.78 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(470.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 35px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Thrust</div></div></foreignObject><text x="17" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Thrust</text></switch></g><g transform="translate(441.5,83.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="32" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 33px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">100%</div></div></foreignObject><text x="16" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">100%</text></switch></g><g transform="translate(457.5,223.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="18" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 19px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">0%</div></div></foreignObject><text x="9" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">0%</text></switch></g><g transform="translate(521.5,255.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="31" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(106, 145, 83); line-height: 1.2; vertical-align: top; width: 32px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Left</div><div>Motor</div></div></div></foreignObject><text x="16" y="20" fill="#6A9153" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(741.5,255.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="31" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(106, 145, 83); line-height: 1.2; vertical-align: top; width: 32px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Right</div><div>Motor</div></div></div></foreignObject><text x="16" y="20" fill="#6A9153" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(611.5,255.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="71" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(89, 118, 158); line-height: 1.2; vertical-align: top; width: 72px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Commanded</div><div>Thrust<br /></div></div></div></foreignObject><text x="36" y="20" fill="#59769E" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 487 190 L 797 190" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 786.5 170 L 781.5 170 Q 776.5 170 776.5 180 L 776.5 190 Q 776.5 200 771.5 200 L 769 200 Q 766.5 200 771.5 200 L 774 200 Q 776.5 200 776.5 210 L 776.5 220 Q 776.5 230 781.5 230 L 786.5 230" fill="none" stroke="#9673a6" stroke-miterlimit="10" transform="rotate(179,776.5,200)" pointer-events="none"/><g transform="translate(791.5,193.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="8" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(150, 115, 166); line-height: 1.2; vertical-align: top; width: 9px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">-r</div></div></foreignObject><text x="4" y="12" fill="#9673A6" text-anchor="middle" font-size="12px" font-family="Helvetica">-r</text></switch></g><path d="M 527.5 110 L 522.5 110 Q 517.5 110 517.5 120 L 517.5 130 Q 517.5 140 512.5 140 L 510 140 Q 507.5 140 512.5 140 L 515 140 Q 517.5 140 517.5 150 L 517.5 160 Q 517.5 170 522.5 170 L 527.5 170" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(490.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="11" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(150, 115, 166); line-height: 1.2; vertical-align: top; width: 12px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+r</div></div></foreignObject><text x="6" y="12" fill="#9673A6" text-anchor="middle" font-size="12px" font-family="Helvetica">+r</text></switch></g><path d="M 482.5 90 L 492.5 90" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 482.5 90 L 492.5 90" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 482.5 90 L 492.5 90" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="637" y="530" width="20" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><rect x="527.5" y="490" width="20" height="80" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><path d="M 487 570 L 797 570" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 487 570 L 487.32 416.03" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 487.33 410.78 L 490.82 417.79 L 487.32 416.03 L 483.82 417.78 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(470.5,393.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 35px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Thrust</div></div></foreignObject><text x="17" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Thrust</text></switch></g><g transform="translate(441.5,423.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="32" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 33px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">100%</div></div></foreignObject><text x="16" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">100%</text></switch></g><g transform="translate(457.5,563.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="18" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 19px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">0%</div></div></foreignObject><text x="9" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">0%</text></switch></g><g transform="translate(521.5,596.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="31" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(106, 145, 83); line-height: 1.2; vertical-align: top; width: 32px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Left</div><div>Motor</div></div></div></foreignObject><text x="16" y="20" fill="#6A9153" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(741.5,596.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="31" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(106, 145, 83); line-height: 1.2; vertical-align: top; width: 32px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Right</div><div>Motor</div></div></div></foreignObject><text x="16" y="20" fill="#6A9153" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(611.5,596.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="71" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(89, 118, 158); line-height: 1.2; vertical-align: top; width: 72px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>Commanded</div><div>Thrust<br /></div></div></div></foreignObject><text x="36" y="20" fill="#59769E" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 487 530 L 797 530" fill="none" stroke="#6c8ebf" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 537.33 489.67 L 757.5 570" fill="none" stroke="#b3b3b3" stroke-miterlimit="10" pointer-events="none"/><path d="M 786.5 530 L 781.5 530 Q 776.5 530 776.5 540 L 776.5 545 Q 776.5 550 771.5 550 L 769 550 Q 766.5 550 771.5 550 L 774 550 Q 776.5 550 776.5 560 L 776.5 565 Q 776.5 570 781.5 570 L 786.5 570" fill="none" stroke="#9673a6" stroke-miterlimit="10" transform="rotate(179,776.5,550)" pointer-events="none"/><g transform="translate(790.5,543.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="10" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(150, 115, 166); line-height: 1.2; vertical-align: top; width: 11px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">-r'</div></div></foreignObject><text x="5" y="12" fill="#9673A6" text-anchor="middle" font-size="12px" font-family="Helvetica">-r'</text></switch></g><path d="M 527.5 490 L 522.5 490 Q 517.5 490 517.5 500 L 517.5 505 Q 517.5 510 512.5 510 L 510 510 Q 507.5 510 512.5 510 L 515 510 Q 517.5 510 517.5 520 L 517.5 525 Q 517.5 530 522.5 530 L 527.5 530" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(489.5,503.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="13" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(150, 115, 166); line-height: 1.2; vertical-align: top; width: 14px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+r'</div></div></foreignObject><text x="7" y="12" fill="#9673A6" text-anchor="middle" font-size="12px" font-family="Helvetica">+r'</text></switch></g><path d="M 482.5 430 L 492.5 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 482.5 430 L 492.5 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 482.5 430 L 492.5 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(434.5,-0.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="132" height="39" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 70px; max-width: 244px; width: 133px; white-space: normal; overflow-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><h1 style="font-size: 16px">Airmode Enabled<br style="font-size: 16px" /></h1></div></div></foreignObject><text x="66" y="28" fill="#000000" text-anchor="middle" font-size="16px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(434.5,339.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="136" height="39" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 50px; max-width: 237px; width: 137px; white-space: normal; overflow-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><h1 style="font-size: 16px">Airmode Disabled<br style="font-size: 16px" /></h1></div></div></foreignObject><text x="68" y="28" fill="#000000" text-anchor="middle" font-size="16px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="637" y="170" width="20" height="20" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><rect x="527" y="110" width="20" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><path d="M 537.33 109.67 L 757.17 230" fill="none" stroke="#b3b3b3" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(14.5,159.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="128" height="39" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 70px; max-width: 244px; width: 129px; white-space: normal; overflow-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><h1 style="font-size: 16px">Saturated Mixing<br style="font-size: 16px" /></h1></div></div></foreignObject><text x="64" y="28" fill="#000000" text-anchor="middle" font-size="16px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 367.32 473.89 L 374.39 466.82 L 410.25 502.68 L 417.67 495.25 L 420.15 519.65 L 395.75 517.17 L 403.18 509.75 Z" fill="#b3b3b3" stroke="none" pointer-events="none"/><path d="M 372.39 233.18 L 365.32 226.11 L 401.18 190.25 L 393.75 182.83 L 418.15 180.35 L 415.67 204.75 L 408.25 197.32 Z" fill="#b3b3b3" stroke="none" pointer-events="none"/><path d="M 694 170 L 692 170 Q 690 170 690 175 L 690 177.5 Q 690 180 688 180 L 687 180 Q 686 180 688 180 L 689 180 Q 690 180 690 185 L 690 187.5 Q 690 190 692 190 L 694 190" fill="none" stroke="#b85450" stroke-miterlimit="10" transform="rotate(179,690,180)" pointer-events="none"/><g transform="translate(699.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="7" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(184, 84, 80); line-height: 1.2; vertical-align: top; width: 8px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">b</div></div></foreignObject><text x="4" y="12" fill="#B85450" text-anchor="middle" font-size="12px" font-family="Helvetica">b</text></switch></g></g></svg>