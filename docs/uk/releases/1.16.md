# PX4-Autopilot v1.16.0 Release Notes

<Badge type="info" text="Candidate Release" />

<script setup>
import { useData } from 'vitepress'
const { site } = useData();
</script>

<div v-if="site.title !== 'PX4 Guide (main)'">
  <div class="custom-block danger">
    <p class="custom-block-title">This page is on a release branch, and hence possibly out of date. <a href="https://docs.px4.io/main/en/releases/main.html">See the latest version</a>.</p>
  </div>
</div>

This document covers all changes in PX4 v1.16.0 since the previous stable release ([PX4 v1.15.0](../releases/1.15.md)).

:::info
These notes include only changes merged in 2023 and later — commits before 2023 are not listed.
:::

## Прочитайте перед оновленням

Please continue reading for [upgrade instructions](#upgrade-guide).

## Основні зміни

- **Rover support rework**
  - New dedicated firmware build for rovers (airframe IDs 50000–52000)
  - Separate modules for Ackermann, differential and mecanum rovers, each with manual, acro, stabilized, position and auto modes
  - Shared pure-pursuit guidance library for all rover modules
  - Legacy rover position control module deprecated in favor of the new modules

## Інструкції для оновлення

- [PX4-Autopilot#24648](https://github.com/PX4/PX4-Autopilot/pull/24648): Added setting default for EKF2_EV_CTRL to 15 for VOXL 2 boards
- [PX4-Autopilot#22517](https://github.com/PX4/PX4-Autopilot/pull/22517): Change default ethernet IP
- [PX4-Autopilot#24602](https://github.com/PX4/PX4-Autopilot/pull/24602): remove serial port default from sf45 module

## Інші зміни

### Підтримка обладнання

- **[New Hardware]** [PX4-Autopilot#23830](https://github.com/PX4/PX4-Autopilot/pull/23830): Boards: ARK FPV FC
- **[New Hardware]** [PX4-Autopilot#23414](https://github.com/PX4/PX4-Autopilot/pull/23414): board: add cuav 7-nano
- **[New Hardware]** [PX4-Autopilot#24769](https://github.com/PX4/PX4-Autopilot/pull/24769): add new board corvon743v1
- **[New Hardware]** [PX4-Autopilot#24018](https://github.com/PX4/PX4-Autopilot/pull/24018): boards: bluerobotics: navigator: Add initial support
- **[New Hardware]** [PX4-Autopilot#24147](https://github.com/PX4/PX4-Autopilot/pull/24147): boards: add new board micoair743-v2
- **[New Hardware]** [PX4-Autopilot#23218](https://github.com/PX4/PX4-Autopilot/pull/23218): boards: add new board micoair h743
- **[New Hardware]** [PX4-Autopilot#24512](https://github.com/PX4/PX4-Autopilot/pull/24512): boards: Add FMUv6s target
- **[New Hardware]** [PX4-Autopilot#23927](https://github.com/PX4/PX4-Autopilot/pull/23927): manifest: Add Skynode S baseboard
- **[New Hardware]** [PX4-Autopilot#23257](https://github.com/PX4/PX4-Autopilot/pull/23257): Add Tropic VMU board support (Baseboard for Teensy 4.1)
- **[New Hardware]** [PX4-Autopilot#23697](https://github.com/PX4/PX4-Autopilot/pull/23697): boards: add new board X-MAV AP-H743v2
- **[New Hardware]** [PX4-Autopilot#23551](https://github.com/PX4/PX4-Autopilot/pull/23551): 3DR boards: Support for 3DR Control Zero H7 OEM Rev G
- **[New Hardware]** [PX4-Autopilot#23623](https://github.com/PX4/PX4-Autopilot/pull/23623): new board support ZeroOne x6

### Загальні

- [Optical flow scaling factor - SENS_FLOW_SCALE](../sensor/optical_flow.md#scale-factor). ([PX4-Autopilot#23936](https://github.com/PX4/PX4-Autopilot/pull/23936)).

- [PX4-Autopilot#22813](https://github.com/PX4/PX4-Autopilot/pull/22813): Reintroduce optional parameter versioning mechanism for airframe maintainers

- [Battery level estimation improvements](../config/battery.md). ([PX4-Autopilot#23205](https://github.com/PX4/PX4-Autopilot/pull/23205)).
  - [Voltage-based estimation with load compensation](../config/battery.md#voltage-based-estimation-with-load-compensation) now uses a real-time estimate of the internal resistance of the battery to compensate voltage drops under load (with increased current), providing a better capacity estimate than with the raw measured voltage.
  - Thrust-based load compensation has been removed (along with the `BATn_V_LOAD_DROP` parameters, where `n` is the battery number).

- The [Position (GNSS) loss failsafe](../config/safety.md#position-gnss-loss-failsafe) configurable delay (`COM_POS_FS_DELAY`) has been removed.
  The failsafe will now trigger 1 second after position has been lost. ([PX4-Autopilot#24063](https://github.com/PX4/PX4-Autopilot/pull/24063)).

- [Log Encryption](../dev_log/log_encryption.md) now generates an encrypted log that contains the public-key-encrypted symmetric key that can be used to decrypt it, instead of putting the key into a separate file.
  This makes log decryption much easier, as there is no need to download or identify a separate key file.
  ([PX4-Autopilot#24024](https://github.com/PX4/PX4-Autopilot/pull/24024)).

- The generic mission command timeout [MIS_COMMAND_TOUT](../advanced_config/parameter_reference.md#MIS_COMMAND_TOUT) parameter replaces the delivery-specific `MIS_PD_TO` parameter.
  Mission commands that may take some time to complete, such as those for controlling gimbals, winches, and grippers, will progress to the next item when either feedback is received or the timeout expires.
  This is often used to provide a minimum delay for hardware that does not provide completion feedback, so that it can reach the commanded state before the mission progresses.
  ([PX4-Autopilot#23960](https://github.com/PX4/PX4-Autopilot/pull/23960)).

- **[uORB]** Introduce a [version field](../middleware/uorb.md#message-versioning) for a subset of uORB messages ([PX4-Autopilot#23850](https://github.com/PX4/PX4-Autopilot/pull/23850))

- [Compass calibration](../config/compass.md) disables internal compasses if an external compass is available.
  This typically reduces false warnings due to magnetometer inconsistencies.
  ([PX4-Autopilot#24316](https://github.com/PX4/PX4-Autopilot/pull/24316)).

### Управління

- [PX4-Autopilot#23863](https://github.com/PX4/PX4-Autopilot/pull/23863): [Sponsored by ARK] Bidirectional DShot

- [PX4-Autopilot#24196](https://github.com/PX4/PX4-Autopilot/pull/24196): Make control allocation and actuator effectiveness a non-module-specific library

- [PX4-Autopilot#24221](https://github.com/PX4/PX4-Autopilot/pull/24221): Spacecraft Build and Bare Control Allocator

- Configurable multicopter orbit-mode yaw via `MC_ORBIT_YAW_MOD` ([PX4-Autopilot#23358](https://github.com/PX4/PX4-Autopilot/pull/23358))

- Collision prevention now works in manual (acceleration-based) flight mode (`MPC_POS_MODE`) ([PX4-Autopilot#23507](https://github.com/PX4/PX4-Autopilot/pull/23507))

### Оцінки

- [PX4-Autopilot#23854](https://github.com/PX4/PX4-Autopilot/pull/23854): EKF2: ellipsoidal earth navigation

- [PX4-Autopilot#23263](https://github.com/PX4/PX4-Autopilot/pull/23263): EKF2: Terrain state

- [PX4-Autopilot#23185](https://github.com/PX4/PX4-Autopilot/pull/23185): ekf2: add mag type init

- [PX4-Autopilot#23436](https://github.com/PX4/PX4-Autopilot/pull/23436): ekf2: Optical flow enabled by default

- Position-loss failsafe delay removed; triggers 1 s after loss (see Common)

### Датчики

- [PX4-Autopilot#23656](https://github.com/PX4/PX4-Autopilot/pull/23656): Implemented AUAV absolute/differential pressure sensor support

- [PX4-Autopilot#23639](https://github.com/PX4/PX4-Autopilot/pull/23639): Implemented temperature sensor support for INA228 / INA238

- [PX4-Autopilot#22744](https://github.com/PX4/PX4-Autopilot/pull/22744): Add Ublox ZED-F9P-15B

- [PX4-Autopilot#24316](https://github.com/PX4/PX4-Autopilot/pull/24316): Mag cal: automatically disable internal mags if external ones are available

- [PX4-Autopilot#23064](https://github.com/PX4/PX4-Autopilot/pull/23064): BMP581: Add Bosch BMP581 barometer

- [PX4-Autopilot#22914](https://github.com/PX4/PX4-Autopilot/pull/22914): Murata SCH16T IMU driver

- [PX4-Autopilot#23023](https://github.com/PX4/PX4-Autopilot/pull/23023): ST IIS2MDC Magnetometer driver

- [PX4-Autopilot#24121](https://github.com/PX4/PX4-Autopilot/pull/24121): Include distance sensor in dds topics

- [PX4-Autopilot#23925](https://github.com/PX4/PX4-Autopilot/pull/23925): drivers: magnetometer: mmc5983ma: Add SPI support

- [PX4-Autopilot#23909](https://github.com/PX4/PX4-Autopilot/pull/23909): drivers/magnetometer/ak09916: Add support to AK09915

- [PX4-Autopilot#23362](https://github.com/PX4/PX4-Autopilot/pull/23362): Add Bosch BMM350 magnetometer

- [PX4-Autopilot#24316](https://github.com/PX4/PX4-Autopilot/pull/24316): Compass calibration now disables internal compass when external unit present, reducing false warnings

### Симуляція

- **SIH**:
  - The SIH on SITL [custom takeoff location](../sim_sih/index.md#set-custom-takeoff-location) in now set using the normal unscaled GPS position values, where previously the value needed to be multiplied by 1E7.
    ([PX4-Autopilot#23363](https://github.com/PX4/PX4-Autopilot/pull/23363)).
  - SIH now supports the standard VTOL airframe
    ([PX4-Autopilot#24175](https://github.com/PX4/PX4-Autopilot/pull/24175)).
- **Gazebo**:
  - Gazebo Harmonic LTS release replaces Gazebo Garden as the version supported by PX4.
    The default installer scripts (used for CI) and documentation have been updated.
    This is required because Garden end-of-life is Nov 2024.
    ([PX4-Autopilot#23603](https://github.com/PX4/PX4-Autopilot/pull/23603))
  - New vehicle model `x500_lidar_2d` — [x500 Quadrotor with 2D Lidar](../sim_gazebo_gz/vehicles.md#x500-quadrotor-with-2d-lidar). ([PX4-Autopilot#22418](https://github.com/PX4/PX4-Autopilot/pull/22418), [PX4-gazebo-models#41](https://github.com/PX4/PX4-gazebo-models/pull/41)).
  - New vehicle model `x500_lidar_front` — [X500 Quadrotor with 1D LIDAR (Front-facing)](../sim_gazebo_gz/vehicles.md#x500-quadrotor-with-1d-lidar-front-facing). ([PX4-Autopilot#23879](https://github.com/PX4/PX4-Autopilot/pull/23879), [PX4-gazebo-models#62](https://github.com/PX4/PX4-gazebo-models/pull/62/files)).
  - New vehicle model `x500_lidar_down` — [X500 Quadrotor with 1D LIDAR (Down-facing)](../sim_gazebo_gz/vehicles.md#x500-quadrotor-with-1d-lidar-down-facing). ([PX4-Autopilot#23879](https://github.com/PX4/PX4-Autopilot/pull/23879), [PX4-gazebo-models#62](https://github.com/PX4/PX4-gazebo-models/pull/62/files)).
  - New vehicle model `r1_rover` — [Aion Robotics R1 Rover](../sim_gazebo_gz/vehicles.md#differential-rover) ([PX4-Autopilot#22402](https://github.com/PX4/PX4-Autopilot/pull/22402) and [PX4-gazebo-models#21](https://github.com/PX4/PX4-gazebo-models/pull/21)).
  - New vehicle model `rover_ackermann` — [Ackermann Rover](../sim_gazebo_gz/vehicles.md#ackermann-rover) ([PX4-Autopilot#23383](https://github.com/PX4/PX4-Autopilot/pull/23383) and [PX4-gazebo-models#46](https://github.com/PX4/PX4-gazebo-models/pull/46)).
  - New vehicle model `x500_gimbal` — [Quadrotor(x500) with gimbal (Front-facing) in Gazebo](../sim_gazebo_gz/vehicles.md#x500-quadrotor-with-gimbal-front-facing) ([PX4-Autopilot#23382](https://github.com/PX4/PX4-Autopilot/pull/23382) and [PX4-gazebo-models#47](https://github.com/PX4/PX4-gazebo-models/pull/47) and [PX4-gazebo-models#70](https://github.com/PX4/PX4-gazebo-models/pull/70)).
  - New vehicle model `quadtailsitter` — [Quad Tailsitter VTOL](../sim_gazebo_gz/vehicles.md#quad-tailsitter-vtol) ([PX4-Autopilot#23943](https://github.com/PX4/PX4-Autopilot/pull/23943) and [PX4-gazebo-models#65](https://github.com/PX4/PX4-gazebo-models/pull/65)).
  - New vehicle model `tiltrotor` — [Tiltrotor VTOL](../sim_gazebo_gz/vehicles.md#tiltrotor-vtol) ([PX4-Autopilot#24028](https://github.com/PX4/PX4-Autopilot/pull/24028) and [PX4-gazebo-models#66](https://github.com/PX4/PX4-gazebo-models/pull/66)).
  - [Faster than Real-time Simulation](../simulation/index.md#simulation_speed) ([PX4-Autopilot#24421](https://github.com/PX4/PX4-Autopilot/pull/24421), [PX4-Autopilot#23783](https://github.com/PX4/PX4-Autopilot/pull/23783))
  - [PX4-Autopilot#24471](https://github.com/PX4/PX4-Autopilot/pull/24471): Gazebo: Moving platform

### uXRCE-DDS / ROS2

- **[Feature]** [PX4-Autopilot#24113](https://github.com/PX4/PX4-Autopilot/pull/24113): <Badge type="warning" text="Experimental"/> [ROS 2 Message Translation Node](../ros2/px4_ros2_msg_translation_node.md) to translate PX4 messages from one definition version to another dynamically
- [PX4-Autopilot#24582](https://github.com/PX4/PX4-Autopilot/pull/24582): dds_topics: add vtol_vehicle_status
- [PX4-Autopilot#24583](https://github.com/PX4/PX4-Autopilot/pull/24583): dds_topics: add home_position

### MAVLink

- Уточнюється

### Мульти-Ротор

- [PX4-Autopilot#24173](https://github.com/PX4/PX4-Autopilot/pull/24173): [Multirotor] add yaw torque low pass filter

- [PX4-Autopilot#23943](https://github.com/PX4/PX4-Autopilot/pull/23943): Add gz model for quadtailsitter

- [PX4-Autopilot#23358](https://github.com/PX4/PX4-Autopilot/pull/23358): Allow system-default [multicopter orbit mode](../flight_modes_mc/orbit.md) yaw behaviour to be configured, using the parameter [MC_ORBIT_YAW_MOD](../advanced_config/parameter_reference.md#MC_ORBIT_YAW_MOD)

- [PX4-Autopilot#23507](https://github.com/PX4/PX4-Autopilot/pull/23507): Adapted the [Collision Prevention](../computer_vision/collision_prevention.md) implementation to work in the default manual flight mode (Acceleration Based) [MPC_POS_MODE](../advanced_config/parameter_reference.md#MPC_POS_MODE).

### VTOL

- Уточнюється

### Літак з фіксованим крилом

- [PX4-Autopilot#24167](https://github.com/PX4/PX4-Autopilot/pull/24167): Fixedwing: fix wheel controller

- [PX4-Autopilot#23520](https://github.com/PX4/PX4-Autopilot/pull/23520): FixedWing: allow position control without valid global position

- Improvement: Fixed-wing auto takeoff: enable setting takeoff flaps for hand/catapult launch. [PX4-Autopilot#23460](https://github.com/PX4/PX4-Autopilot/pull/23460)

### Ровер

This release contains a major rework for the rover support in PX4:

- Complete restructure of the [rover related documentation](../frames_rover/index.md).
- New firmware build specifically for [rovers](../frames_rover/index.md#flashing-the-rover-build).
- New module dedicated to [Ackermann rovers](../frames_rover/ackermann.md):
  - The module currently supports [manual mode](../flight_modes_rover/ackermann.md#manual-mode), [acro mode](../flight_modes_rover/ackermann.md#acro-mode), [position mode](../flight_modes_rover/ackermann.md#position-mode) and [auto modes](../flight_modes_rover/ackermann.md#auto-modes).
- New module dedicated to [differential rovers](../frames_rover/differential.md):
  - The module currently supports [manual mode](../flight_modes_rover/differential.md#manual-mode), [acro mode](../flight_modes_rover/differential.md#acro-mode), [stabilized mode](../flight_modes_rover/differential.md#stabilized-mode), [position mode](../flight_modes_rover/differential.md#position-mode) and [auto modes](../flight_modes_rover/differential.md#auto-modes).
- New module dedicated to [mecanum rovers](../frames_rover/mecanum.md):
  - The module currently supports [manual mode](../flight_modes_rover/mecanum.md#manual-mode), [acro mode](../flight_modes_rover/mecanum.md#acro-mode), [stabilized mode](../flight_modes_rover/mecanum.md#stabilized-mode), [position mode](../flight_modes_rover/mecanum.md#position-mode) and [auto modes](../flight_modes_rover/mecanum.md#auto-modes).
- Added rover-specific firmware build (`50000–52000`) for Ackermann, differential and mecanum rovers
- Restructure of the [rover airframe](../airframes/airframe_reference.md#rover) numbering convention ([PX4-Autopilot#23506](https://github.com/PX4/PX4-Autopilot/pull/23506)).
  This also introduces several [new rover airframes](../airframes/airframe_reference.md#rover):
  - Generic Differential Rover `50000`.
  - Generic Ackermann Rover `51000`.
  - Axial SCX10 2 Trail Honcho `51001`.
  - Generic Mecanum Rover `52000`.
- Library for the [pure pursuit guidance algorithm](../config_rover/differential.md#pure-pursuit-guidance-logic) that is shared by all the rover modules.
- [Simulation](../frames_rover/index.md#simulation) for differential-steering and Ackermann rovers in gazebo (for release notes see `r1_rover` and `rover_ackermann` in [simulation](#simulation)).
- Deprecation of the [rover position control](../frames_rover/rover_position_control.md) module: Note that the legacy rover module still exists but has been superseded by the new dedicated modules.

### Infrastructure

- [PX4-Autopilot#24011](https://github.com/PX4/PX4-Autopilot/pull/24011): standard_modes: add vehicle-type specific standard modes
- [PX4-Autopilot#24020](https://github.com/PX4/PX4-Autopilot/pull/24020): ci: build all upload to releases
- [PX4-Autopilot#24002](https://github.com/PX4/PX4-Autopilot/pull/24002): ci: px4-dev container
- [PX4-Autopilot#23937](https://github.com/PX4/PX4-Autopilot/pull/23937): ci: workflow for ubuntu 24
- [PX4-Autopilot#23869](https://github.com/PX4/PX4-Autopilot/pull/23869): ci: add test for Ubuntu 22.04
- [PX4-Autopilot#23574](https://github.com/PX4/PX4-Autopilot/pull/23574): ci: try runs-on Dronecode Infra
- [PX4-Autopilot#23550](https://github.com/PX4/PX4-Autopilot/pull/23550): ci: replace build workflows
