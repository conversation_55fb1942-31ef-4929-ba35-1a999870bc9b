// -----------------------------------------------------------------------------
// This file was autogenerated by symforce from template:
//     function/FUNCTION.h.jinja
// Do NOT modify by hand.
// -----------------------------------------------------------------------------

#pragma once

#include <matrix/math.hpp>

namespace sym {

/**
 * This function was autogenerated from a symbolic function. Do not modify by hand.
 *
 * Symbolic function: compute_hagl_innov_var
 *
 * Args:
 *     P: Matrix24_24
 *     R: Scalar
 *
 * Outputs:
 *     innov_var: Scalar
 */
template <typename Scalar>
void ComputeHaglInnovVar(const matrix::Matrix<Scalar, 24, 24>& P, const <PERSON><PERSON><PERSON> R,
                         <PERSON>alar* const innov_var = nullptr) {
  // Total ops: 4

  // Input arrays

  // Intermediate terms (0)

  // Output terms (1)
  if (innov_var != nullptr) {
    Scalar& _innov_var = (*innov_var);

    _innov_var = P(23, 23) - P(23, 8) - P(8, 23) + P(8, 8) + R;
  }
}  // NOLINT(readability/fn_size)

// NOLINTNEXTLINE(readability/fn_size)
}  // namespace sym
