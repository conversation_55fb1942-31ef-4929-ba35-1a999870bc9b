//
// Copyright (c) 2022 ZettaScale Technology
//
// This program and the accompanying materials are made available under the
// terms of the Eclipse Public License 2.0 which is available at
// http://www.eclipse.org/legal/epl-2.0, or the Apache License, Version 2.0
// which is available at https://www.apache.org/licenses/LICENSE-2.0.
//
// SPDX-License-Identifier: EPL-2.0 OR Apache-2.0
//
// Contributors:
//   ZettaScale Zenoh Team, <<EMAIL>>
//

// ⚠️ This file is auto-generated from include/zenoh-pico.h.in

#ifndef ZENOH_PICO_H
#define ZENOH_PICO_H

#define ZENOH_PICO "@ZENOH_PICO@"
#define ZENOH_PICO_MAJOR @ZENOH_PICO_MAJOR@
#define ZENOH_PICO_MINOR @ZENOH_PICO_MINOR@
#define ZENOH_PICO_PATCH @ZENOH_PICO_PATCH@
#define ZENOH_PICO_TWEAK @ZENOH_PICO_TWEAK@

#cmakedefine Z_FRAG_MAX_SIZE @FRAG_MAX_SIZE@
#cmakedefine Z_BATCH_UNICAST_SIZE @BATCH_UNICAST_SIZE@

#define Z_FEATURE_MULTI_THREAD @Z_FEATURE_MULTI_THREAD@
#define Z_FEATURE_PUBLICATION @Z_FEATURE_PUBLICATION@
#define Z_FEATURE_SUBSCRIPTION @Z_FEATURE_SUBSCRIPTION@
#define Z_FEATURE_QUERY @Z_FEATURE_QUERY@
#define Z_FEATURE_QUERYABLE @Z_FEATURE_QUERYABLE@
#define Z_FEATURE_RAWETH_TRANSPORT @Z_FEATURE_RAWETH_TRANSPORT@
#define Z_FEATURE_INTEREST @Z_FEATURE_INTEREST@

#include "zenoh-pico/api/constants.h"
#include "zenoh-pico/api/handlers.h"
#include "zenoh-pico/api/macros.h"
#include "zenoh-pico/api/primitives.h"
#include "zenoh-pico/api/types.h"

#endif /* ZENOH_PICO_H */
