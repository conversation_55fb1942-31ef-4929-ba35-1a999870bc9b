#!/bin/sh
#
# @name Typhoon H480 SITL
#
# @type Hexarotor x
#

. ${R}etc/init.d/rc.mc_defaults

param set-default MAV_TYPE 13

param set-default MC_PITCHRATE_P 0.08
param set-default MC_PITCHRATE_I 0.04
param set-default MC_PITCHRATE_D 0.001
param set-default MC_PITCH_P 9
param set-default MC_ROLLRATE_P 0.08
param set-default MC_ROLLRATE_I 0.04
param set-default MC_ROLLRATE_D 0.001
param set-default MC_ROLL_P 9
param set-default MPC_XY_VEL_I_ACC 4
param set-default MPC_XY_VEL_P_ACC 3

param set-default RTL_DESCEND_ALT 10

param set-default TRIG_INTERFACE 3
param set-default TRIG_MODE 4
param set-default MNT_MODE_IN 4
param set-default MNT_MODE_OUT 2
param set-default MAV_PROTO_VER 2

param set-default CA_AIRFRAME 0
param set-default CA_ROTOR_COUNT 6

param set-default CA_ROTOR0_PX 0
param set-default CA_ROTOR0_PY 1
param set-default CA_ROTOR0_KM -0.05
param set-default CA_ROTOR1_PX 0
param set-default CA_ROTOR1_PY -1
param set-default CA_ROTOR1_KM 0.05
param set-default CA_ROTOR2_PX 0.866025
param set-default CA_ROTOR2_PY -0.5
param set-default CA_ROTOR2_KM -0.05
param set-default CA_ROTOR3_PX -0.866025
param set-default CA_ROTOR3_PY 0.5
param set-default CA_ROTOR3_KM 0.05
param set-default CA_ROTOR4_PX 0.866025
param set-default CA_ROTOR4_PY 0.5
param set-default CA_ROTOR4_KM 0.05
param set-default CA_ROTOR5_PX -0.866025
param set-default CA_ROTOR5_PY -0.5
param set-default CA_ROTOR5_KM -0.05

param set-default PWM_MAIN_FUNC1 101
param set-default PWM_MAIN_FUNC2 102
param set-default PWM_MAIN_FUNC3 103
param set-default PWM_MAIN_FUNC4 104
param set-default PWM_MAIN_FUNC5 105
param set-default PWM_MAIN_FUNC6 106
# Gimbal
param set-default PWM_MAIN_FUNC7 420
param set-default PWM_MAIN_FUNC8 421
param set-default PWM_MAIN_FUNC9 422
# Landing gear
param set-default PWM_MAIN_FUNC10 400
param set-default PWM_MAIN_FUNC11 400
