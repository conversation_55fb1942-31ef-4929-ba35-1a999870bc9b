# Інструменти розробника, що підтримуються спільнотою

Ця секція містить інформацію про _підтримувані спільнотою_  середовища розробки, IDE, симулятори та інші інструменти.

:::warning
Ці налаштування не обслуговуються, не тестуються або не підтримуються основною командою розробників.
Вони можуть працювати або не працювати з поточними версіями PX4.

Дивіться [Встановлення інструментарію](../dev_setup/dev_env.md) для інформації про середовища та інструменти, що підтримуються основною командою розробників.
:::

Інструменти мають різний рівень підтримки своїми спільнотами (деякі добре підтримують, інші - ні).
Питання про ці інструменти повинні порушуватися на [форумах для обговорення](../contribute/support.md#forums-and-chat)

- Платформи/налаштування розробки
  - [CentOS Linux](../dev_setup/dev_env_linux_centos.md)
  - [Arch Linux](../dev_setup/dev_env_linux_arch.md)
  - [Windows VM Toolchain](../dev_setup/dev_env_windows_vm.md) — встановлення Ubuntu на віртуальній машині Windows.
  - [Windows Cygwin Toolchain](../dev_setup/dev_env_windows_cygwin.md) — налаштування Windows працює тільки для PX4 v1.12
    - [Технічне обслуговування Windows Cygwin Toolchain](../dev_setup/dev_env_windows_cygwin_packager_setup.md)
- IDEs
  - [Qt Creator IDE](../dev_setup/qtcreator.md)
- [Симулятори](../simulation/community_supported_simulators.md) — [Simulation-In-Hardware](../sim_sih/index.md), [FlightGear](../sim_flightgear/index.md), [JSBSim](../sim_jsbsim/index.md), [AirSim](../sim_airsim/index.md), [HITL](../simulation/hitl.md)
