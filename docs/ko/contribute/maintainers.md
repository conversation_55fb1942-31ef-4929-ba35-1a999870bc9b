# Maintainer Role

Dronecode maintainers have technical leadership and responsibility for specific areas of PX4, and for other ecosystem components such as MAVLink, MAVSDK, QGroundControl, and others.
The maintainer role is defined by the community with help and supervision from the [Dronecode Foundation](https://www.dronecode.org/).

To find the most up-to-date maintainers list, visit [PX4-Autopilot README](https://github.com/PX4/PX4-Autopilot#maintenance-team).

## Recruitment Process

If you would like to join the PX4 maintainers team or if you want to nominate someone else follow the steps below:

1. Read the [role description](#dronecode-maintainer-role-description), and make sure you understand the responsibilities of the role.
2. To nominate yourself, reach out to one of the maintainers (see the complete list in the [PX4-Autopilot README](https://github.com/PX4/PX4-Autopilot#maintenance-team)), and seek their sponsorship.
3. Express your interest in becoming a maintainer, and specify which area you would like to maintain.
4. The sponsoring maintainer needs to bring this up for discussion in one of the [weekly developer calls](dev_call.md).
   The maintainer team will vote on the call to determine whether to accept you as a maintainer.

## Onboarding Process

Once accepted every maintainers will go through the following process:

1. **Discord** server admin will grant you the `dev team` role, which gives you:
   1. Basic admin privileges on discord.
   2. Access to the `#maintainers` channel.
2. You will be given access to the GitHub team: "[`Dev Team`](https://github.com/orgs/PX4/teams/dev-team)"  which grants you:
   1. Permission to merge the PR of any of PX4 workspace repositories after it's approved
   2. Permission to trigger GitHub actions when a new contributor opens a PR.
   3. Permission to edit Issue/PR contents.
3. **Add your info to official PX4 channels**:
   1. Include your information on the PX4 [README](https://github.com/PX4/PX4-Autopilot/blob/main/README.md) next to the rest of the team
   2. Listed on the [Maintainers section](https://px4.io/community/maintainers/) of the PX4 website.
   3. Add your information to the internal Dronecode database of maintainers to keep you in sync.
   4. Community introduction to the new maintainer in the form of a forum post, which is promoted through ever growing official channels

## Dronecode Maintainer Role Description

### 요약

Maintainers lead/manage the development of a **specific category (referred to as category below)** of any Open Source Projects hosted within the Dronecode Foundation, such as the PX4 Autopilot.

### Responsibilities

1. Take charge of overseeing the development in their category.
2. Provide guidance/advice on community members in their category.
3. Review relevant pull requests and issues from the community on GitHub.
4. Coordinate with the maintainer group.
5. Keep regular attendance on [weekly meetings ](dev_call.md).
6. Help create and maintain a roadmap for the project your represent.
7. Uphold the [Code of Conduct](https://github.com/Dronecode/foundation/blob/main/CODE-OF-CONDUCT.md) of our community.

### Qualifications

1. Proven track record of valuable contributions.
2. Domain expertise in the category field.
3. Good overview of the project you are applying to.
4. You need to manage approval from your employer when relevant.

### Perks

1. **Official recognition** as the maintainer in Dronecode/PX4 website, documentation, community and social media.
2. **Github & Discord privileges** (described in the [onboarding process](#onboarding-process)).
3. Priority placement to the yearly **PX4 Developer Summit** scholarship which helps you with travel reimbursement.

### Tools we Provide to Assist You

Dronecode will provide the following tools to help you:

1. **Community survey**: If you need any insight into the community's opinion, we will send out social media posts, mailing lists, announcements in Discord server to get that answer for you.
2. **Workflow automation**: We will provide workflow for PR/Issue review & tagging process to help you.

And as always, don't hesitate to reach out if you need help with anything.
We are here for you!

### Point of Contact

Regarding questions about the maintainer role, please contact the maintainer team.
