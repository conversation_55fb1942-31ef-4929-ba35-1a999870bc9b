<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
    <asset>
        <contributor>
            <authoring_tool>SketchUp 15.3.331</authoring_tool>
        </contributor>
        <created>2015-09-07T23:00:08Z</created>
        <modified>2015-09-07T23:00:08Z</modified>
        <unit meter="0.01" name="centimeter" />
        <up_axis>Z_UP</up_axis>
    </asset>
    <library_visual_scenes>
        <visual_scene id="ID1">
            <node name="SketchUp">
				<node id="ID3" name="instance_0">
					<matrix>1 0 0 -60 0 1 0 -40 0 0 1 -62.5 0 0 0 1</matrix>
					<instance_node url="#ID4" />
				</node>
            </node>
        </visual_scene>
    </library_visual_scenes>
    <library_nodes>
        <node id="ID4" name="Uline_S-3878_Box">
            <instance_geometry url="#ID5">
                <bind_material>
                    <technique_common>
                        <instance_material symbol="Material2" target="#ID6">
                            <bind_vertex_input semantic="UVSET0" input_semantic="TEXCOORD" input_set="0" />
                        </instance_material>
                    </technique_common>
                </bind_material>
            </instance_geometry>
            <instance_geometry url="#ID18">
                <bind_material>
                    <technique_common>
                        <instance_material symbol="Material2" target="#ID19">
                            <bind_vertex_input semantic="UVSET0" input_semantic="TEXCOORD" input_set="0" />
                        </instance_material>
                    </technique_common>
                </bind_material>
            </instance_geometry>
            <instance_geometry url="#ID31">
                <bind_material>
                    <technique_common>
                        <instance_material symbol="Material2" target="#ID32">
                            <bind_vertex_input semantic="UVSET0" input_semantic="TEXCOORD" input_set="0" />
                        </instance_material>
                    </technique_common>
                </bind_material>
            </instance_geometry>
            <instance_geometry url="#ID44">
                <bind_material>
                    <technique_common>
                        <instance_material symbol="Material2" target="#ID45">
                            <bind_vertex_input semantic="UVSET0" input_semantic="TEXCOORD" input_set="0" />
                        </instance_material>
                    </technique_common>
                </bind_material>
            </instance_geometry>
            <instance_geometry url="#ID57">
                <bind_material>
                    <technique_common>
                        <instance_material symbol="Material2" target="#ID58">
                            <bind_vertex_input semantic="UVSET0" input_semantic="TEXCOORD" input_set="0" />
                        </instance_material>
                    </technique_common>
                </bind_material>
            </instance_geometry>
            <instance_geometry url="#ID70">
                <bind_material>
                    <technique_common>
                        <instance_material symbol="Material2" target="#ID45">
                            <bind_vertex_input semantic="UVSET0" input_semantic="TEXCOORD" input_set="0" />
                        </instance_material>
                    </technique_common>
                </bind_material>
            </instance_geometry>
        </node>
    </library_nodes>
    <library_geometries>
        <geometry id="ID5">
            <mesh>
                <source id="ID11">
                    <float_array id="ID15" count="12">0 80 125 0 0 0 0 0 125 0 80 0</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID15" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID12">
                    <float_array id="ID16" count="12">-1 0 0 -1 0 0 -1 0 0 -1 0 -0</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID16" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID14">
                    <float_array id="ID17" count="8">0 0.9994983 1 0 1 0.9994983 0 0</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID17" stride="2">
                            <param name="S" type="float" />
                            <param name="T" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <vertices id="ID13">
                    <input semantic="POSITION" source="#ID11" />
                    <input semantic="NORMAL" source="#ID12" />
                </vertices>
                <triangles count="2" material="Material2">
                    <input offset="0" semantic="VERTEX" source="#ID13" />
                    <input offset="1" semantic="TEXCOORD" source="#ID14" />
                    <p>0 0 1 1 2 2 1 1 0 0 3 3</p>
                </triangles>
            </mesh>
        </geometry>
        <geometry id="ID18">
            <mesh>
                <source id="ID24">
                    <float_array id="ID28" count="12">0 80 125 120 80 0 0 80 0 120 80 125</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID28" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID25">
                    <float_array id="ID29" count="12">0 1 0 0 1 0 0 1 0 0 1 0</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID29" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID27">
                    <float_array id="ID30" count="8">1 1 0 0 1 0 0 1</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID30" stride="2">
                            <param name="S" type="float" />
                            <param name="T" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <vertices id="ID26">
                    <input semantic="POSITION" source="#ID24" />
                    <input semantic="NORMAL" source="#ID25" />
                </vertices>
                <triangles count="2" material="Material2">
                    <input offset="0" semantic="VERTEX" source="#ID26" />
                    <input offset="1" semantic="TEXCOORD" source="#ID27" />
                    <p>0 0 1 1 2 2 1 1 0 0 3 3</p>
                </triangles>
            </mesh>
        </geometry>
        <geometry id="ID31">
            <mesh>
                <source id="ID37">
                    <float_array id="ID41" count="12">120 0 125 0 0 0 120 0 0 0 0 125</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID41" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID38">
                    <float_array id="ID42" count="12">0 -1 0 0 -1 0 0 -1 0 0 -1 0</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID42" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID40">
                    <float_array id="ID43" count="8">0.9990431 1 0 0 0.9990431 0 0 1</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID43" stride="2">
                            <param name="S" type="float" />
                            <param name="T" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <vertices id="ID39">
                    <input semantic="POSITION" source="#ID37" />
                    <input semantic="NORMAL" source="#ID38" />
                </vertices>
                <triangles count="2" material="Material2">
                    <input offset="0" semantic="VERTEX" source="#ID39" />
                    <input offset="1" semantic="TEXCOORD" source="#ID40" />
                    <p>0 0 1 1 2 2 1 1 0 0 3 3</p>
                </triangles>
            </mesh>
        </geometry>
        <geometry id="ID44">
            <mesh>
                <source id="ID50">
                    <float_array id="ID54" count="12">120 0 125 0 80 125 0 0 125 120 80 125</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID54" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID51">
                    <float_array id="ID55" count="12">0 0 1 0 0 1 0 0 1 0 0 1</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID55" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID53">
                    <float_array id="ID56" count="8">120 0 0 80 0 0 120 80</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID56" stride="2">
                            <param name="S" type="float" />
                            <param name="T" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <vertices id="ID52">
                    <input semantic="POSITION" source="#ID50" />
                    <input semantic="NORMAL" source="#ID51" />
                </vertices>
                <triangles count="2" material="Material2">
                    <input offset="0" semantic="VERTEX" source="#ID52" />
                    <input offset="1" semantic="TEXCOORD" source="#ID53" />
                    <p>0 0 1 1 2 2 1 1 0 0 3 3</p>
                </triangles>
            </mesh>
        </geometry>
        <geometry id="ID57">
            <mesh>
                <source id="ID63">
                    <float_array id="ID67" count="12">120 80 0 120 0 125 120 0 0 120 80 125</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID67" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID64">
                    <float_array id="ID68" count="12">1 0 0 1 0 0 1 0 0 1 0 0</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID68" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID66">
                    <float_array id="ID69" count="8">0.9983687 0 0 1 0 0 0.9983687 1</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID69" stride="2">
                            <param name="S" type="float" />
                            <param name="T" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <vertices id="ID65">
                    <input semantic="POSITION" source="#ID63" />
                    <input semantic="NORMAL" source="#ID64" />
                </vertices>
                <triangles count="2" material="Material2">
                    <input offset="0" semantic="VERTEX" source="#ID65" />
                    <input offset="1" semantic="TEXCOORD" source="#ID66" />
                    <p>0 0 1 1 2 2 1 1 0 0 3 3</p>
                </triangles>
            </mesh>
        </geometry>
        <geometry id="ID70">
            <mesh>
                <source id="ID71">
                    <float_array id="ID75" count="12">120 80 0 0 0 0 0 80 0 120 0 0</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID75" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID72">
                    <float_array id="ID76" count="12">0 0 -1 0 0 -1 0 0 -1 0 0 -1</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID76" stride="3">
                            <param name="X" type="float" />
                            <param name="Y" type="float" />
                            <param name="Z" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <source id="ID74">
                    <float_array id="ID77" count="8">-120 80 0 0 0 80 -120 0</float_array>
                    <technique_common>
                        <accessor count="4" source="#ID77" stride="2">
                            <param name="S" type="float" />
                            <param name="T" type="float" />
                        </accessor>
                    </technique_common>
                </source>
                <vertices id="ID73">
                    <input semantic="POSITION" source="#ID71" />
                    <input semantic="NORMAL" source="#ID72" />
                </vertices>
                <triangles count="2" material="Material2">
                    <input offset="0" semantic="VERTEX" source="#ID73" />
                    <input offset="1" semantic="TEXCOORD" source="#ID74" />
                    <p>0 0 1 1 2 2 1 1 0 0 3 3</p>
                </triangles>
            </mesh>
        </geometry>
    </library_geometries>
    <library_materials>
        <material id="ID6" name="S-3878BX_Side2">
            <instance_effect url="#ID7" />
        </material>
        <material id="ID19" name="S-3878BX_Back">
            <instance_effect url="#ID20" />
        </material>
        <material id="ID32" name="S-3878BX_Front">
            <instance_effect url="#ID33" />
        </material>
        <material id="ID45" name="Kraft_Tile">
            <instance_effect url="#ID46" />
        </material>
        <material id="ID58" name="S-3878BX_Side1">
            <instance_effect url="#ID59" />
        </material>
    </library_materials>
    <library_effects>
        <effect id="ID7">
            <profile_COMMON>
                <newparam sid="ID9">
                    <surface type="2D">
                        <init_from>ID8</init_from>
                    </surface>
                </newparam>
                <newparam sid="ID10">
                    <sampler2D>
                        <source>ID9</source>
                    </sampler2D>
                </newparam>
                <technique sid="COMMON">
                    <lambert>
                        <diffuse>
                            <texture texture="ID10" texcoord="UVSET0" />
                        </diffuse>
                    </lambert>
                </technique>
            </profile_COMMON>
        </effect>
        <effect id="ID20">
            <profile_COMMON>
                <newparam sid="ID22">
                    <surface type="2D">
                        <init_from>ID21</init_from>
                    </surface>
                </newparam>
                <newparam sid="ID23">
                    <sampler2D>
                        <source>ID22</source>
                    </sampler2D>
                </newparam>
                <technique sid="COMMON">
                    <lambert>
                        <diffuse>
                            <texture texture="ID23" texcoord="UVSET0" />
                        </diffuse>
                    </lambert>
                </technique>
            </profile_COMMON>
        </effect>
        <effect id="ID33">
            <profile_COMMON>
                <newparam sid="ID35">
                    <surface type="2D">
                        <init_from>ID34</init_from>
                    </surface>
                </newparam>
                <newparam sid="ID36">
                    <sampler2D>
                        <source>ID35</source>
                    </sampler2D>
                </newparam>
                <technique sid="COMMON">
                    <lambert>
                        <diffuse>
                            <texture texture="ID36" texcoord="UVSET0" />
                        </diffuse>
                    </lambert>
                </technique>
            </profile_COMMON>
        </effect>
        <effect id="ID46">
            <profile_COMMON>
                <newparam sid="ID48">
                    <surface type="2D">
                        <init_from>ID47</init_from>
                    </surface>
                </newparam>
                <newparam sid="ID49">
                    <sampler2D>
                        <source>ID48</source>
                    </sampler2D>
                </newparam>
                <technique sid="COMMON">
                    <lambert>
                        <diffuse>
                            <texture texture="ID49" texcoord="UVSET0" />
                        </diffuse>
                    </lambert>
                </technique>
            </profile_COMMON>
        </effect>
        <effect id="ID59">
            <profile_COMMON>
                <newparam sid="ID61">
                    <surface type="2D">
                        <init_from>ID60</init_from>
                    </surface>
                </newparam>
                <newparam sid="ID62">
                    <sampler2D>
                        <source>ID61</source>
                    </sampler2D>
                </newparam>
                <technique sid="COMMON">
                    <lambert>
                        <diffuse>
                            <texture texture="ID62" texcoord="UVSET0" />
                        </diffuse>
                    </lambert>
                </technique>
            </profile_COMMON>
        </effect>
    </library_effects>
    <library_images>
        <image id="ID8">
            <init_from>../materials/textures/side2.png</init_from>
        </image>
        <image id="ID21">
            <init_from>../materials/textures/front.png</init_from>
        </image>
        <image id="ID34">
            <init_from>../materials/textures/front.png</init_from>
        </image>
        <image id="ID47">
            <init_from>../materials/textures/Kraft_Tile.jpg</init_from>
        </image>
        <image id="ID60">
            <init_from>../materials/textures/side1.png</init_from>
        </image>
    </library_images>
    <scene>
        <instance_visual_scene url="#ID1" />
    </scene>
</COLLADA>
