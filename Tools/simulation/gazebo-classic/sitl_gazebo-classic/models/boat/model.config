<?xml version="1.0"?>
<model>
  <name>Boat</name>
  <version>1.0</version>
  <sdf version='1.5'>boat.sdf</sdf>
  <license>Apache 2.0</license>
  <url type="website">http://wiki.ros.org/wamv_description</url>
  <author email="brians<PERSON><PERSON>@gmail.com"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <description>
    This is a model of a boat model based on WAM-V. The implementation is from [osrf/vrx](https://bitbucket.org/osrf/vrx/)
  </description>
</model>
