name: MAVROS Offboard Tests

on:
  push:
    branches:
    - 'main'
    paths-ignore:
      - 'docs/**'
      - '.github/**'
  pull_request:
    branches:
    - '*'
    paths-ignore:
      - 'docs/**'
      - '.github/**'

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      ACTIONS_ALLOW_USE_UNSECURE_NODE_VERSION: true
    strategy:
      fail-fast: false
      matrix:
        config:
          - {test_file: "mavros_posix_tests_offboard_posctl.test",    vehicle: "iris"}

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Build PX4 and Run Tests
      uses: addnab/docker-run-action@v3
      with:
        image: px4io/px4-dev-ros-melodic:2021-09-08
        options: -v ${{ github.workspace }}:/workspace
        run: |
          cd /workspace
          git config --global --add safe.directory /workspace
          make px4_sitl_default
          make px4_sitl_default sitl_gazebo-classic
          ./test/rostest_px4_run.sh ${{matrix.config.test_file}} vehicle:=${{matrix.config.vehicle}}
