# Режими польоту (Фіксоване крило)

Режими польоту забезпечують автопілот підтримку для полегшення ручного польоту транспортного засобу, автоматизацію загальних завдань, таких як зльот та посадка, виконання автономних місій або делегування керування польотом зовнішній системі.

Ця тема надає загальний огляд доступних режимів польоту для фіксованих крил (літаків).

## Загальний огляд

Режими польоту можуть бути або _ручними_, або _автономними_.
Ручні режими забезпечують різні рівні підтримки автопілота при ручному керуванні (використовуючи керувальні палиці дистанційного керування або джойстик), тоді як _автономні_ режими є повністю контрольованими автопілотом.

Manual-Easy:

- [Режим позиції](../flight_modes_fw/position.md) — Найпростіший і найбезпечніший ручний режим для транспортних засобів, які мають фіксацію позиції/GPS.
  Транспортний засіб виконує [координований поворот](https://en.wikipedia.org/wiki/Coordinated_flight), якщо руль не є нульовим, тоді як pitch-стік контролює швидкість підйому/спуску.
  Якщо стіки відпущені, транспортний засіб вирівнюється і тримає прямий шлях польоту, навіть проти вітру.
  Швидкість активно контролюється, якщо встановлений датчик швидкості.
- [Режим висоти](../flight_modes_fw/altitude.md) — Найпростіший і найбезпечніший _непідтримуваний GPS_ ручний режим.
  Єдина відмінність порівняно з _Режимом положення_ полягає в тому, що пілот завжди безпосередньо керує кутом кочення літака і немає автоматичного утримання курсу.
- [Режим стабілізації](../flight_modes_fw/stabilized.md) — Пілот напряму керує кутом крену та тангажу, і апарат зберігає задану точку до тих пір, поки стіки знову не будуть переміщені.
  Тяга безпосередньо встановлюється пілотом.
  Координація повороту все ще обробляється контролером.
  Висота і швидкість повітря не контролюються, зокрема, це відповідальність пілота не допустити падіння транспортного засобу.

Manual-Acrobatic

- [Acro режим](../flight_modes_fw/acro.md) — Ручний режим для виконання акробатичних маневрів, таких як перекиди і сальто, стійки та акробатичні фігури.
  Ручки крена, тангажу та рискання керують швидкістю кутового обертання навколо відповідних вісей, а керування тяги безпосередньо передається до розподілу керування. Коли стіки будуть відцентровані, апарат перестане обертатися, але залишиться у своїй поточній орієнтації (на боці, перевернутий, тощо) і рухатиметься відповідно до свого поточного імпульсу.
- [Ручне керування](../flight_modes_fw/manual.md) — Найскладніший ручний режим польоту.
  Це надсилає введення стіки безпосередньо до розподілу керування для "повного" ручного управління.
  Не використовується зворотний зв'язок датчика для компенсації перешкод.

Автономний:
Усі автономні режими польоту потребують дійсної оцінки положення (GPS).
Швидкість активно контролюється, якщо встановлений датчик швидкості повітря в будь-якому автономному режимі польоту.

- [Утримання](../flight_modes_fw/hold.md) — Літак кружляє навколо позиції утримання GPS на поточній висоті.
  Режим може бути використаний для призупинення місії або для допомоги у відновленні контролю над транспортним засобом у випадку надзвичайної ситуації.
  Це може бути активовано з попередньо налаштованим RC вимикачем або кнопкою паузи QGroundControl.
- [Повернення](../flight_modes_fw/return.md) — Транспортний засіб летить по чіткій траєкторії для посадки в безпечному місці.
  За замовчуванням призначенням є місіонний зразок посадки.
  Режим можна активувати вручну (через попередньо програмований RC перемикач) або автоматично (тобто в разі спрацювання аварійного режиму).
- [Місія](../flight_modes_fw/mission.md) — Транспортний засіб виконує [передбачений план місії/польоту](../flying/missions.md), який був завантажений до керуючого пристрою польоту.
- [Зліт](../flight_modes_fw/takeoff.md) — Транспортний засіб ініціює послідовність підйому, використовуючи лише _режим запуску за допомогою катапульта або запуску вручну_ або _режим зльоту зі злітної смуги_ (у поточному напрямку).
- [Посадка](../flight_modes_fw/land.md) — Вертикальний апарат активує послідовність [посадки планера](../flight_modes_fw/mission.md#mission-landing).
- [Офборд](../flight_modes_fw/offboard.md) — Транспортний засіб слідкує за встановленими точками орієнтації, що надаються через MAVLink або ROS 2.

Пілоти переходять між режимами польоту за допомогою перемикачів на дистанційному керуванні або зі станції земного керування (див. [Конфігурацію режиму польоту](../config/flight_mode.md)).
Деякі режими польоту мають сенс тільки при певних передпольотних та польотних умовах (наприклад, блокування GPS, датчик швидкості повітря, виявлення орієнтації транспортного засобу по вісі).
PX4 не дозволить переходити до цих режимів, доки не будуть виконані відповідні умови.

Щоб отримати детальну технічну інформацію, виберіть розділи бічної панелі, що відносяться до конкретного режиму.

## Подальша інформація

- [Базова конфігурація > Режими польоту](../config/flight_mode.md) - Як відповідати перемикачі керування RC конкретним режимам польоту
- [Flight Modes (Multicopter)](../flight_modes_mc/index.md)
- [Flight Modes (VTOL)](../flight_modes_vtol/index.md)
- [Drive Modes (Differential Rover)](../flight_modes_rover/differential.md)
- [Drive Modes (Ackermann Rover)](../flight_modes_rover/ackermann.md)