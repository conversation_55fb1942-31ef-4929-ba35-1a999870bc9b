# Holybro XBP9X Telemetry Radio (Discontinued)

Holybro XBP9X radio can be configured easily using Digi's free XCTU software or via Digi's simplified AT or API command sets.
The radio provides secure, reliable delivery of critical data between devices with 256-bit AES encryption.

![Holybro XBP9X Radio](../../assets/hardware/telemetry/holybro-xbp9x.jpg)

With RF line-of-sight ranging up to 65 miles/100km and strong interference blocking capability, the radios are ideal for those applications requiring the combination of range, data redundancy and data reliability.

**Features:**

- USB Type-C port, integrated USB to UART converter
- 6-position JST-GH connector, can be directly connected to pixhawk4 flight controller
- High voltage BEC on board, Support DC7~35V voltage supply
- Can be powered by USB or flight controller in 100mW transmit power mode
- UART transmission LED indicator
- Three-stage RSSI LED indicator
- 256-bit AES encryption for secure data communications
- DigiMesh networking topology for redundancy and reliability
- Fully certified for use in unlicensed 900 MHz band

## 购买渠道

Discontinued (no longer available at Holybro).

## 产品规格

![Holybro XBP9X Radio](../../assets/hardware/telemetry/holybro-xbp9x-spec.png)
