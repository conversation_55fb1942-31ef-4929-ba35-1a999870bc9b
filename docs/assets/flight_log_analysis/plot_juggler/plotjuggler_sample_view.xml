<?xml version='1.0' encoding='UTF-8'?>
<root>
 <tabbed_widget parent="main_window" name="Main Window">
  <Tab containers="1" tab_name="tab1">
   <Container>
    <DockSplitter count="1" orientation="-" sizes="1">
     <DockSplitter count="2" orientation="|" sizes="0.500244;0.499756">
      <DockSplitter count="2" orientation="-" sizes="0.500392;0.499608">
       <DockArea name="...">
        <plot style="Lines" mode="TimeSeries" flip_y="false" flip_x="false">
         <range bottom="-1.050000" left="0.758590" right="494.757823" top="1.050000"/>
         <limitY/>
         <curve color="#1f77b4" name="actuator_controls_0/control.00"/>
         <curve color="#d62728" name="actuator_controls_0/control.01"/>
         <curve color="#1ac938" name="actuator_controls_0/control.03"/>
         <curve color="#ff7f0e" name="actuator_controls_0/control.04"/>
         <curve color="#f14cc1" name="actuator_controls_0/control.05"/>
         <curve color="#9467bd" name="actuator_controls_0/control.06"/>
         <curve color="#17becf" name="actuator_controls_0/control.02"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot style="Lines" mode="TimeSeries" flip_y="false" flip_x="false">
         <range bottom="5.572706" left="0.758590" right="494.757823" top="24.017403"/>
         <limitY/>
         <curve color="#1f77b4" name="battery_status/average_power"/>
         <curve color="#d62728" name="battery_status/current_a"/>
         <curve color="#1ac938" name="battery_status/voltage_v"/>
        </plot>
       </DockArea>
      </DockSplitter>
      <DockSplitter count="2" orientation="-" sizes="0.500392;0.499608">
       <DockArea name="...">
        <plot style="Lines" mode="XYPlot" flip_y="false" flip_x="false">
         <range bottom="-7.444404" left="-39.694462" right="56.224607" top="45.888190"/>
         <limitY/>
         <curve color="#bcbd22" curve_x="vehicle_local_position/x" name="vehicle_local_position/[x;y]" curve_y="vehicle_local_position/y"/>
        </plot>
       </DockArea>
       <DockArea name="...">
        <plot style="Lines" mode="TimeSeries" flip_y="false" flip_x="false">
         <range bottom="-2.590211" left="0.758590" right="494.757823" top="2.209112"/>
         <limitY/>
         <curve color="#ff7f0e" name="sensor_gyro.00/z"/>
        </plot>
       </DockArea>
      </DockSplitter>
     </DockSplitter>
    </DockSplitter>
   </Container>
  </Tab>
  <currentTabIndex index="0"/>
 </tabbed_widget>
 <use_relative_time_offset enabled="1"/>
 <!-- - - - - - - - - - - - - - - -->
 <!-- - - - - - - - - - - - - - - -->
 <Plugins>
  <plugin ID="DataLoad CSV">
   <default delimiter="0" time_axis=""/>
  </plugin>
  <plugin ID="DataLoad Parquet">
   <default dateFromat="false" prevTimestamp="" radioIndexChecked="0" parseDateTime="0"/>
  </plugin>
  <plugin ID="DataLoad ULog"/>
  <plugin ID="MQTT Subscriber (Mosquitto)"/>
  <plugin ID="UDP Server"/>
  <plugin ID="WebSocket Server"/>
  <plugin ID="Fast Fourier Transform"/>
  <plugin ID="Quaternion to RPY"/>
  <plugin ID="Reactive Script Editor">
   <library code="--[[ Helper function to create a ScatterXY series from arrays.&#xa;&#xa; series_name: name of the created ScatterXY series&#xa; prefix:      prefix of the timeseries, before the index of the array&#xa; suffix_X:   suffix to complete the name of the series containing the X value.&#xa;             if [nil], the index of the array will be used.&#xa; suffix_Y:   suffix to complete the name of the series containing the Y value&#xa; timestamp:   usually the tracker_time variable&#xa;              &#xa; Example:&#xa; &#xa; Assuming we have multiple series in the form:&#xa; &#xa;   /trajectory/node.{N}/position/x&#xa;   /trajectory/node.{N}/position/y&#xa;   &#xa; where {N} is the index of the array (integer). We can create a reactive series from the array with:&#xa; &#xa;   CreateSeriesFromArray( &quot;my_trajectory&quot;, &quot;/trajectory/node&quot;,  &quot;position/x&quot;, &quot;position/y&quot;, tracker_time );&#xa;]]--&#xa;&#xa;function CreateSeriesFromArray( series_name, prefix, suffix_X, suffix_Y, timestamp )&#xa;  --- create a new series or overwite the previous one&#xa;  new_series = MutableScatterXY.new(series_name)&#xa;  &#xa;  --- Append points to new_series&#xa;  index = 0&#xa;  while(true) do&#xa;&#xa;    x = index;&#xa;    -- if not nil, get the X coordinate from a series&#xa;    if suffix_X ~= nil then &#xa;      series_x = TimeseriesView.find( string.format( &quot;%s.%d/%s&quot;, prefix, index, suffix_X) )&#xa;      if series_x == nil then break end&#xa;      x = series_x:atTime(timestamp)&#x9; &#xa;    end&#xa;    &#xa;    series_y = TimeseriesView.find( string.format( &quot;%s.%d/%s&quot;, prefix, index, suffix_Y) )&#xa;    if series_x == nil then break end &#xa;    y = series_y:atTime(timestamp)&#xa;    &#xa;    new_series:push_back(x,y)&#xa;    index = index+1&#xa;  end&#xa;end&#xa;"/>
   <scripts/>
  </plugin>
  <plugin ID="CSV Exporter"/>
  <plugin ID="Video Viewer">
   <config use_frame="true" video_file="" curve_name=""/>
  </plugin>
 </Plugins>
 <!-- - - - - - - - - - - - - - - -->
 <previouslyLoaded_Datafiles>
  <fileInfo prefix="" filename="Boat Support/220728_ZurchLake_RateControl_Test/log_6_SecondRateCtrlTest_PGain_AddedToo_MaxRateLowered_2022-7-28-13-31-16.ulg">
   <selected_datasources value=""/>
   <plugin ID="DataLoad ULog"/>
  </fileInfo>
 </previouslyLoaded_Datafiles>
 <!-- - - - - - - - - - - - - - - -->
 <customMathEquations/>
 <snippets/>
 <!-- - - - - - - - - - - - - - - -->
</root>

