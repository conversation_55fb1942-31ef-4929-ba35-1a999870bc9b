# Hardware Hardware Selection & Setup

This section contains information the components that might be used in a drone, and how they are set up.

- [Flight Controllers (Autopilots)](../flight_controller/index.md) - Pixhawk and other FCs, firmware updates, other bootloaders
- [Sensors](../sensor/index.md) — accelerometer, gyroscope, compass, airspeed, barometer, rangefinders, GNSS, RTK GNSS, Optical Flow, Tachometers, Factory calibration, thermal calibration.
- [Actuators](../actuators/index.md) — Allocation, ESC, motors, servos
- [Radio Control (RC)](../getting_started/rc_transmitter_receiver.md) — Manual control using an RC system
- [Joysticks](../config/joystick.md) — Manual control using a Joystick connected to QGroundControl
- [Data Links](../data_links/index.md) — MAVLink, telemetry radios, satellite comms
- [Power Systems](../power_systems/index.md) — Battery estimation tuning, power modules, smart batteries
- [Peripherals](../peripherals/index.md) — camera, gimbal, grippers, parachute, RemoteID, traffic avoidance etc.
- [I2C Peripherals](../sensor_bus/i2c_general.md)
- [CAN Peripherals](../can/index.md)
- [DroneCAN Peripherals](../dronecan/index.md)
- [Cable Wiring](../assembly/cable_wiring.md)
- [Companion Computers](../companion_computer/index.md) — Setup, peripherals, computer vision, etc.
- [Serial Port Configuration](../peripherals/serial_configuration.md)
- [PX4 Ethernet Setup](../advanced_config/ethernet_setup.md)
