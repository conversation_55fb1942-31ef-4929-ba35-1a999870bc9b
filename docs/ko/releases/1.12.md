# 출시 1.12

- [Release 1.12](#release-1-12)
  - [Pre Releases](#pre-releases)
  - [Changes](#changes)
    - [Common](#common)
    - [Sensors](#sensors)
    - [Hardware](#hardware)
    - [MAVLink](#mavlink)
    - [Commander](#commander)
    - [Multicopter](#multicopter)
    - [VTOL](#vtol)
    - [Control](#control)
    - [GPS](#gps)
    - [NuttX](#nuttx)
    - [UAVCAN](#uavcan)

## 사전 출시

- [Beta 4](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta4)
- [Beta 3](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta3)
- [Beta 2](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta2)
- [Beta 1](https://github.com/PX4/PX4-Autopilot/releases/tag/v1.12.0-beta1)

## 변경 사항

### 공통

- **RTL Trigger based on remaining flight range ([PR#16399](https://github.com/PX4/PX4-Autopilot/pull/16399))**
  - 기체 속도, 풍속 및 목적지 거리와 방향을 고려하여 RTL에서 집까지의 시간을 계산합니다.
- **Pre-emptive geofence breach ([PR#16400](https://github.com/PX4/PX4-Autopilot/pull/16400))**
  - Triggers a breach if the _predicted_ current trajectory will result in a breach, allowing the vehicle to be re-routed to a safe hold position.
- **Airframe Scripts**
  - 기본값 설정 구문이 변경되었으며 사용자 지정 스크립트를 업데이트하여야 합니다.
  - See [PR#16796](https://github.com/PX4/PX4-Autopilot/pull/16796/files#diff-dcf2f5536f47f260e5e0ff3b3fd22eaef6b6c510126463d70affa0eb7bd4d3ddL20) for an example.
- 안전 스위치는 기본적으로 꺼짐으로 설정(모터는 시동 해제되지만 서보/플랩은 움직일 수 있음)
- 안전 스위치가 잠깁니다. 일단 비활성화되면 비활성화 상태로 유지됩니다.
- 착륙 감지기 : 거리 센서가있는 경우지면 거리를 사용하도록 착륙 감지를 확장합니다.
- 텔레메트리 포함 IRC Ghost에 대한 지원 추가

### 센서

- 더 빠르고 안정적인 자력계 보정
  - 새로운 연철 교정 계수
  - 외부 센서의 회전을을 자동으로 결정
- 최적화된 속도 제어 센서 파이프 라인 (최소 내부 루프 종단간 지연 시간)

### 하드웨어

이 릴리스에는 아래의 보드, 주변 장치, 액세서리 및 신규 하드웨어 지원이 포함됩니다.

- Pixhawk FMUv6U (Read more about this spec on the [Pixhawk GitHub Repository](https://github.com/pixhawk/Pixhawk-Standards))
- Pixhawk FMUv6X (Read more about this spec on the [Pixhawk GitHub Repository](https://github.com/pixhawk/Pixhawk-Standards))
- CUAV X7 / X7Pro ([Read more about this product on the manufacturers site](http://www.cuav.net/en/x7en/)]
- CUAV Nora ([Read more about this product on the manufacturers site](http://www.cuav.net/en/nora/))
- CUAV CAN GPS ([Read more about this product on the manufacturers site](http://www.cuav.net/en/neo-3-2/))
- SP Racing H7 Extreme ([Read more about this product on the manufacturers site](http://seriouslypro.com/spracingh7extreme))
- Bitcraze Crazyflie v2.1 ([Read more about this product on the manufacturers site](https://www.bitcraze.io/products/crazyflie-2-1/))
- ARK CAN Flow ([Read more about this product on the manufacturers site](https://arkelectron.com/product/ark-flow/))
- mRo Ctrl Zero H7 (Experimental) ([Read more about this product on the manufacturers site](https://store.mrobotics.io/mRo-Control-Zero-F7-p/mro-ctrl-zero-f7.htm))

다음 내용들이 제거됩니다.

- 단종된 Intel AeroFC 제거

### MAVLink

- **MAVLink Ethernet configuration ([PR#14460](https://github.com/PX4/PX4-Autopilot/pull/14460))**
  - UDP 포트, 원격 포트 및 브로드캐스트 모드와 같은 MAVLink 이더넷 설정은 이제 매개변수를 통해 동적으로 변경할 수 있습니다.
- **Support for querying `COMPONENT_INFORMATION` ([PR#16039](https://github.com/PX4/PX4-Autopilot/pull/16039))**
  - 이제 매개변수 메타데이터가 매일 QGC와 함께 자동으로 동기화됩니다.
  - 이 새로운 메시지를 통하여 모든 MAVLink 시스템은 자동조종장치에서 풍부한 계층 정보를 요청할 수 있습니다. 즉, 임무에서 지원되는 명령을 이해하거나, 매개변수 메타데이터를 얻을 수 있습니다. This message was introduced primarily to help GCS better understand autopilots (RFC: [mavlink#1339](https://github.com/mavlink/mavlink/issues/1339))

### 사령관(Commander)

- **Commander: use control mode flags and cleanup arm/disarm ([PR#16266](https://github.com/PX4/PX4-Autopilot/pull/16266))**
  - Consolidate scattered arming requirements in arm_disarm(), and, keeps the `vehicle_control_mode` last state in commander
- **Commander: Separate out manual control setpoint processing ([PR#16878](https://github.com/PX4/PX4-Autopilot/pull/16878))**
  - Adds a new class `ManualControl` for handling `manual_control_setpoint` and handles RC loss, RC override, and RC arming/disarming

### 멀티콥터

- **More intuitive stick feel in Position mode**

  - 속도 설정값 대신 가속에 매핑된 수평 스틱 입력
  - 이동 속도 속도에 도달시의 갑작스런 기울기 변화를 제거합니다.
  - 직관적인 단락(예: 착륙시)
  - Opt out possible using [MPC_POS_MODE](../advanced_config/parameter_reference.md#MPC_POS_MODE)
  - Development: [First attempt](https://github.com/PX4/PX4-Autopilot/pull/12072), [Introduction](https://github.com/PX4/PX4-Autopilot/pull/16052), [Improvements](https://github.com/PX4/PX4-Autopilot/pull/16320), [Bugfix zero oscillation](https://github.com/PX4/PX4-Autopilot/pull/16786), [Bugfix position unlock](https://github.com/PX4/PX4-Autopilot/pull/16791), [Bugfix invalid setpoint](https://github.com/PX4/PX4-Autopilot/pull/17078), [Bugfix high velocity pre takeoff](https://github.com/PX4/PX4-Autopilot/pull/17437)

- **Hover thrust independent velocity control gains**

  - Parameters `MPC_{XY/Z}_VEL_{P/I/D}` were replaced with `MPC_{XY/Z}_VEL_{P/I/D}_ACC`, see:
    [MPC_XY_VEL_P_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_P_ACC), [MPC_XY_VEL_I_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_I_ACC), [MPC_XY_VEL_D_ACC](../advanced_config/parameter_reference.md#MPC_XY_VEL_D_ACC), [MPC_Z_VEL_P_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_P_ACC), [MPC_Z_VEL_I_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_I_ACC), [MPC_Z_VEL_D_ACC](../advanced_config/parameter_reference.md#MPC_Z_VEL_D_ACC)

    :::warning
    이득은 새로운 의미를 가지게 되었습니다.

    - $m/s$의 속도 오류에서 $m/s^2$의 가속 출력으로 확장
    -
    - Automatic parameter transition assumes 50% hover thrust: `~10m/s^2 / 50% = 20 m/s^2`.
      See [question](https://github.com/PX4/PX4-Autopilot/pull/14823#issuecomment-791357646)

:::

  - Development: [Logic introduction](https://github.com/PX4/PX4-Autopilot/pull/14749), [Parameter replacement](https://github.com/PX4/PX4-Autopilot/pull/14823)

- **Improve Rounded Turns ([PR#16376](https://github.com/PX4/PX4-Autopilot/pull/16376))**
  - 멀티로터 임무의 웨이포인트에서 좀 더 큰 둥근 회전을 생성합니다 (코너에서 L1 스타일 안내 로직 사용).
  - See [Mission Mode > Inter-waypoint Trajectory](../flight_modes_fw/mission.md#rounded-turns-inter-waypoint-trajectory) and [Mission > Setting Acceptance/Turning Radius](../flying/missions.md#setting-acceptance-turning-radius)

- **Removal of Rattitude flight mode ([PR#17019](https://github.com/PX4/PX4-Autopilot/pull/17019))**
  - 다시 원하시면 저희에게 알려주십시오.

### 수직이착륙기(VTOL)

- **RTL improvements ([PR#16377](https://github.com/PX4/PX4-Autopilot/pull/16377))**
  - Hardens the RTL safety failsafes taking into consideration the many edge cases when trying to land, depending on the current vehicle mode (Multicopter vs Fixed-wing)
- Fixed-wing / VTOL significant TECS improvements

### 제어

- **Dynamic Notch Filter updated with Gyro FFT ([PR#16385](https://github.com/PX4/PX4-Autopilot/pull/16385))**
  - 자이로 제어 데이터에 동적 노치 필터링을 추가하여 제어가 훨씬 부드러워졌습니다.
- **Multi-EKF enabled by default** on stm32f7 and stm32h7 boards

### GPS

- The GPS protocol now defaults to u-blox for faster startup, and [GPS_x_PROTOCOL](../advanced_config/parameter_reference.md#GPS_1_PROTOCOL) needs to be changed if another GPS is used.

### NuttX

Nuttx was upgraded from [8.2+ to NuttX 10.10.0+](https://github.com/apache/incubator-nuttx/compare/nuttx-8.2..nuttx-10.0.1) (@ [904a602c74dc08a100b5c2bd490807de19e73e10](https://github.com/apache/incubator-nuttx/commit/904a602c74dc08a100b5c2bd490807de19e73e10))

- **SDCARD performance:** Results in better performance on H7 Targets
  - [**BACKPORT**] stm32:SDIO:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] stm32h7:SDMMC:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] stm32f7:SDMMC:Use 250 Ms Data path timeout, regardless of Card Clock frequency
  - [**BACKPORT**] Fixes race condition in event wait logic of SDMMC driver.
  - [**BACKPORT**] mmcsd:Stuck in 1-bit mode, Removed CONFIG_ARCH_HAVE_SDIO_DELAYED_INVLDT

- **Ethernet stability:**
  - [**BACKPORT**] stm32x7:Ethernet Fixed hardfaults, from too big frames
  - [**BACKPORT**] stm32:Ethernet Fix too big frames

- **Boot up stability** V5-V6X ensuring the LSE (RTC) oscillator is started

  - [**BACKPORT**] stm32h7:lse fix Kconfig help text
  - [**BACKPORT**] stm32f7:lse Use Kconfig values directly
  - [**BACKPORT**] stm32h7:Add DBGMCU
  - [**BACKPORT**] stm32f7:Add option to auto select LSE CAPABILITY
  - [**BACKPORT**] stm32h7:Add option to auto select LSE CAPABILITY

    ::: info
    This Knob will cycle through the correct\*
    values from low to high. To avoid damaging
    the crystal. We want to use the lowest setting
    that gets the OSC running. See app note AN2867
    \*It will take into account the rev of the silicon
    and use the correct code points to achieve the drive
    strength. See Eratta ES0392 Rev 7 2.2.14 LSE oscillator
    driving capability selection bits are swapped.

:::

- **Driver changes**

  - [**BACKPORT**] drivers/serial: fix Rx interrupt enable for cdcacm

  - [**BACKPORT**] binnfmt:Fix return before close ELF fd

  - [**BACKPORT**] stm32f7:Allow for reuse of the OTG_ID GPIO

  - [**BACKPORT**] stm32f7:SDMMC fix reset of do_gpio

  - [**BACKPORT**] stm32h7: serial: use dma tx semaphore as resource holder

  - [**BACKPORT**] stm32h7:SDMMC fix reset of do_gpio

  - [**BACKPORT**] stm32h7:Serial Add RX and TX DMA

  - [**BACKPORT**] stm32h7:Allow for reuse of the OTG_ID GPIO

  - [**BACKPORT**] Kinetis:kinetis:Replace DMA

  - [**BACKPORT**] kinetis:Serial use eDMA

  - [**BACKPORT**] kinetis:SPI use eDMA

  - [**BACKPORT**] Kinetis:Serail No DMA Poll needed

  - [**BACKPORT**] libc/stdio: Preallocate the stdin, stdout and stderr
    For targets without consoles.

- **FlexCan fixes**
  - [**BACKPORT**][flexcan] Correct reset state for CTRL1 register
  - [**BACKPORT**][flexcan] Fix TX drop #2792 and correctly set CAN timings to non-zeroed registers
  - [**BACKPORT**] FlexCAN Fix TX abort process

- **Support for CAN bootloader**
  - [**BACKPORT**] s32k1xx:Support ramfunc

- **STM32F412 cleanup**

- [**BACKPORT**] stm32f412:Corrected Pin count

- [**BACKPORT**] stm32f412:Replaced Kludged pinmap with one for SoC

- [**BACKPORT**] stm32412: Fixes pinmap CAN1

- **Security patches**

- [**BACKPORT**] tcp: Remove incomplete support for TCP reassembly

- [**BACKPORT**] net/tcp/tcp_input.c: Correct bad check of urgent data length

- [**BACKPORT**] libc: Add additional checks to malloc realloc and memalign

- **IMXRT fixes**

- Add Single wire and proper parity settings to IMXRT to support sbus etal.

- [**BACKPORT**] imxrt:serial support single-wire mode

- [**BACKPORT**] imxrt:imxrt_lowputc Fixed parity settings.

- **STM32H7 improvements**

- [**BACKPORT**] stm32h7:SPI Fix 16 bit SPI mode

- [**BACKPORT**] stm32h7:DMA BDMA does not auto disabled on completion

- [**BACKPORT**] Fix HEAP clobbering static data in SRAM4

- [**BACKPORT**] stm32h7:SDMMC fix reset of do_gpio

### UAVCAN

- UAVCANv0 : 펌웨어 업그레이드와 CAN 노드의 매개변수 동기화와 같은 기본 기능이 5 년 이상 구현되었지만, 이제는 장치가 시장에 출시되어 새롭게 지원합니다. 일반적인 CAN GPS, 대기 속도 및 전력 모듈이 지원됩니다.
- UAVCANv0 노드 : PX4는 수년 동안 빌딩 노드를 지원하였습니다. 이제 CUAV GPS 장치와 같은 특정 타겟 구축을 지원합니다.
- UAVCANv1 : 완전한 종단간 구현의 초기 알파
