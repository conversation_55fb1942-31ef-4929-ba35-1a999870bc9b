<?xml version="1.0" ?>
<sdf version="1.5">
  <model name="foggy_lidar">
    <link name="link">

      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.19</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>

      <visual name="visual">
        <geometry>
          <box>
            <size>0.02 0.05 0.05</size>
          </box>
        </geometry>
      </visual>

      <sensor name="laser" type="ray">
        <pose>0 0 0 0 1.51 0</pose>
        <always_on>1</always_on>
        <update_rate>5.5</update_rate>
        <visualize>true</visualize>
        <ray>
          <scan>
            <horizontal>
              <samples>360</samples>
              <resolution>1</resolution>
              <min_angle>-3.14</min_angle>
              <max_angle>3.14</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.06</min>
            <max>120</max>
            <resolution>0.05</resolution>
          </range>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>1.0</stddev>
          </noise>
        </ray>
        <plugin name="LaserPlugin"
          filename="libgazebo_lidar_plugin.so">
          <robotNamespace></robotNamespace>
          <min_distance>0.1</min_distance>
          <max_distance>100.0</max_distance>
          <simulate_fog>true</simulate_fog>
        </plugin>
      </sensor>
    </link>
  </model>
</sdf>

<!-- vim: set et fenc= ff=unix sts=0 sw=2 ts=2 : -->
