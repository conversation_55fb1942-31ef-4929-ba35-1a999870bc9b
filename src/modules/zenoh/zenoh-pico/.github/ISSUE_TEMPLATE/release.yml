name: Add an issue to the next release
description: |
  Add an issue as part of next release. 
  This will be added to the current release project. 
  You must be a contributor to use this template.
labels: ["release"]
body:
  - type: markdown
    attributes:
      value: |
        **Guidelines for a good issue**

        *Is your release item related to a problem?*
        A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

        *Describe the solution you'd like*
        A clear and concise description of what you want to happen.

        *Describe alternatives you've considered*
        A clear and concise description of any alternative solutions or features you've considered.

        *Additional context*
        Add any other context about the release item request here.
  - type: textarea
    id: item
    attributes:
      label: "Describe the release item"
    validations:
      required: true
