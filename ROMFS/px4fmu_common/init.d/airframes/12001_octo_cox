#!/bin/sh
#
# @name Generic 10" Octo coaxial geometry
#
# @type Octorotor Coaxial
# @class Copter
#
# @output Motor1 motor 1
# @output Motor2 motor 2
# @output Motor3 motor 3
# @output Motor4 motor 4
# @output Motor5 motor 5
# @output Motor6 motor 6
# @output Motor7 motor 7
# @output Motor8 motor 8
#
# @maintainer <PERSON><PERSON><PERSON> <<EMAIL>>
#
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.mc_defaults

param set-default MAV_TYPE 14

param set-default CA_ROTOR_COUNT 8
param set-default CA_ROTOR0_PX 0.35
param set-default CA_ROTOR0_PY 0.35
param set-default CA_ROTOR0_PZ -0.05
param set-default CA_ROTOR1_KM -0.05
param set-default CA_ROTOR1_PX 0.35
param set-default CA_ROTOR1_PY -0.35
param set-default CA_ROTOR1_PZ -0.05
param set-default CA_ROTOR2_PX -0.35
param set-default CA_ROTOR2_PY -0.35
param set-default CA_ROTOR2_PZ -0.05
param set-default CA_ROTOR3_PX -0.35
param set-default CA_ROTOR3_PY 0.35
param set-default CA_ROTOR3_PZ -0.05
param set-default CA_ROTOR3_KM -0.05
param set-default CA_ROTOR4_PX 0.35
param set-default CA_ROTOR4_PY -0.35
param set-default CA_ROTOR4_PZ 0.05
param set-default CA_ROTOR5_PX 0.35
param set-default CA_ROTOR5_PY 0.35
param set-default CA_ROTOR5_PZ 0.05
param set-default CA_ROTOR5_KM -0.05
param set-default CA_ROTOR6_PX -0.35
param set-default CA_ROTOR6_PY 0.35
param set-default CA_ROTOR6_PZ 0.05
param set-default CA_ROTOR7_KM -0.05
param set-default CA_ROTOR7_PX -0.35
param set-default CA_ROTOR7_PY -0.35
param set-default CA_ROTOR7_PZ 0.05
