# Інструмент автоматизації плагіну "Advanced Lift Drag" (AVL)

Модель рухомого засобу Gazebo [Поліпшений літак](../sim_gazebo_gz/vehicles.md#advanced-plane) використовує плагін _Advanced Lift Drag_ для моделювання поведінки піднімної сили та аеродинамічного опору рухомого засобу.
Цей інструмент дозволяє вам розрахувати параметри, необхідні для створення плагіну _Advanced Lift Drag_ для вашого певного рухомого засобу.

Вам  лише потрібно надати кілька параметрів для кожного крила, а інструмент використовуватиме цю інформацію для виклику моделі решітки вихрів "Athena" (Athena Lattice Vortex або AVL) для здійснення необхідних обчислень.
Результати будуть автоматично записані у шаблон плагіну, який потім можна скопіювати у файл моделі або sdf-файл світу.

## Встановлення

Щоб налаштувати інструмент:

1. Завантажте AVL 3.36 з <https://web.mit.edu/drela/Public/web/avl/>.
  Файл AVL для версії 3.36 можна знайти приблизно посередині сторінки.

2. Після завантаження розпакуйте AVL та перемістіть його в домашню директорію за допомогою:

  ```sh
  sudo tar -xf avl3.36.tgz
  mv ./Avl /home/
  ```

3. Дотримуйтесь **index.md**, що знаходиться у `./Avl` щоб завершити процес встановлення AVL (вимагає встановлення бібліотек `plotlib` та `eispack`).
  Ми рекомендуємо використовувати варіант компіляції "gfortran", який може далі вимагати, щоб ви встановили "gfortran".
  На Ubuntu це можна зробити виконавши:

  ```sh
  sudo apt update
  sudo apt install gfortran
  ```

  При запуску Makefile для AVL, ви можливо зіткнетесь з повідомленням `Error 1`, яке стверджує що відсутня якась директорія.
  Це не завадить AVL працювати для нашої мети.

Як тільки процес, описаний в AVL README буде завершений, AVL готовий до використання.
Зі сторони AVL та інструменту більше ніяких налаштувань не потрібно.

Якщо хочете змінити положення директорії AVL, це можна просто зробити передавши прапорець `--avl_path` до файлу `input_avl.py` та використавши бажану директорію для прапора (не забудьте додати "/" в останній частині шляху).
Запуск в такому вигляді також автоматично налаштує шляхі де необхідно.

## Запуск AVL

Зразковий шаблон наданий у вигляді `input.yml`, який реалізує стандартний літак з двома елеронами, рулем висоти та стерном.
Цей приклад може бути запущений так: `python input_avl.py --yaml_file input.yml`.

Для запуску інструмента для вашого літака:

1. Скопіюйте приклад `input.yml` до `<your_custom_yaml_file>.yml` і змініть його, щоб підставити бажаний літак

2. Запустіть інструмент на вашому yml-файлі:

  ```sh
  python input_avl.py <your_custom_yaml_file>.yml
  ```

  Зверніть увагу, що пакети `yaml` та `argparse` повинні бути присутні в середовищі Python.

3. Інструмент очікує діапазон певних параметрів рухомого засобу, які потрібні щоб вказати геометрію та фізичні властивості літака.
  Ви можете або:
  - обрати попередньо визначений шаблон моделі (наприклад Cessna або ВЗІП), який має відому кількість поверхонь керування і просто змінити деяки фізичні властивості, або
  - визначити повністю довільну модель

Після виконання скрипту, згенеровані файли `.avl`, `.sdf` та креслення пропонованих поверхонь керування можна знайти у директорії `<your-plane-name>`.
Файл sdf - це згенерований плагін Advanced Lift Drag який може бути скопійовано і вставлено у файл model.sdf, який потім можна запустити у Gazebo.

## Функціонал

Файл **input_avl.py** приймає параметри надані користувачем та створює з цього файл .avl, який потім може бути прочитаний AVL (програмою).
Це відбувається у файлі **process.sh**.

Вивід згенерований AVL буде збережено у два файли: **custom_vehicle_body_axis_derivatives.txt** та **custom_vehicle_stability_derivatives.txt**.
Ці два файли містять параметри, які необхідні для заповнення плагіну Advanced Lift Drag.

І нарешті, **avl_out_parse.py** читає згенеровані файли .txt та присвоює параметри відповідним елементам у файлі sdf.

Згенерований плагін Advanced Lift Drag (`<custom_plane>.sdf`) може бути скопійований в певний файл **model.sdf**, що використовується Gazebo.

## Зручність використання

Поточна реалізація надає мінімальний робочий приклад.
Більш точні виміри можна отримати, підлаштувавши обрану кількість вихрів вздовж розмаху та хорди крила відповідно до бажаних налаштувань.
Тут можна знайти добру відправну точку: <https://www.redalyc.org/pdf/6735/673571173005.pdf>.

Також можна більш точно моделювати рухомий засіб, використовуючи більшу кількість секцій.
У поточному yml-файлі визначено лише лівий та правий край для кожної поверхні, що дає рівно одну секцію, але код підтримує розширення до будь-якої кількості бажаних секцій.

::: info

- Поверхня керування в AVL завжди визначається зліва направо.
  Це означає, що спочатку потрібно вказати лівий край поверхні, а потім правий.
  Якщо ви зробите це навпаки, поверхня фактично буде визначена догори дриґом.
- Інструмент призначений для підтримки не більше двох поверхонь керування будь-якого типу на будь-якому рухомому засобі.
  Маючи більше поверхонь може призвести до помилкової поведінки.
- Ще один важливий момент полягає в тому, що ці скрипти використовують синтаксис match, case, який був введений лише в Python версії 3.10.
- Основним довідковим ресурсом для AVL є <https://web.mit.edu/drela/Public/web/avl/AVL_User_Primer.pdf>.
  Цей документ був написаний творцями AVL і містить всі змінні, які можуть бути потрібні для визначення поверхонь керування.
- AVL не може передбачити значення звалювання, тому їх потрібно обчислити/оцінити іншим чином.
  У поточній реалізації значення звалювання за замовчуванням були взяті з моделі поліпшеного літака з PX4.
  Звичайно вони повинні бути змінені для нових/інших моделей.

:::

## Визначення параметрів

Нижче наведено вичерпний перелік того, як параметри визначаються на виході та з яких файлів у AVL вони беруться.
Плагін Advanced Lift Drag містить більше деталей про те, що роблять кожен з цих параметрів.

:::info
The parameters have not been verified by an expert, so you should check them in the plugin.
:::

З файлу журналу похідних стійкості беруться наступні параметри плагіну advanced lift drag:

| Ім'я в AVL | Ім'я в плагіні Advanced Lift Drag | Опис                                                                                  |
| ---------- | --------------------------------- | ------------------------------------------------------------------------------------- |
| Alpha      | alpha                             | Кут атаки                                                                             |
| Cmtot      | Cem0                              | Коефіцієнт моменту тангажу при нульовому куті атаки                                   |
| CLtot      | CL0                               | Коефіцієнт сили підйому при нульовому куті атаки                                      |
| CDtot      | CD0                               | Коефіцієнт аеродинамічного опору при нульовому куті атаки                             |
| CLa        | CLa                               | dCL/da (нахил кривої CL-alpha)                                     |
| CYa        | CYa                               | dCy/da (нахил бічної сили відносно кута атаки)                     |
| Cla        | Cell                              | dCl/da (нахил моменту крену відносно кута атаки)                   |
| Cma        | Cema                              | dCm/da (нахил моменту тангажу відносно кута атаки - до звалювання) |
| Cna        | Cena                              | dCn/da (нахил моменту рискання відносно кута атаки)                |
| CLb        | CLb                               | dCL/dbeta (нахил коефіцієнту сили підйому відносно кута ковзання)  |
| CYb        | CYb                               | dCY/dbeta (нахил бічної сили відносно кута ковзання)               |
| Clb        | Cell                              | dCl/dbeta (нахил моменту крену відносно кута ковзання)             |
| Cmb        | Cemb                              | dCm/dbeta (нахил моменту тангажу відносно кута ковзання)           |
| Cnb        | Cenb                              | dCn/dbeta (нахил моменту рискання відносно кута ковзання)          |

З файлу журналу похідних вісі тіла беруться наступні параметри плагіну advanced lift drag:

| Ім'я в AVL | Ім'я в плагіні Advanced Lift Drag | Опис                                                                                   |
| ---------- | --------------------------------- | -------------------------------------------------------------------------------------- |
| e          | eff                               | Ефективність крила (коефіцієнт ефективності Освальда для 3D крила)  |
| CXp        | CDp                               | dCD/dp (нахил коефіцієнту опору відносно швидкості крену)           |
| CYp        | CYp                               | dCY/dp (нахил бічної сили відносно швидкості крену)                 |
| CZp        | CLp                               | dCL/dp (нахил коефіцієнту сили підйому відносно швидкості крену)    |
| Clp        | Cellp                             | dCl/dp (нахил моменту крену відносно швидкості крену)               |
| Cmp        | Cemp                              | dCm/dp (нахил моменту тангажу відносно швидкості крену)             |
| Cmp        | Cenp                              | dCn/dp (нахил моменту рискання відносно швидкості крену)            |
| CXq        | CDq                               | dCD/dq (нахил коефіцієнту опору відносно швидкості тангажу)         |
| CYq        | CYq                               | dCY/dq (нахил бічної сили відносно швидкості тангажу)               |
| CZq        | CLq                               | dCL/dq (нахил коефіцієнту сили підйому відносно швидкості тангажу)  |
| Clq        | Cellq                             | dCl/dq (нахил моменту крену відносно швидкості тангажу)             |
| Cmq        | Cemq                              | dCm/dq (нахил моменту тангажу відносно швидкості тангажу)           |
| Cnq        | Cenq                              | dCn/dq (нахил моменту рискання відносно швидкості тангажу)          |
| CXr        | CDr                               | dCD/dr (нахил коефіцієнту опору відносно швидкості рискання)        |
| CYr        | CYr                               | dCY/dr (нахил бічної сили відносно швидкості рискання)              |
| CZr        | CLr                               | dCL/dr (нахил коефіцієнту сили підйому відносно швидкості рискання) |
| Clr        | Cellr                             | dCl/dr (нахил моменту крену відносно швидкості рискання)            |
| Cmr        | Cemr                              | dCm/dr (нахил моменту тангажу відносно швидкості рискання)          |
| Cnr        | Cenr                              | dCn/dr (нахил моменту рискання відносно швидкості рискання)         |

Крім того, кожна поверхня керування має шість власних параметрів, які також походять з цього файлу журналу.
`{i}` нижче знаходиться в діапазоні від 1 до кількості унікальних типів поверхні керування в моделі.

| Ім'я в AVL | Ім'я в плагіні Advanced Lift Drag | Опис                                                   |
| ---------- | --------------------------------- | ------------------------------------------------------ |
| CXd{i}     | CD_ctrl      | Вплив відхилення поверхні керування на опір            |
| CYd{i}     | CY_ctrl      | Вплив відхилення поверхні керування на бічну силу      |
| CZd{i}     | CL_ctrl      | Вплив відхилення поверхні керування на силу підйому    |
| Cld{i}     | Cell_ctrl    | Вплив відхилення поверхні керування на момент крену    |
| Cmd{i}     | Cem_ctrl     | Вплив відхилення поверхні керування на момент тангажу  |
| Cnd{i}     | Cen_ctrl     | Вплив відхилення поверхні керування на момент рискання |
