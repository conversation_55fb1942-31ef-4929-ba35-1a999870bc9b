<?xml version="1.0" ?>
<sdf version="1.5">
  <model name="EuroPallet">
    <!-- Rises the pallet above the ground -->
    <pose>0 0 0.0635  0 0 0</pose>
	
    <static>false</static>
    <link name="link">
      <inertial>
        <mass>25.0</mass>
        <inertia>
          <ixx>1</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>1</iyy>
          <iyz>0</iyz>
          <izz>1</izz>
        </inertia>
      </inertial>
	  
      <collision name="top">
	    <pose>0 0 0.0625  0 0 0</pose>
        <geometry>
		  <box>
		    <size>1.20 0.80 0.042</size>
	      </box>
        </geometry>
		
	<!--	<surface>
          <friction>
            <ode>
              <mu>1.0</mu>
              <mu2>1.0</mu2>
              <fdir1>0 0 1</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <contact>
            <ode>
              <min_depth>0.005</min_depth>
              <kp>1e8</kp>
            </ode>
          </contact>
        </surface> -->
		
      </collision>
	  
	  <!--
	  Column positions:
	  
	  _______________
	  | 1    2    3 |   ^
	  |             |   | Forward
	  |             |
	  | 4    5    6 |
	  |             |
	  |             |
	  | 7    8    9 |
	  |_____________|
	  
	  
	  
	  -->
	  
      <collision name="colum_1">
	    <pose>0.5275 -0.35 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.10 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="colum_2">
	    <pose>0.5275 0 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.145 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="colum_3">
	    <pose>0.5275 0.35 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.10 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="colum_4">
	    <pose>0 -0.35 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.10 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="colum_5">
	    <pose>0 0 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.145 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="colum_6">
	    <pose>0 0.35 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.10 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="colum_7">
	    <pose>-0.5275 -0.35 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.10 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="colum_8">
	    <pose>-0.5275 0 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.145 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="colum_9">
	    <pose>-0.5275 0.35 0  0 0 0</pose>
        <geometry>
		  <box>
		    <size>.145 0.10 0.085</size>
	      </box>
        </geometry>
      </collision>
	  
	  
	  <!--
	  Bottom positions:
	  
	  ______________
	  |1|  |2 |  |3|   ^
	  | |  |  |  | |   | Forward
	  | |  |  |  | |
	  | |  |  |  | |
	  | |  |  |  | |
	  | |  |  |  | |
	  | |  |  |  | |
	  |_|  |__|  |_|
	  
	  -->
	  
	  
      <collision name="bottom_1">
	    <pose>0 -0.35 -0.053  0 0 0</pose>
        <geometry>
		  <box>
		    <size>1.20 0.10 0.021</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="bottom_2">
	    <pose>0 0 -0.053  0 0 0</pose>
        <geometry>
		  <box>
		    <size>1.20 0.145 0.021</size>
	      </box>
        </geometry>
      </collision>
	  
      <collision name="bottom_3">
	    <pose>0 0.35 -0.053  0 0 0</pose>
        <geometry>
		  <box>
		    <size>1.20 0.10 0.021</size>
	      </box>
        </geometry>
      </collision>
	  
	  
      <visual name="visual">
        <geometry>
          <mesh>
            <uri>model://europallet/meshes/europallet.dae</uri>
          </mesh>
        </geometry>
      </visual>
    </link>
  </model>
</sdf>
