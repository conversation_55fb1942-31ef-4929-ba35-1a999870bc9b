<?xml version="1.0" encoding="utf-8"?>
<!--

Include this file on your JSBSim standalone application command line in order to send 
data to a running instance of FlightGear to command FlightGear to display
visuals for your running script.

Start FlightGear like this:

fgfs.exe -fg-root="{your path}" -aircraft=c172p -native-fdm=socket,in,60,,5550,udp -fdm=external

Then, start up your JSBSim script like this:
 
src/jsbsim -realtime data_output/flightgear.xml scripts/c1723.xml
 
-->

<output name="localhost" type="FLIGHTGEAR" protocol="UDP" port="5550" rate="60">
</output>