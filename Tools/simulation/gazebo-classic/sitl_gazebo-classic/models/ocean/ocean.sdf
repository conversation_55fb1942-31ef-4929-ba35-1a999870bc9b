<?xml version="1.0" ?>
<!-- Copyright (c) 2016 The UUV Simulator Authors.
     All rights reserved.

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<sdf version="1.4">
<model name="ocean">
  <static>true</static>
  <link name="ocean_link">

  <visual name="ocean_surface">
    <pose>0 0 0 0 0 0</pose>
    <cast_shadows>false</cast_shadows>
    <geometry>
      <mesh>
        <uri>model://ocean/media/models/sea_surface_1000m_x_1000m.dae</uri>
        <scale>2 2 2</scale>
      </mesh>
    </geometry>
    <material>
      <script>
        <uri>model://ocean/media/materials/scripts/waves.material</uri>
        <name>UUVSimulator/SimpleWaves</name>
      </script>
    </material>
  </visual>

  <visual name="ocean_below">
    <pose>0 0 0 3.14 0 0</pose>
    <cast_shadows>false</cast_shadows>
    <geometry>
      <mesh>
        <uri>model://ocean//media/models/sea_surface_1000m_x_1000m.dae</uri>
        <scale>2 2 2</scale>
      </mesh>
    </geometry>
    <material>
      <script>
        <uri>model://ocean/media/materials/scripts/water.material</uri>
        <name>UUVSimulator/StaticWater</name>
      </script>
    </material>
  </visual>

  <visual name="seafloor">
    <cast_shadows>false</cast_shadows>
    <pose>0 0 -100 0 0 0</pose>
    <geometry>
      <box>
        <size>2000 2000 .1</size>
      </box>
    </geometry>
    <material>
      <script>
        <uri>model://ocean/media/materials/scripts/sand.material</uri>
        <name>UUVSimulator/SandAndStones</name>
      </script>
      <emissive>
        0.6 0.6 0.6 1.0
      </emissive>
    </material>
  </visual>

  <collision name="seafloor">
    <pose>0 0 -100 0 0 0</pose>
    <geometry>
      <box>
        <size>2000 2000 .1</size>
      </box>
    </geometry>
  </collision>

  <visual name="north">
    <pose>1000 0 -50 0 -1.5708 0</pose>
    <geometry><plane><normal>0 0 1</normal><size>100 2000</size></plane></geometry>
    <material>
      <script>
        <uri>model://ocean/media/materials/scripts/water.material</uri>
        <name>UUVSimulator/SeaWall</name>
      </script>
      <emissive>
        0.6 0.6 0.6 1.0
      </emissive>
    </material>
  </visual>

  <visual name="south">
    <pose>-1000 0 -50 0 1.5708 0</pose>
    <geometry><plane><normal>0 0 1</normal><size>100 2000</size></plane></geometry>
    <material>
      <script>
        <uri>model://ocean/media/materials/scripts/water.material</uri>
        <name>UUVSimulator/SeaWall</name>
      </script>
      <emissive>
        0.6 0.6 0.6 1.0
      </emissive>
    </material>
  </visual>

  <visual name="west">
    <pose>0 1000 -50 1.5708 0 0</pose>
    <geometry><plane><normal>0 0 1</normal><size>2000 100</size></plane></geometry>
    <material>
      <script>
        <uri>model://ocean/media/materials/scripts/water.material</uri>
        <name>UUVSimulator/SeaWall</name>
      </script>
      <emissive>
        0.6 0.6 0.6 1.0
      </emissive>
    </material>
  </visual>

  <visual name="east">
    <pose>0 -1000 -50 -1.5708 0 0</pose>
    <geometry><plane><normal>0 0 1</normal><size>2000 100</size></plane></geometry>
    <material>
      <script>
        <uri>model://ocean/media/materials/scripts/water.material</uri>
        <name>UUVSimulator/SeaWall</name>
      </script>
      <emissive>
        0.6 0.6 0.6 1.0
      </emissive>
    </material>
  </visual>
  </link>
</model>
</sdf>
