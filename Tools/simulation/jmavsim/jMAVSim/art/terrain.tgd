<terragen
	name = "Project"
	gui_use_node_pos = "1"
	gui_node_pos = "0 0 0"
	gui_group = ""
	written_by_program = "Terragen 3"
	written_by_version = "3.3.04.0"
	author = ""
	comments = ""
	current_frame = "1"
	start_frame = "1"
	end_frame = "100"
	gui_network_view_position = "88.32740645 455.4475142 0"
	gui_network_view_size = "2064 1323 0"
	gui_network_view_zoom = "0.6806506061"
	>
	<camera
		name = "Render Camera"
		gui_use_node_pos = "1"
		gui_node_pos = "640 60 0"
		gui_group = "Cameras"
		show_camera_body_in_preview = "1"
		position = "-24.31151095 10 -5.688128581"
		rotation = "-7 0 0"
		light_exposure = "1"
		perspective = "1"
		fisheye = "0"
		use_horizontal_fov = "1"
		horizontal_fov = "60"
		use_vertical_fov = "0"
		vertical_fov = "40"
		focal_length_in_mm = "31.17691454"
		film_aperture_in_mm = "36 24"
		orthographic = "0"
		use_ortho_width = "1"
		ortho_width = "1000"
		use_ortho_height = "0"
		ortho_height = "1000"
		spherical = "0"
		motion_blur_position = "1"
		motion_blur_length = "0.5"
		shutter_offset = "-0.25"
		subject_distance = "100"
		aperture_diameter_in_mm = "5"
		import_position = "1"
		import_rotation = "1"
		import_fov_general = "1"
		import_Z_up = "0"
		import_rotation_order = "4"
		import_vertical_FOV = "1"
		import_focal_length = "0"
		import_focal_length_to_FOV = "0"
		do_not_import_FOV = "0"
		m_fbx_convert_to_metres = "1"
		import_offset = "0 0 0"
		import_scale = "1"
		import_filename = ""
		export_filename = ""
		stereo = "0"
		stereo_left = "1"
		stereo_centre = "0"
		stereo_right = "0"
		stereo_mode = "1"
		inter-axial_separation_in_mm = "63.5"
		zero_parallax_distance = "2.54"
		>
	</camera>
	<render
		name = "Render 01"
		gui_use_node_pos = "1"
		gui_node_pos = "960 20 0"
		gui_group = "Renderers"
		master = "1"
		image_width = "1280"
		lock_aspect_ratio = "0"
		image_height = "900"
		image_aspect_ratio = "1.422222222"
		pixel_aspect_ratio = "1"
		camera = "Render Camera"
		surfaces_visible = "1"
		atmosphere_visible = "1"
		do_shadows = "1"
		detail = "0.6"
		anti-aliasing = "3"
		ray_trace_objects = "1"
		ray_trace_atmosphere = "0"
		motion_blur = "1"
		motion_blur_method = "1"
		depth_of_field = "0"
		dof_method = "2"
		GI_relative_detail = "2"
		GI_sample_quality = "2"
		GI_blur_radius = "8"
		supersample_prepass = "0"
		GI_surface_details = "1"
		do_crop_region = "0"
		crop_left = "0"
		crop_right = "1"
		crop_bottom = "0"
		crop_top = "1"
		crop_to_object = "0"
		crop_object_name = ""
		pixel_filter = "2"
		anti-aliasing_bloom = "1"
		detail_blending = "0"
		displacement_filter = "1"
		microvertex_jittering = "1"
		detail_jittering = "1"
		lock_subdiv_to_frame = "0"
		lock_to_frame_number = "1"
		do_reverse_primary_rays = "0"
		reverse_primary_rays_multiplier = "1"
		do_ray_traced_shadows = "1"
		ray_trace_everything = "0"
		soft_clip_effect = "1"
		soft_clip_softness = "1"
		compensate_soft_clip = "1"
		contrast = "1"
		contrast_adjust = "0.25"
		gamma_correction = "2.2"
		minimum_threads = "1"
		maximum_threads = "64"
		size_of_subdiv_cache_in_Mb = "1600"
		preallocate_subdiv_cache = "0"
		ray_detail_region = "1"
		ray_detail_region_padding = "0"
		GI_prepass_padding = "0"
		render_layer = ""
		output_image_filename = "C:\Users\<USER>\Documents\temp.%04d.bmp"
		extra_output_images = "0"
		extra_output_image_filename = "C:\Users\<USER>\Documents\temp.IMAGETYPE.%04d.bmp"
		create_subfolders = "1"
		micro_exporter = "0"
		micro_exporter_name = ""
		sequence_first = "1"
		sequence_last = "100"
		sequence_step = "1"
		>
		<render_GI_settings
			name = "Render GI settings 01"
			gui_use_node_pos = "1"
			gui_node_pos = "1160 120 0"
			gui_group = ""
			GI_relative_detail = "2"
			GI_cache_detail = "2"
			GI_sample_quality = "2"
			GI_blur_radius = "8"
			supersample_prepass = "0"
			no_GI_cache_file = "1"
			write_to_GI_cache_file = "0"
			GI_cache_filename_to_write = "C:\Users\<USER>\Documents\gicache_%04d.gic"
			read_from_GI_cache_file = "0"
			GI_cache_filename_to_read = ""
			GI_cache_blend_mode = "3"
			GI_cache_blend_range = "5"
			GI_prepass_padding = "0"
			GI_surface_details = "1"
			GI_surface_details_method = "1"
			GISD_occlusion_weight = "1"
			GISD_bounce_weight = "1"
			GISD_radius = "24"
			GISD_falloff = "1"
			>
		</render_GI_settings>
		<render_bucket_controls
			name = "Render bucket controls 01"
			gui_use_node_pos = "1"
			gui_node_pos = "1160 60 0"
			gui_group = ""
			max_bucket_size = "256 256"
			allow_auto_reduction = "1"
			>
		</render_bucket_controls>
		<render_pixel_sampler
			name = "Render pixel sampler 01"
			gui_use_node_pos = "1"
			gui_node_pos = "1160 0 0"
			gui_group = ""
			anti-aliasing = "3"
			customise_sampling = "0"
			first_sampling_level = "2"
			pixel_noise_threshold = "0.1"
			>
		</render_pixel_sampler>
		<render_subdiv_settings
			name = "Render subdiv settings 01"
			gui_use_node_pos = "1"
			gui_node_pos = "1160 -60 0"
			gui_group = ""
			fully_adaptive = "1"
			microvertex_jittering = "1"
			force_all_edges = "0"
			detail_jittering = "1"
			displacement_filter = "1"
			jitter_shading_points = "1"
			ray_detail_multiplier = "0.25"
			stabilise_ray_detail_in_motion = "0"
			ray_detail_stabilisation = "3"
			>
		</render_subdiv_settings>
	</render>
	<enviro_light
		name = "Enviro light"
		gui_use_node_pos = "1"
		gui_node_pos = "320 0 0"
		gui_group = "Lighting"
		enable = "1"
		mode = "1"
		ambient_strength_on_surfaces = "1"
		ambient_colour_on_surfaces = "0.6499999762 0.8000000119 1"
		ambient_strength_in_atmosphere = "1"
		ambient_colour_in_atmosphere = "0.6499999762 0.8000000119 1"
		global_strength_on_surfaces = "1"
		global_tint_on_surfaces = "1 1 1"
		global_strength_in_atmosphere = "1"
		global_tint_in_atmosphere = "1 1 1"
		strength_on_surfaces = "1"
		colour_on_surfaces = "1 1 1"
		strength_in_atmosphere = "1"
		colour_in_atmosphere = "1 1 1"
		>
	</enviro_light>
	<sunlight
		name = "Sunlight 01"
		gui_use_node_pos = "1"
		gui_node_pos = "320 -60 0"
		gui_group = "Lighting"
		enable = "1"
		light_surfaces = "1"
		light_atmosphere = "1"
		heading = "300"
		elevation = "18"
		colour = "0.7063724995 0.7124999762 0.6465937495"
		strength = "5"
		cast_shadows = "1"
		shadows_of_surfaces = "1"
		shadows_of_atmosphere = "1"
		soft_shadows = "0"
		soft_shadow_diameter = "0.5"
		soft_shadow_samples = "9"
		soft_shadow_sample_jitter = "1"
		glow_in_atmosphere = "1"
		specular_highlights = "1"
		visible_disc = "1"
		angular_diameter = "0.5"
		>
	</sunlight>
	<sphere
		name = "Background"
		gui_use_node_pos = "1"
		gui_node_pos = "0 -60 0"
		gui_group = "Objects"
		enable = "1"
		show_b-box_in_preview = "0"
		visibility = "2"
		visible_to_camera = "1"
		visible_to_other_rays = "1"
		cast_shadows = "0"
		render_method = "0"
		sorting_bias = "0"
		centre = "0 0 0"
		radius = "-200000000"
		rotate = "0 0 0"
		scale = "1 1 1"
		proper_surface_normals = "0"
		scaling_deforms_normals = "1"
		heading = "0"
		elevation = "0"
		distance = "0"
		surface_shader = "Background shader"
		displacement_tolerance = "1"
		import_offset = "0 0 0"
		import_scale = "1"
		import_motion_filename = ""
		>
		<constant_shader
			name = "Background shader"
			gui_use_node_pos = "1"
			gui_node_pos = "200 0 0"
			gui_group = ""
			enable = "1"
			input_node = ""
			gui_use_preview_patch_size = "0"
			gui_preview_patch_size = "1000 1000"
			colour = "0 0 0"
			alpha = "0 0 0"
			>
		</constant_shader>
	</sphere>
	<planet
		name = "Planet 01"
		gui_use_node_pos = "1"
		gui_node_pos = "0 0 0"
		gui_group = "Objects"
		enable = "1"
		show_b-box_in_preview = "0"
		render_surface = "1"
		translate_textures_with_planet = "0"
		render_atmosphere = "1"
		rotate_textures_with_planet = "0"
		lat_long_at_apex = "0 0"
		centre = "0 -6378000 0"
		rotation = "0 0 0"
		radius = "6378000"
		heading = "0"
		elevation = "270"
		distance = "6378000"
		surface_shader = "Snow"
		atmosphere_shader = "Altocumulus layer 01"
		displacement_tolerance = "1"
		import_offset = "0 0 0"
		import_scale = "1"
		import_motion_filename = ""
		>
	</planet>
	<planet_atmosphere
		name = "Atmosphere 01"
		gui_use_node_pos = "1"
		gui_node_pos = "0 400 0"
		gui_group = "Atmosphere"
		enable = "1"
		input_node = ""
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		enable_primary = "1"
		enable_secondary = "1"
		centre = "0 -6378000 0"
		radius = "6378000"
		seed = "0"
		haze_density = "1"
		haze_horizon_colour = "0.25 0.25 0.25"
		bluesky_density = "2.5"
		bluesky_horizon_colour = "0.200000003 0.25 0.3000000119"
		bluesky_additive = "0.75"
		bluesky_additive_colour = "0.1438666731 0.3779166639 1"
		redsky_decay = "2.5"
		haze_exp_height = "2000"
		bluesky_exp_height = "8000"
		ceiling_adjust = "7"
		ceiling = "56000"
		floor = "-16000"
		haze_glow_amount = "1.5"
		haze_glow_power = "1"
		bluesky_glow_amount = "0"
		bluesky_glow_power = "0.75"
		enviro_light = "1"
		enviro_light_tint = "1 1 1"
		anisotropic_enviro_light = "1"
		shadow_function = ""
		ambient = "0 0 0"
		fake_dark_power = "0"
		fake_dark_sharpness = "10"
		bluesky_density_colour = "0.2158000022 0.4535000026 1"
		redsky_decay_colour = "0.805896461 0.6354003549 0.3678794503"
		improved_glow_model = "1"
		number_of_samples = "16"
		adjust_to_distance = "1"
		sample_jitter = "1"
		enable_ray_traced_shadows = "0"
		>
	</planet_atmosphere>
	<power_fractal_shader_v3
		name = "Base colours"
		gui_use_node_pos = "1"
		gui_node_pos = "-920 460 0"
		gui_group = "Shaders"
		enable = "1"
		input_node = "Compute Terrain"
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		seed = "8028"
		feature_scale = "1"
		lead-in_scale = "1000"
		smallest_scale = "0.1125930452"
		noise_octaves = "15"
		noise_stretch_XYZ = "1 1 1"
		apply_high_colour = "1"
		high_colour = "0.3249999881 0.2232999951 0.1086999997"
		apply_low_colour = "1"
		low_colour = "0.1036000028 0.06972999871 0.007155000232"
		colour_contrast = "0.125"
		colour_offset = "0"
		colour_roughness = "5"
		clamp_high_colour = "1"
		clamp_low_colour = "1"
		apply_displacement = "0"
		displacement_direction = "1"
		displacement_amplitude = "1"
		displacement_offset = "0"
		displacement_roughness = "1"
		displacement_spike_limit = "1"
		continue_spike_limit = "0"
		adjust_coastline = "0"
		coastline_altitude = "0"
		coastline_smoothing = "30"
		noise_flavour = "0"
		ridge_smoothing = "0"
		gully_smoothing = "0"
		noise_variation = "1"
		variation_method = "2"
		buoyancy_from_variation = "0"
		clumping_of_variation = "0"
		distort_by_normal = "1"
		distortion_by_normal = "5"
		lead-in_warp_effect = "1"
		lead-in_warp_amount = "0.5"
		less_warp_at_feature_scale = "0"
		allow_vertical_warp = "0"
		four-d_noise = "0"
		four-d_noise_speed = "0.1"
		reference_frame_number = "0"
		blend_by_shader = "0"
		blending_shader = ""
		fit_blendshader_to_this = "0"
		invert_blendshader = "0"
		>
	</power_fractal_shader_v3>
	<simple_shape_shader
		name = "Simple shape shader 01"
		gui_use_node_pos = "1"
		gui_node_pos = "-720 1040 0"
		gui_group = "Terrain"
		enable = "1"
		input_node = ""
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		type_of_shape = "1"
		position = "0 0 0"
		size = "10000 10000"
		rotation = "0"
		polygon_sides = "5"
		draw_shape_edges_in_preview = "1"
		apply_colour = "1"
		apply_main_colour = "1"
		colour = "1 1 1"
		apply_edge_colour = "1"
		edge_colour = "0 0 0"
		colour_edge_profile = "1"
		colour_edge_width = "90"
		colour_edge_units = "1"
		colour_position_key = "1"
		apply_displacement = "0"
		displace_relative_to_surface = "1"
		displace_relative_to_shader_position = "0"
		displacement_direction = "1"
		displacement_amplitude = "1"
		displacement_offset = "0"
		displacement_edge_profile = "0"
		displacement_edge_width = "50"
		displacement_edge_units = "0"
		displacement_position_key = "1"
		>
	</simple_shape_shader>
	<power_fractal_shader_v3
		name = "Fractal terrain 01"
		gui_use_node_pos = "1"
		gui_node_pos = "-920 980 0"
		gui_group = "Terrain"
		enable = "1"
		input_node = ""
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		seed = "119"
		feature_scale = "5000"
		lead-in_scale = "25000"
		smallest_scale = "0.107881116"
		noise_octaves = "20"
		noise_stretch_XYZ = "1 1 1"
		apply_high_colour = "0"
		high_colour = "1 1 1"
		apply_low_colour = "0"
		low_colour = "0 0 0"
		colour_contrast = "0.5"
		colour_offset = "0"
		colour_roughness = "5"
		clamp_high_colour = "1"
		clamp_low_colour = "1"
		apply_displacement = "1"
		displacement_direction = "1"
		displacement_amplitude = "2000"
		displacement_offset = "0"
		displacement_roughness = "0.875"
		displacement_spike_limit = "0.25"
		continue_spike_limit = "1"
		adjust_coastline = "0"
		coastline_altitude = "0"
		coastline_smoothing = "30"
		noise_flavour = "3"
		ridge_smoothing = "0.1"
		gully_smoothing = "0.1"
		noise_variation = "2"
		variation_method = "2"
		buoyancy_from_variation = "0.5"
		clumping_of_variation = "0.25"
		distort_by_normal = "0"
		distortion_by_normal = "5"
		lead-in_warp_effect = "1"
		lead-in_warp_amount = "0.75"
		less_warp_at_feature_scale = "1"
		allow_vertical_warp = "0"
		four-d_noise = "0"
		four-d_noise_speed = "0.1"
		reference_frame_number = "0"
		blend_by_shader = "1"
		blending_shader = "Simple shape shader 01"
		fit_blendshader_to_this = "0"
		invert_blendshader = "1"
		>
	</power_fractal_shader_v3>
	<fractal_warp_shader
		name = "Fractal warp shader 01"
		gui_use_node_pos = "1"
		gui_node_pos = "-920 860 0"
		gui_group = "Terrain"
		enable = "1"
		input_node = "Fractal terrain 01"
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		scale = "1000"
		warp_amount = "0.25"
		variation = "1.5"
		roughness = "1"
		blend_by_shader = "0"
		blending_shader = ""
		fit_blendshader_to_this = "0"
		invert_blendshader = "0"
		>
	</fractal_warp_shader>
	<compute_terrain
		name = "Compute Terrain"
		gui_use_node_pos = "1"
		gui_node_pos = "-920 740 0"
		gui_group = "Terrain"
		enable = "1"
		input_node = "Fractal warp shader 01"
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		gradient_patch_size = "20"
		smooth_surface = "0"
		>
	</compute_terrain>
	<group
		name = "Objects"
		gui_use_node_pos = "1"
		gui_node_pos = "0 -80 0"
		gui_group = ""
		gui_node_size = "240 320 1"
		gui_node_colour = "0.8000000119 0.8000000119 0.8000000119"
		special_group = "1"
		global_bookmark = "1"
		>
	</group>
	<group
		name = "Terrain"
		gui_use_node_pos = "1"
		gui_node_pos = "-720 900 0"
		gui_group = ""
		gui_node_size = "640 440 1"
		gui_node_colour = "0.2119999975 0.5174000263 0.1138999984"
		special_group = "2"
		global_bookmark = "1"
		>
	</group>
	<group
		name = "Shaders"
		gui_use_node_pos = "1"
		gui_node_pos = "-720 340 0"
		gui_group = ""
		gui_node_size = "640 520 1"
		gui_node_colour = "0.7968999743 0.1604000032 0.1604000032"
		special_group = "3"
		global_bookmark = "1"
		>
	</group>
	<group
		name = "Water"
		gui_use_node_pos = "1"
		gui_node_pos = "200 720 0"
		gui_group = ""
		gui_node_size = "640 240 1"
		gui_node_colour = "0.1000000015 0.5 0.349999994"
		special_group = "4"
		global_bookmark = "1"
		>
	</group>
	<group
		name = "Atmosphere"
		gui_use_node_pos = "1"
		gui_node_pos = "200 350 0"
		gui_group = ""
		gui_node_size = "640 280 1"
		gui_node_colour = "0.2310000062 0.3203999996 1"
		special_group = "5"
		global_bookmark = "1"
		>
	</group>
	<group
		name = "Lighting"
		gui_use_node_pos = "1"
		gui_node_pos = "320 -80 0"
		gui_group = ""
		gui_node_size = "240 320 1"
		gui_node_colour = "1 0.7299000025 0.2195000052"
		special_group = "6"
		global_bookmark = "1"
		>
	</group>
	<group
		name = "Cameras"
		gui_use_node_pos = "1"
		gui_node_pos = "640 -50 0"
		gui_group = ""
		gui_node_size = "240 380 1"
		gui_node_colour = "0.3021000028 0.2673999965 0.4647000134"
		special_group = "7"
		global_bookmark = "1"
		>
	</group>
	<group
		name = "Renderers"
		gui_use_node_pos = "1"
		gui_node_pos = "973 -50 0"
		gui_group = ""
		gui_node_size = "266.0336416 380 1"
		gui_node_colour = "0.7372000217 0.3440000117 0.2119999975"
		special_group = "8"
		global_bookmark = "1"
		>
	</group>
	<surface_layer
		name = "Surface layer 01"
		gui_use_node_pos = "1"
		gui_node_pos = "-920 340 0"
		gui_group = "Shaders"
		enable = "1"
		input_node = "/Base colours"
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		apply_colour = "1"
		diffuse_colour = "0.1733999997 0.3488999903 0.07592999935"
		colour_function = ""
		enable_test_colour = "0"
		test_colour = "1 0 1"
		luminous = "0"
		luminosity_multiplier = "1"
		luminosity_tint = "1 1 1"
		luminosity_function = ""
		displacement_direction = "1"
		displacement_multiplier = "1"
		displacement_function = ""
		displacement_offset = "0"
		smoothing_effect = "0"
		smoothing_amount = "1"
		child_layers = ""
		coverage = "0.65"
		fractal_breakup = "1"
		breakup_shader = "Fractal breakup 01"
		fractal_contrast = "0.3375"
		invert_breakup = "0"
		only_breakup_colour = "1"
		blend_by_shader = "0"
		blending_shader = ""
		invert_blendshader = "0"
		blend_as_coverage = "1"
		limit_maximum_altitude = "1"
		maximum_altitude = "625"
		max_alt_fuzzy_zone = "200"
		limit_minimum_altitude = "0"
		minimum_altitude = "200"
		min_alt_fuzzy_zone = "200"
		altitude_key = "1"
		use_Y_for_altitude = "0"
		limit_maximum_slope = "1"
		maximum_slope_angle = "25"
		max_slope_fuzzy_zone = "10"
		limit_minimum_slope = "0"
		minimum_slope_angle = "30"
		min_slope_fuzzy_zone = "10"
		slope_key = "0"
		use_Y_for_slope = "0"
		intersect_underlying = "0"
		intersection_mode = "0"
		intersection_zone = "2"
		smoothing_scale = "0"
		intersection_shift = "2"
		min_intersection_shift = "-2"
		fuzzy_zone_softness = "1"
		>
	</surface_layer>
	<power_fractal_shader_v3
		name = "Fractal breakup 01"
		gui_use_node_pos = "1"
		gui_node_pos = "-720 400 0"
		gui_group = "Shaders"
		enable = "1"
		input_node = ""
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		seed = "17385"
		feature_scale = "31.6406"
		lead-in_scale = "1000"
		smallest_scale = "0.01"
		noise_octaves = "19"
		noise_stretch_XYZ = "1 1 1"
		apply_high_colour = "1"
		high_colour = "0.166899994 0.2976999879 0.08918999881"
		apply_low_colour = "1"
		low_colour = "0.09869000316 0.1112999991 0"
		colour_contrast = "0.5"
		colour_offset = "0"
		colour_roughness = "5"
		clamp_high_colour = "0"
		clamp_low_colour = "0"
		apply_displacement = "0"
		displacement_direction = "1"
		displacement_amplitude = "1"
		displacement_offset = "0"
		displacement_roughness = "1"
		displacement_spike_limit = "1"
		continue_spike_limit = "0"
		adjust_coastline = "0"
		coastline_altitude = "0"
		coastline_smoothing = "30"
		noise_flavour = "0"
		ridge_smoothing = "0.1"
		gully_smoothing = "0.1"
		noise_variation = "1"
		variation_method = "2"
		buoyancy_from_variation = "0"
		clumping_of_variation = "0.25"
		distort_by_normal = "1"
		distortion_by_normal = "5"
		lead-in_warp_effect = "1"
		lead-in_warp_amount = "1"
		less_warp_at_feature_scale = "0"
		allow_vertical_warp = "0"
		four-d_noise = "0"
		four-d_noise_speed = "0.1"
		reference_frame_number = "0"
		blend_by_shader = "0"
		blending_shader = ""
		fit_blendshader_to_this = "0"
		invert_blendshader = "0"
		>
	</power_fractal_shader_v3>
	<surface_layer
		name = "Snow"
		gui_use_node_pos = "1"
		gui_node_pos = "-920 220 0"
		gui_group = "Shaders"
		enable = "1"
		input_node = "/Surface layer 01"
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		apply_colour = "1"
		diffuse_colour = "1 1 1"
		colour_function = ""
		enable_test_colour = "0"
		test_colour = "1 0 1"
		luminous = "0"
		luminosity_multiplier = "1"
		luminosity_tint = "1 1 1"
		luminosity_function = ""
		displacement_direction = "1"
		displacement_multiplier = "1"
		displacement_function = ""
		displacement_offset = "0"
		smoothing_effect = "0"
		smoothing_amount = "1"
		child_layers = ""
		coverage = "0.7"
		fractal_breakup = "1"
		breakup_shader = "Fractal breakup 02"
		fractal_contrast = "1"
		invert_breakup = "0"
		only_breakup_colour = "1"
		blend_by_shader = "0"
		blending_shader = ""
		invert_blendshader = "0"
		blend_as_coverage = "1"
		limit_maximum_altitude = "0"
		maximum_altitude = "1000"
		max_alt_fuzzy_zone = "300"
		limit_minimum_altitude = "1"
		minimum_altitude = "1250"
		min_alt_fuzzy_zone = "468.75"
		altitude_key = "1"
		use_Y_for_altitude = "0"
		limit_maximum_slope = "1"
		maximum_slope_angle = "60"
		max_slope_fuzzy_zone = "20"
		limit_minimum_slope = "0"
		minimum_slope_angle = "30"
		min_slope_fuzzy_zone = "10"
		slope_key = "0"
		use_Y_for_slope = "0"
		intersect_underlying = "0"
		intersection_mode = "0"
		intersection_zone = "2"
		smoothing_scale = "0"
		intersection_shift = "2"
		min_intersection_shift = "-2"
		fuzzy_zone_softness = "1"
		>
	</surface_layer>
	<power_fractal_shader_v3
		name = "Fractal breakup 02"
		gui_use_node_pos = "1"
		gui_node_pos = "-720 280 0"
		gui_group = "Shaders"
		enable = "1"
		input_node = ""
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		seed = "61149"
		feature_scale = "1"
		lead-in_scale = "1000"
		smallest_scale = "0.01"
		noise_octaves = "19"
		noise_stretch_XYZ = "1 1 1"
		apply_high_colour = "1"
		high_colour = "1 1 1"
		apply_low_colour = "0"
		low_colour = "0 0 0"
		colour_contrast = "0.5"
		colour_offset = "0"
		colour_roughness = "5"
		clamp_high_colour = "0"
		clamp_low_colour = "0"
		apply_displacement = "0"
		displacement_direction = "1"
		displacement_amplitude = "1"
		displacement_offset = "0"
		displacement_roughness = "1"
		displacement_spike_limit = "1"
		continue_spike_limit = "0"
		adjust_coastline = "0"
		coastline_altitude = "0"
		coastline_smoothing = "30"
		noise_flavour = "0"
		ridge_smoothing = "0.1"
		gully_smoothing = "0.1"
		noise_variation = "1"
		variation_method = "2"
		buoyancy_from_variation = "0"
		clumping_of_variation = "0.25"
		distort_by_normal = "1"
		distortion_by_normal = "5"
		lead-in_warp_effect = "1"
		lead-in_warp_amount = "1"
		less_warp_at_feature_scale = "0"
		allow_vertical_warp = "0"
		four-d_noise = "0"
		four-d_noise_speed = "0.1"
		reference_frame_number = "0"
		blend_by_shader = "0"
		blending_shader = ""
		fit_blendshader_to_this = "0"
		invert_blendshader = "0"
		>
	</power_fractal_shader_v3>
	<cloud_layer_v2
		name = "Altocumulus layer 01"
		gui_use_node_pos = "1"
		gui_node_pos = "0 280 0"
		gui_group = "Atmosphere"
		enable = "1"
		input_node = "/Atmosphere 01"
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		enable_primary = "1"
		enable_secondary = "1"
		move_textures_with_cloud = "0"
		centre = "0 -6378000 0"
		radius = "6378000"
		seed = "0"
		cloud_altitude = "4286"
		cloud_depth = "509.88"
		local_sphere = "0"
		local_sphere_centre = "0 4286 0"
		local_sphere_radius = "10000"
		local_sphere_falloff = "1"
		local_sphere_value_at_radius = "-0.5"
		density_shader = "Density fractal 01"
		edge_sharpness = "1"
		cloud_density = "0.01"
		coverage_adjust = "-0.225"
		cloud_colour = "0.25 0.25 0.25"
		scattering_colour = "0.25 0.25 0.25"
		sun_glow_amount = "1"
		sun_glow_power = "1"
		light_propagation = "2"
		light_propagation_mix = "0.125"
		fake_internal_scattering = "2"
		enviro_light = "1"
		enviro_light_tint = "1 1 1"
		darker_unresolved_scattering = "1"
		anisotropic_enviro_light = "1"
		altitude_offset_function = ""
		altitude_offset_multiplier = "1"
		depth_modulator = ""
		depth_modulator_centre = "0"
		final_density_modulator = ""
		direct_light_modulator = ""
		enviro_light_modulator = ""
		ambient_light_modulator = ""
		shadow_function = ""
		ambient = "0 0 0"
		fake_dark_power = "0"
		fake_dark_sharpness = "10"
		improved_lighting_model = "1"
		taper_top_and_base = "1"
		flatter_base = "0"
		base_wispiness = "0"
		base_softness = "0"
		invert_profile = "0"
		coverage_gamma = "1"
		rendering_method = "1"
		quality = "1"
		number_of_samples = "34"
		sample_jitter = "1"
		step_optimisation = "0"
		enable_ray_traced_shadows = "0"
		acceleration_cache = "0"
		use_2D_shadow_map = "0"
		shadow_map_resolution = "200 200"
		shadow_map_blur_radius = "2 2"
		use_voxel_buffer = "0"
		voxel_buffer_resolution = "100 20 100"
		use_voxels_to_accelerate_empty_space = "1"
		visualise_voxels = "0"
		use_voxels_for_shadows = "1"
		voxel_shadow_threshold = "4"
		>
	</cloud_layer_v2>
	<cloud_fractal_shader_v3
		name = "Density fractal 01"
		gui_use_node_pos = "1"
		gui_node_pos = "200 340 0"
		gui_group = "Atmosphere"
		enable = "1"
		input_node = ""
		gui_use_preview_patch_size = "0"
		gui_preview_patch_size = "1000 1000"
		seed = "19095"
		feature_scale = "150"
		lead-in_scale = "20000"
		smallest_scale = "10"
		noise_octaves = "13"
		noise_stretch_XYZ = "1 1 1"
		apply_high_colour = "1"
		high_colour = "1 1 1"
		apply_low_colour = "0"
		low_colour = "0 0 0"
		contrast = "1"
		coverage_adjust = "0"
		roughness = "1.75"
		clamp_high = "0"
		clamp_low = "0"
		apply_displacement = "0"
		displacement_direction = "1"
		displacement_amplitude = "1"
		displacement_offset = "0"
		displacement_roughness = "1"
		displacement_spike_limit = "1"
		continue_spike_limit = "0"
		adjust_coastline = "0"
		coastline_altitude = "0"
		coastline_smoothing = "30"
		noise_flavour = "0"
		ridge_smoothing = "0.1"
		gully_smoothing = "0.1"
		noise_variation = "1"
		variation_method = "2"
		buoyancy_from_variation = "0.5"
		clumping_of_variation = "0.25"
		distort_by_normal = "0"
		distortion_by_normal = "5"
		lead-in_warp_effect = "1"
		lead-in_warp_amount = "0.5"
		less_warp_at_feature_scale = "1"
		allow_vertical_warp = "1"
		four-d_noise = "0"
		four-d_noise_speed = "0.1"
		reference_frame_number = "0"
		blend_by_shader = "0"
		blending_shader = ""
		fit_blendshader_to_this = "0"
		invert_blendshader = "0"
		>
	</cloud_fractal_shader_v3>
</terragen>
