<?xml version="1.0"?>
<sdf version='1.5'>
  <model name='plane'>
    <pose>0 0 0.246 0 0 0</pose>
    <link name='base_link'>
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>1.5</mass>
        <inertia>
          <ixx>0.197563</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.1458929</iyy>
          <iyz>0</iyz>
          <izz>0.1477</izz>
        </inertia>
      </inertial>
      <collision name='base_link_collision'>
        <pose>0 0 -0.07 0 0 0</pose>
        <geometry>
          <box>
            <size>0.47 0.47 0.11</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <max_vel>10</max_vel>
              <min_depth>0.01</min_depth>
            </ode>
          </contact>
          <!--<friction>
            <ode/>
          </friction>-->
        </surface>
      </collision>
      <visual name='base_link_visual'>
        <pose>0.07 0 -0.08 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://plane/meshes/body.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/DarkGrey</name>
            <uri>__default__</uri>
          </script>
        </material>
        <plugin name="left_elevon_lift_visual" filename="libForceVisual.so">
          <topic_name>lift_force/lumped_force</topic_name>
          <color>Gazebo/Green</color>
        </plugin>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
      <self_collide>0</self_collide>
    </link>
    <link name='plane/imu_link'>
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.015</mass>
        <inertia>
          <ixx>1e-05</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>1e-05</iyy>
          <iyz>0</iyz>
          <izz>1e-05</izz>
        </inertia>
      </inertial>
    </link>
    <joint name='plane/imu_joint' type='revolute'>
      <child>plane/imu_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>0</lower>
          <upper>0</upper>
          <effort>0</effort>
          <velocity>0</velocity>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <include>
      <uri>model://airspeed</uri>
      <pose>0 0 0 0 0 0</pose>
      <name>airspeed</name>
    </include>
    <joint name='airspeed_joint' type='fixed'>
      <child>airspeed::link</child>
      <parent>base_link</parent>
    </joint>
    <link name='rotor_puller'>
      <pose>0.3 0 0.0 0 1.57 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.005</mass>
        <inertia>
          <ixx>9.75e-07</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.000166704</iyy>
          <iyz>0</iyz>
          <izz>0.000167604</izz>
        </inertia>
      </inertial>
      <collision name='rotor_puller_collision'>
        <pose>0.0 0 0.0 0 0 0</pose>
        <geometry>
          <cylinder>
            <length>0.005</length>
            <radius>0.1</radius>
          </cylinder>
        </geometry>
        <surface>
          <contact>
            <ode/>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <visual name='rotor_puller_visual'>
        <pose>0 0 -0.09 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://plane/meshes/iris_prop_ccw.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>__default__</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
      <self_collide>0</self_collide>
    </link>
    <joint name='rotor_puller_joint' type='revolute'>
      <child>rotor_puller</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>

    <link name="left_elevon">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 0.3 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='left_elevon_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://plane/meshes/left_aileron.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>__default__</uri>
          </script>
        </material>
      </visual>
    </link>
    <link name="right_elevon">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 -0.3 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='right_elevon_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://plane/meshes/right_aileron.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>__default__</uri>
          </script>
        </material>
      </visual>
    </link>
    <link name="left_flap">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 0.15 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='left_flap_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://plane/meshes/left_flap.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>__default__</uri>
          </script>
        </material>
      </visual>
    </link>
    <link name="right_flap">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>0 -0.15 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='right_flap_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://plane/meshes/right_flap.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>__default__</uri>
          </script>
        </material>
      </visual>
    </link>
    <link name="elevator">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose> -0.5 0 0 0.00 0 0.0</pose>
      </inertial>
      <visual name='elevator_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://plane/meshes/elevators.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>__default__</uri>
          </script>
        </material>
      </visual>
    </link>
    <link name="rudder">
      <inertial>
        <mass>0.00000001</mass>
        <inertia>
          <ixx>0.000001</ixx>
          <ixy>0.0</ixy>
          <iyy>0.000001</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.000001</izz>
        </inertia>
        <pose>-0.5 0 0.05 0 0 0 </pose>
      </inertial>
      <visual name='rudder_visual'>
        <pose>0.07 0.0 -0.08 0.00 0 0.0</pose>
        <geometry>
          <mesh>
            <scale>0.1 0.1 0.1</scale>
            <uri>model://plane/meshes/rudder.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>__default__</uri>
          </script>
        </material>
      </visual>
    </link>
    <joint name='left_elevon_joint' type='revolute'>
      <parent>base_link</parent>
      <child>left_elevon</child>
      <pose>-0.07 0.4 0.08 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <joint name='right_elevon_joint' type='revolute'>
      <parent>base_link</parent>
      <child>right_elevon</child>
      <pose>-0.07 -0.4 0.08 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <joint name='left_flap_joint' type='revolute'>
      <parent>base_link</parent>
      <child>left_flap</child>
      <pose>-0.07 0.2 0.08 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <joint name='right_flap_joint' type='revolute'>
      <parent>base_link</parent>
      <child>right_flap</child>
      <pose>-0.07 -0.2 0.08 0.00 0 0.0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <joint name='elevator_joint' type='revolute'>
      <parent>base_link</parent>
      <child>elevator</child>
      <pose> -0.5 0 0 0 0 0</pose>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <joint name='rudder_joint' type='revolute'>
      <parent>base_link</parent>
      <child>rudder</child>
      <pose>-0.5 0 0.05 0.00 0 0.0</pose>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <!-- -30/+30 deg. -->
          <lower>-0.53</lower>
          <upper>0.53</upper>
        </limit>
        <dynamics>
          <damping>1.000</damping>
        </dynamics>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    <include>
      <uri>model://gps</uri>
      <pose>0 0 0 0 0 0</pose>
      <name>gps</name>
    </include>
    <joint name='gps_joint' type='fixed'>
      <child>gps::link</child>
      <parent>base_link</parent>
    </joint>
    <plugin name="aircraft" filename="libAdvancedLiftDragPlugin.so">
      <a0>0.0</a0>
      <CL0>0.15188</CL0>
      <AR>6.5</AR>
      <eff>0.97</eff>
      <CLa>5.015</CLa>
      <CD0>0.029</CD0>
      <Cem0>0.075</Cem0>
      <Cema>-0.463966</Cema>
      <CYb>-0.258244</CYb>
      <Cellb>-0.039250</Cellb>
      <Cenb>0.100826</Cenb>
      <CDp>0.0</CDp>
      <CYp>0.065861</CYp>
      <CLp>0.0</CLp>
      <Cellp>-0.487407</Cellp>
      <Cemp>0.0</Cemp>
      <Cenp>-0.040416</Cenp>
      <CDq>0.055166</CDq>
      <CYq>0.0</CYq>
      <CLq>7.971792</CLq>
      <Cellq>0.0</Cellq>
      <Cemq>-12.140140</Cemq>
      <Cenq>0.0</Cenq>
      <CDr>0.0</CDr>
      <CYr>0.230299</CYr>
      <CLr>0.0</CLr>
      <Cellr>0.078165</Cellr>
      <Cemr>0.0</Cemr>
      <Cenr>-0.089947</Cenr>
      <alpha_stall>0.3391428111</alpha_stall>
      <CLa_stall>-3.85</CLa_stall>
      <CDa_stall>-0.9233984055</CDa_stall>
      <Cema_stall>0</Cema_stall>
      <ref_pt>-0.12 0.0 0.0</ref_pt>
      <area>0.34</area>
      <mac>0.22</mac>
      <air_density>1.2041</air_density>
      <forward>1 0 0</forward>
      <upward>0 0 1</upward>
      <link_name>base_link</link_name>
      <topic_name>lift_force/lumped_force</topic_name> <!--Uncomment to draw the force-->
      <num_ctrl_surfaces>4</num_ctrl_surfaces>
      <control_surface>
        <name>left_elevon_joint</name>
        <index>0</index>
        <direction>1</direction>
        <CD_ctrl>-0.000059</CD_ctrl>
        <CY_ctrl>0.000171</CY_ctrl>
        <CL_ctrl>-0.011940</CL_ctrl>
        <Cell_ctrl>-0.003331</Cell_ctrl>
        <Cem_ctrl>0.001498</Cem_ctrl>
        <Cen_ctrl>-0.000057</Cen_ctrl>
      </control_surface>
      <control_surface>
        <name>right_elevon_joint</name>
        <direction>1</direction>
        <index>1</index>
        <CD_ctrl>-0.000059</CD_ctrl>
        <CY_ctrl>-0.000171</CY_ctrl>
        <CL_ctrl>-0.011940</CL_ctrl>
        <Cell_ctrl>0.003331</Cell_ctrl>
        <Cem_ctrl>0.001498</Cem_ctrl>
        <Cen_ctrl>0.000057</Cen_ctrl>
      </control_surface>
      <control_surface>
        <name>elevator_joint</name>
        <direction>-1</direction>
        <index>2</index>
        <CD_ctrl>0.000274</CD_ctrl>
        <CY_ctrl>0</CY_ctrl>
        <CL_ctrl>0.010696</CL_ctrl>
        <Cell_ctrl>0.0</Cell_ctrl>
        <Cem_ctrl>-0.025798</Cem_ctrl>
        <Cen_ctrl>0.0</Cen_ctrl>
      </control_surface>
      <control_surface>
        <name>rudder_joint</name>
        <direction>1</direction>
        <index>3</index>
        <CD_ctrl>0.0</CD_ctrl>
        <CY_ctrl>-0.003913</CY_ctrl>
        <CL_ctrl>0.0</CL_ctrl>
        <Cell_ctrl>-0.000257</Cell_ctrl>
        <Cem_ctrl>0.0</Cem_ctrl>
        <Cen_ctrl>0.001613</Cen_ctrl>
      </control_surface>
      <robotNamespace></robotNamespace>
      <windSubTopic>world_wind</windSubTopic>
    </plugin>
    <plugin name='puller' filename='libgazebo_motor_model.so'>
      <robotNamespace></robotNamespace>
      <jointName>rotor_puller_joint</jointName>
      <linkName>rotor_puller</linkName>
      <turningDirection>cw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>3500</maxRotVelocity>
      <motorConstant>8.54858e-06</motorConstant>
      <momentConstant>0.01</momentConstant>
      <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
      <motorNumber>4</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
      <motorSpeedPubTopic>/motor_speed/4</motorSpeedPubTopic>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <robotNamespace></robotNamespace>
      <windSubTopic>world_wind</windSubTopic>
    </plugin>
    <plugin name='groundtruth_plugin' filename='libgazebo_groundtruth_plugin.so'>
      <robotNamespace/>
    </plugin>
    <plugin name='magnetometer_plugin' filename='libgazebo_magnetometer_plugin.so'>
      <robotNamespace/>
      <pubRate>100</pubRate>
      <noiseDensity>0.0004</noiseDensity>
      <randomWalk>6.4e-06</randomWalk>
      <biasCorrelationTime>600</biasCorrelationTime>
      <magTopic>/mag</magTopic>
    </plugin>
    <plugin name='barometer_plugin' filename='libgazebo_barometer_plugin.so'>
      <robotNamespace/>
      <pubRate>50</pubRate>
      <baroTopic>/baro</baroTopic>
    </plugin>
    <plugin name='mavlink_interface' filename='libgazebo_mavlink_interface.so'>
      <robotNamespace></robotNamespace>
      <imuSubTopic>/imu</imuSubTopic>
      <magSubTopic>/mag</magSubTopic>
      <baroSubTopic>/baro</baroSubTopic>
      <mavlink_addr>INADDR_ANY</mavlink_addr>
      <mavlink_tcp_port>{{ mavlink_tcp_port }}</mavlink_tcp_port>
      <mavlink_udp_port>{{ mavlink_udp_port }}</mavlink_udp_port>
      <serialEnabled>{{ serial_enabled }}</serialEnabled>
      <serialDevice>{{ serial_device }}</serialDevice>
      <baudRate>{{ serial_baudrate }}</baudRate>
      <qgc_addr>INADDR_ANY</qgc_addr>
      <qgc_udp_port>14550</qgc_udp_port>
      <sdk_addr>INADDR_ANY</sdk_addr>
      <sdk_udp_port>14540</sdk_udp_port>
      <hil_mode>{{ hil_mode }}</hil_mode>
      <hil_state_level>false</hil_state_level>
      <enable_lockstep>true</enable_lockstep>
      <use_tcp>true</use_tcp>
      <motorSpeedCommandPubTopic>/gazebo/command/motor_speed</motorSpeedCommandPubTopic>
      <control_channels>
        <channel name="rudder">
          <input_index>2</input_index>
          <input_offset>0</input_offset>
          <input_scaling>1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>rudder_joint</joint_name>
        </channel>
        <channel name="rotor4">
          <input_index>4</input_index>
          <input_offset>0</input_offset>
          <input_scaling>3500</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_name>rotor_puller_joint</joint_name>
        </channel>
        <channel name="left_elevon">
          <input_index>5</input_index>
          <input_offset>0</input_offset>
          <input_scaling>1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>left_elevon_joint</joint_name>
        </channel>
        <channel name="right_elevon">
          <input_index>6</input_index>
          <input_offset>0</input_offset>
          <input_scaling>1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>right_elevon_joint</joint_name>
        </channel>
        <channel name="elevator">
          <input_index>7</input_index>
          <input_offset>0</input_offset>
          <input_scaling>1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>elevator_joint</joint_name>
        </channel>
        <channel name="left_flap">
          <input_index>3</input_index>
          <input_offset>0</input_offset>
          <input_scaling>-1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>left_flap_joint</joint_name>
        </channel>
        <channel name="right_flap">
          <input_index>8</input_index>
          <input_offset>0</input_offset>
          <input_scaling>-1</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position_kinematic</joint_control_type>
          <joint_name>right_flap_joint</joint_name>
        </channel>
      </control_channels>
    </plugin>
    <static>0</static>
    <plugin name='gazebo_imu_plugin' filename='libgazebo_imu_plugin.so'>
      <robotNamespace></robotNamespace>
      <linkName>plane/imu_link</linkName>
      <imuTopic>/imu</imuTopic>
    </plugin>
  </model>
</sdf>
