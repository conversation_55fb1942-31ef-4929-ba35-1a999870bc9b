material UUVSimulator/SeaWall
{
  receive_shadows off
  technique
  {
    pass
    {
       ambient 0.616687 0.90461 1 0.7
       diffuse 0.616687 0.90461 1 0.7
       specular 1 1 1 1 20
       emissive 0.0 0.0 0.0 1.0
       scene_blend alpha_blend

    }
  }
}


material UUVSimulator/StaticWater
{
  technique
  {
    pass
    {
      ambient 0.5 0.5 0.5 0.8
      diffuse 0.5 0.5 0.5 0.8

      scene_blend alpha_blend

      texture_unit
      {
        texture ../textures/water_water_0076_03_s.jpg
        filtering bilinear
        max_anisotropy 16
        scale 0.1 0.1
      }
    }
  }
}

material UUVSimulator/StaticDeepWater
{
  technique
  {
    pass
    {
      ambient 0.5 0.5 0.5 0.8
      diffuse 0.5 0.5 0.5 0.8

      scene_blend alpha_blend

      texture_unit
      {
        texture ../textures/water_water_0046_01.jpg
        filtering bilinear
        max_anisotropy 16
        scale 1.0 1.0
      }
    }
  }
}

material UUVSimulator/StaticTurquoiseWater
{
  technique
  {
    pass
    {
      ambient 0.5 0.5 0.5 0.8
      diffuse 0.5 0.5 0.5 0.8

      scene_blend alpha_blend

      texture_unit
      {
        texture ../textures/water_water_0051_01.jpg
        filtering bilinear
        max_anisotropy 16
        scale 1.0 1.0
      }
    }
  }
}

material UUVSimulator/StaticWaves
{
  technique
  {
    pass
    {
      ambient 0.5 0.5 0.5 0.8
      diffuse 0.5 0.5 0.5 0.8

      scene_blend alpha_blend

      texture_unit
      {
        texture ../textures/water_water_0093_01.jpg
        filtering bilinear
        max_anisotropy 16
        scale 1.0 1.0
      }
    }
  }
}
