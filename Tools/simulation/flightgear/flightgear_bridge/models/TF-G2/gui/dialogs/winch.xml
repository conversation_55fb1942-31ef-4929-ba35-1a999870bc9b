<?xml version="1.0"?>
<!-- Version: 2014-03-15 -->
<PropertyList>
    <name>winch_dialog</name>
    <layout>vbox</layout>
    <default-padding>5</default-padding>
    <modal>false</modal>
    <width>500</width>
    <!-- title bar -->
    <group>
        <layout>hbox</layout>
        <empty>
            <stretch>true</stretch>
        </empty>
        <text>
            <label>Winch Configuration</label>
        </text>
        <empty>
            <stretch>true</stretch>
        </empty>
        <button>
            <legend/>
            <key>Esc</key>
            <pref-width>16</pref-width>
            <pref-height>16</pref-height>
            <border>2</border>
            <binding>
                <command>dialog-close</command>
            </binding>
        </button>
    </group>
    <hrule/>
    <hrule/>
    <!-- towrope parameters -->
    <layout>hbox</layout>
    <empty>
        <stretch>true</stretch>
    </empty>
    <text>
        <label>Towrope Settings</label>
    </text>
    <empty>
        <stretch>true</stretch>
    </empty>
    <!-- initial tow length -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Initial Length</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>9999</label>
                <live>true</live>
                <format>%4.0fm </format>
                <property>/sim/hitches/winch/winch/initial-tow-length-m</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>20m</label>
                </text>
            </group>
            <slider>
                <name>tow_length</name>
                <property>/sim/hitches/winch/winch/initial-tow-length-m</property>
                <min>20</min>
                <max>2000</max>
                <step>5</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>2000m</label>
                </text>
            </group>
        </group>
    </group>
    <!-- max tow length -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Max. Length</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>9999</label>
                <live>true</live>
                <format>%4.0fm </format>
                <property>/sim/hitches/winch/winch/max-tow-length-m</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>20m</label>
                </text>
            </group>
            <slider>
                <name>max_tow_length</name>
                <property>/sim/hitches/winch/winch/max-tow-length-m</property>
                <min>20</min>
                <max>2000</max>
                <step>5</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>2000m</label>
                </text>
            </group>
        </group>
    </group>
    <!-- break force -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Weak Link Break Force</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>99999</label>
                <live>true</live>
                <format>%5.0fN </format>
                <property>/sim/hitches/winch/tow/brake-force</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>100N</label>
                </text>
            </group>
            <slider>
                <name>break_force</name>
                <property>/sim/hitches/winch/tow/brake-force</property>
                <min>100</min>
                <max>10000</max>
                <step>100</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>10000N</label>
                </text>
            </group>
        </group>
    </group>
    <!-- tow characteristic -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Elastic Constant</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>99999</label>
                <live>true</live>
                <format>%1.0fN</format>
                <property>/sim/hitches/winch/tow/elastic-constant</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0N</label>
                </text>
            </group>
            <slider>
                <name>elastic_constant</name>
                <property>/sim/hitches/winch/tow/elastic-constant</property>
                <min>0</min>
                <max>100000</max>
                <step>10</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>100000N</label>
                </text>
            </group>
        </group>
    </group>
    <!-- tow thickness -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Rope Diameter</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>99999</label>
                <live>true</live>
                <format>%2.1fmm</format>
                <property>/sim/hitches/winch/rope/rope-diameter-mm</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0.0mm</label>
                </text>
            </group>
            <slider>
                <name>rope_diameter</name>
                <property>/sim/hitches/winch/rope/rope-diameter-mm</property>
                <min>0</min>
                <max>50</max>
                <step>0.5</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>50.0mm</label>
                </text>
            </group>
        </group>
    </group>
    <!-- tow weight -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Weight per Meter</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>9999999</label>
                <live>true</live>
                <format>%1.3fkg/m</format>
                <property>/sim/hitches/winch/tow/weight-per-m-kg-m</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0.0kg/m</label>
                </text>
            </group>
            <slider>
                <name>rope_diameter</name>
                <property>/sim/hitches/winch/tow/weight-per-m-kg-m</property>
                <min>0</min>
                <max>1</max>
                <step>0.001</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>1.0kg/m</label>
                </text>
            </group>
        </group>
    </group>
    <layout>hbox</layout>
    <empty>
        <stretch>true</stretch>
    </empty>
    <text>
        <label>Winch Settings</label>
    </text>
    <empty>
        <stretch>true</stretch>
    </empty>
    <!-- max power -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Max. Power</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>9999999</label>
                <live>true</live>
                <format>%5.0fW</format>
                <property>/sim/hitches/winch/winch/max-power</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0W</label>
                </text>
            </group>
            <slider>
                <name>max_power</name>
                <property>/sim/hitches/winch/winch/max-power</property>
                <min>0</min>
                <max>200000</max>
                <step>1000.</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>200000W</label>
                </text>
            </group>
        </group>
    </group>
    <!-- max force -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Max. Force</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>9999999</label>
                <live>true</live>
                <format>%5.0fN</format>
                <property>/sim/hitches/winch/winch/max-force</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0N</label>
                </text>
            </group>
            <slider>
                <name>max_force</name>
                <property>/sim/hitches/winch/winch/max-force</property>
                <min>0</min>
                <max>10000</max>
                <step>100.</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>10000N</label>
                </text>
            </group>
        </group>
    </group>
    <!-- max winch speed -->
    <group>
        <layout>hbox</layout>
        <text>
            <halign>left</halign>
            <label>Max. Winch Speed</label>
        </text>
        <group>
            <layout>hbox</layout>
            <halign>right</halign>
            <text>
                <label>999999</label>
                <live>true</live>
                <format>%3.0fm/s</format>
                <property>/sim/hitches/winch/winch/max-speed-m-s</property>
                <color>
                    <red>0.2</red>
                    <green>0.9</green>
                    <blue>0.2</blue>
                </color>
            </text>
            <empty>
                <stretch>true</stretch>
            </empty>
            <vrule/>
            <group>
                <width>60</width>
                <text>
                    <label>0m/s</label>
                </text>
            </group>
            <slider>
                <name>max_speed</name>
                <property>/sim/hitches/winch/winch/max-speed-m-s</property>
                <min>0</min>
                <max>100</max>
                <step>1.</step>
                <binding>
                    <command>dialog-apply</command>
                </binding>
            </slider>
            <group>
                <width>60</width>
                <text>
                    <label>100m/s</label>
                </text>
            </group>
        </group>
    </group>
    <hrule/>
    <hrule/>
    <!-- bottom line -->
    <group>
        <layout>hbox</layout>
        <empty>
            <stretch>true</stretch>
        </empty>
        <button>
            <legend>Close</legend>
            <binding>
                <command>dialog-close</command>
            </binding>
        </button>
        <empty>
            <stretch>true</stretch>
        </empty>
    </group>
</PropertyList>
