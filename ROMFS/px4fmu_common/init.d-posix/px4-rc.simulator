#!/bin/sh
# shellcheck disable=SC2154

# Simulator IMU data provided at 250 Hz
param set-default IMU_INTEG_RATE 250

# For simulation, allow registering modes while armed for developer convenience
param set-default COM_MODE_ARM_CHK 1

if [ "$PX4_SIMULATOR" = "sihsim" ] || [ "$(param show -q SYS_AUTOSTART)" -eq "0" ]; then
    # Run SIH
    . px4-rc.sihsim

elif [ "$PX4_SIMULATOR" = "gz" ] || [ "$(param show -q SIM_GZ_EN)" = "1" ]; then
    # Run Gazebo (gz)
    . px4-rc.gzsim

elif [ "$PX4_SIM_MODEL" = "jmavsim_iris" ] || [ "$(param show -q SYS_AUTOSTART)" -eq "10017" ]; then
    # Run jMAVSim
    . px4-rc.jmavsim

else
    # Run simulator_mavlink module for interface with gazebo-classic
    . px4-rc.mavlinksim
fi
