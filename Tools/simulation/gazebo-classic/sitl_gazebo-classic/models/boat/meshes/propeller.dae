<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author/>
      <authoring_tool>FBX COLLADA exporter</authoring_tool>
      <comments/>
    </contributor>
    <created>2019-03-01T17:20:36Z</created>
    <keywords/>
    <modified>2019-03-01T17:20:36Z</modified>
    <revision/>
    <subject/>
    <title/>
    <unit meter="1.000000" name="centimeter"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_images>
    <image id="WamV_png-image" name="WamV_png">
      <init_from>WamV.png</init_from>
    </image>
  </library_images>
  <library_materials>
    <material id="WamV" name="WamV">
      <instance_effect url="#WamV-fx"/>
    </material>
  </library_materials>
  <library_effects>
    <effect id="WamV-fx" name="WamV">
      <profile_COMMON>
        <technique sid="standard">
          <phong>
            <emission>
              <color sid="emission">0.000000  0.000000 0.000000 1.000000</color>
            </emission>
            <ambient>
              <color sid="ambient">0.588235  0.588235 0.588235 1.000000</color>
            </ambient>
            <diffuse>
              <texture texture="WamV_png-image" texcoord="CHANNEL0">
                <extra>
                  <technique profile="MAYA">
                    <wrapU sid="wrapU0">TRUE</wrapU>
                    <wrapV sid="wrapV0">TRUE</wrapV>
                    <blend_mode>ADD</blend_mode>
                  </technique>
                </extra>
              </texture>
            </diffuse>
            <specular>
              <color sid="specular">0.000000  0.000000 0.000000 1.000000</color>
            </specular>
            <shininess>
              <float sid="shininess">9.999999</float>
            </shininess>
            <reflective>
              <color sid="reflective">0.000000  0.000000 0.000000 1.000000</color>
            </reflective>
            <reflectivity>
              <float sid="reflectivity">1.000000</float>
            </reflectivity>
            <transparent opaque="RGB_ZERO">
              <color sid="transparent">1.000000  1.000000 1.000000 1.000000</color>
            </transparent>
            <transparency>
              <float sid="transparency">0.000000</float>
            </transparency>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_geometries>
    <geometry id="Prop_Left-lib" name="Prop_LeftMesh">
      <mesh>
        <source id="Prop_Left-POSITION">
          <float_array id="Prop_Left-POSITION-array" count="231">
-125.617302 100.276398 -37.149891
-125.617302 102.714600 -36.496601
-125.617302 105.152702 -37.149891
-140.610199 100.276398 -37.149891
-140.610199 102.714600 -36.496601
-140.610199 105.152702 -37.149899
-142.274002 99.403641 -35.638180
-142.274002 102.714600 -34.751019
-142.274002 106.025497 -35.638180
-142.029007 105.989197 -20.267220
-139.638397 105.250999 -38.274071
-127.209900 96.424698 -37.796532
-129.762695 94.380623 -33.848759
-133.684494 93.492668 -26.742399
-139.226501 99.953644 -18.319611
-141.642700 104.808899 -18.342480
-136.049805 100.476997 -31.609739
-136.286194 101.734703 -30.195049
-136.620407 95.545090 -21.068319
-125.695503 98.459900 -39.092319
-131.039703 104.467598 -38.627621
-140.378006 105.479401 -32.703129
-125.617302 106.936699 -38.935020
-125.617302 107.589104 -41.373451
-125.617302 106.935799 -43.811581
-125.617302 105.151001 -45.596420
-125.617302 102.712997 -46.248840
-140.610199 106.936699 -38.935032
-140.610199 107.589104 -41.373451
-140.610199 106.935799 -43.811581
-140.610199 105.151001 -45.596420
-140.610199 102.712997 -46.248829
-142.274002 108.448402 -38.062229
-142.274002 109.334702 -41.373451
-142.274002 108.447502 -44.684380
-142.274002 106.023804 -47.108139
-142.274002 102.712997 -47.994419
-142.274002 102.713501 -41.372669
-125.617302 102.713501 -41.372669
-142.029007 119.353600 -54.762211
-139.638397 104.128304 -45.119480
-127.209900 108.955002 -37.714439
-129.762695 113.395897 -37.918091
-133.684494 119.994202 -40.702290
-139.226501 124.057999 -50.509048
-141.642700 121.610603 -54.702351
-136.049805 112.286697 -44.317268
-136.286194 112.883003 -46.113781
-136.620407 123.881897 -45.316780
-125.695503 106.815201 -38.829090
-131.039703 104.213799 -44.264198
-140.378006 108.838699 -48.102741
-125.617302 100.275101 -45.594662
-125.617302 98.490227 -43.809818
-125.617302 97.836937 -41.371681
-125.617302 98.490913 -38.934139
-140.610199 100.275101 -45.594650
-140.610199 98.490242 -43.809811
-140.610199 97.836937 -41.371681
-140.610199 98.490913 -38.934139
-142.274002 99.402283 -47.106369
-142.274002 96.978523 -44.682610
-142.274002 96.091347 -41.371681
-142.274002 96.979187 -38.061352
-142.029007 82.797852 -49.088570
-139.638397 98.761337 -40.724449
-127.209900 102.760902 -48.607029
-129.762695 100.364098 -52.351139
-133.684494 94.653801 -56.673309
-139.226501 84.128967 -55.289341
-141.642700 81.721161 -51.073170
-136.049805 95.376846 -48.190990
-136.286194 93.522873 -47.809158
-136.620407 88.713692 -57.732899
-125.695503 102.865501 -46.196602
-131.039703 99.459267 -41.226170
-140.378006 93.822571 -43.312130
</float_array>
          <technique_common>
            <accessor source="#Prop_Left-POSITION-array" count="77" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Prop_Left-Normal0">
          <float_array id="Prop_Left-Normal0-array" count="1242">
0.000000 -0.865857 0.500292
0.419332 -0.453768 0.786292
0.419363 -0.786038 0.454179
-0.000000 -0.499847 0.866114
0.419351 0.000000 0.907824
0.419332 -0.453768 0.786292
-0.000000 0.000000 1.000000
0.419377 0.454030 0.786117
0.419351 0.000000 0.907824
-0.000000 0.500139 0.865945
0.419363 0.786351 0.453637
0.419377 0.454030 0.786117
0.419363 -0.786038 0.454179
0.723841 -0.344901 0.597577
0.723844 -0.597443 0.345127
0.419332 -0.453768 0.786292
0.723853 0.000000 0.689954
0.723841 -0.344901 0.597577
0.419351 0.000000 0.907824
0.723853 0.345040 0.597481
0.723853 0.000000 0.689954
0.419377 0.454030 0.786117
0.723843 0.597611 0.344837
0.723853 0.345040 0.597481
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
-0.692159 -0.610187 -0.385472
-0.409379 -0.831610 -0.375278
-0.412488 -0.633451 -0.654671
0.882382 0.373786 0.285809
0.821688 0.420528 0.384688
0.708571 0.590210 0.386755
-0.880474 -0.416297 -0.226852
-0.850136 -0.487417 -0.199235
-0.692159 -0.610187 -0.385472
-0.850136 -0.487417 -0.199235
-0.808548 -0.569079 -0.149666
-0.692159 -0.610187 -0.385472
-0.877485 -0.414426 -0.241396
-0.880474 -0.416297 -0.226852
-0.692159 -0.610187 -0.385472
-0.618179 -0.180544 -0.765022
-0.666532 -0.476965 -0.572921
-0.692159 -0.610187 -0.385472
-0.692159 -0.610187 -0.385472
-0.666532 -0.476965 -0.572921
-0.790342 -0.507178 -0.343700
-0.692159 -0.610187 -0.385472
-0.790342 -0.507178 -0.343700
-0.877485 -0.414426 -0.241396
0.821688 0.420528 0.384688
0.732452 0.363833 0.575448
0.708571 0.590210 0.386755
0.762697 0.622835 0.174270
0.829214 0.502684 0.244362
0.708571 0.590210 0.386755
0.524497 0.706348 0.475370
0.089520 0.995565 -0.028930
0.441854 0.874449 0.200261
0.829214 0.502684 0.244362
0.874213 0.399242 0.276328
0.708571 0.590210 0.386755
0.874213 0.399242 0.276328
0.882382 0.373786 0.285809
0.708571 0.590210 0.386755
0.708571 0.590210 0.386755
0.732452 0.363833 0.575448
0.674406 0.106901 0.730581
0.708571 0.590210 0.386755
0.666886 0.240887 0.705150
0.524497 0.706348 0.475370
0.708571 0.590210 0.386755
0.674406 0.106901 0.730581
0.666886 0.240887 0.705150
-0.598472 -0.255981 -0.759147
-0.618179 -0.180544 -0.765022
-0.692159 -0.610187 -0.385472
-0.412488 -0.633451 -0.654671
-0.598472 -0.255981 -0.759147
-0.692159 -0.610187 -0.385472
-0.692159 -0.610187 -0.385472
-0.808548 -0.569079 -0.149666
-0.746428 -0.661563 -0.071973
0.441854 0.874449 0.200261
0.762697 0.622835 0.174270
0.708571 0.590210 0.386755
0.441854 0.874449 0.200261
0.708571 0.590210 0.386755
0.524497 0.706348 0.475370
-0.692159 -0.610187 -0.385472
-0.746428 -0.661563 -0.071973
-0.409379 -0.831610 -0.375278
-0.000000 0.866194 0.499707
0.419332 0.907833 -0.000172
0.419363 0.786351 0.453637
-0.000000 1.000000 -0.000178
0.419351 0.786199 -0.453911
0.419332 0.907833 -0.000172
0.000000 0.866025 -0.500001
0.419377 0.453782 -0.786260
0.419351 0.786199 -0.453911
-0.000000 0.499861 -0.866105
0.419363 -0.000313 -0.907819
0.419377 0.453782 -0.786260
0.419363 0.786351 0.453637
0.723841 0.689966 -0.000095
0.723843 0.597611 0.344837
0.419332 0.907833 -0.000172
0.723853 0.597518 -0.344976
0.723841 0.689966 -0.000095
0.419351 0.786199 -0.453911
0.723854 0.344913 -0.597554
0.723853 0.597518 -0.344976
0.419377 0.453782 -0.786260
0.723843 -0.000167 -0.689965
0.723854 0.344913 -0.597554
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
-0.692159 -0.028735 0.721173
-0.409380 0.090805 0.907834
-0.412488 -0.250236 0.875920
0.882382 0.060625 -0.466612
0.821688 0.122885 -0.556532
0.708571 0.039835 -0.704514
-0.880474 0.011689 0.473950
-0.850135 0.071166 0.521733
-0.692159 -0.028735 0.721173
-0.850135 0.071166 0.521733
-0.808547 0.154925 0.567670
-0.692159 -0.028735 0.721173
-0.877485 -0.001842 0.479601
-0.880474 0.011689 0.473950
-0.692159 -0.028735 0.721173
-0.618179 -0.572256 0.538867
-0.666532 -0.257681 0.699525
-0.692159 -0.028735 0.721173
-0.692159 -0.028735 0.721173
-0.666532 -0.257681 0.699525
-0.790342 -0.044064 0.611079
-0.692159 -0.028735 0.721173
-0.790342 -0.044064 0.611079
-0.877485 -0.001842 0.479601
0.821688 0.122885 -0.556532
0.732452 0.316436 -0.602812
0.708571 0.039835 -0.704514
0.762697 -0.160495 -0.626526
0.829214 -0.039718 -0.557518
0.708571 0.039835 -0.704514
0.524496 0.058509 -0.849400
0.089521 -0.522836 -0.847720
0.441854 -0.263793 -0.857425
0.829214 -0.039718 -0.557518
0.874213 0.039685 -0.483918
0.708571 0.039835 -0.704514
0.874213 0.039685 -0.483918
0.882382 0.060625 -0.466612
0.708571 0.039835 -0.704514
0.708571 0.039835 -0.704514
0.732452 0.316436 -0.602812
0.674406 0.579251 -0.457870
0.708571 0.039835 -0.704514
0.666886 0.490234 -0.561190
0.524496 0.058509 -0.849400
0.708571 0.039835 -0.704514
0.674406 0.579251 -0.457870
0.666886 0.490234 -0.561190
-0.598472 -0.529450 0.601260
-0.618179 -0.572256 0.538867
-0.692159 -0.028735 0.721173
-0.412488 -0.250236 0.875920
-0.598472 -0.529450 0.601260
-0.692159 -0.028735 0.721173
-0.692159 -0.028735 0.721173
-0.808547 0.154925 0.567670
-0.746428 0.268451 0.608917
0.441854 -0.263793 -0.857425
0.762697 -0.160495 -0.626526
0.708571 0.039835 -0.704514
0.441854 -0.263793 -0.857425
0.708571 0.039835 -0.704514
0.524496 0.058509 -0.849400
-0.692159 -0.028735 0.721173
-0.746428 0.268451 0.608917
-0.409380 0.090805 0.907834
-0.000000 -0.000337 -1.000000
0.419333 -0.454065 -0.786120
0.419363 -0.000313 -0.907819
-0.000001 -0.500154 -0.865936
0.419352 -0.786199 -0.453911
0.419333 -0.454065 -0.786120
-0.000000 -0.866026 -0.499999
0.419378 -0.907812 0.000142
0.419352 -0.786199 -0.453911
-0.000000 -1.000000 0.000161
0.419363 -0.786038 0.454179
0.419378 -0.907812 0.000142
0.419363 -0.000313 -0.907819
0.723841 -0.345066 -0.597481
0.723843 -0.000167 -0.689965
0.419333 -0.454065 -0.786120
0.723855 -0.597516 -0.344976
0.723841 -0.345066 -0.597481
0.419352 -0.786199 -0.453911
0.723855 -0.689952 0.000073
0.723855 -0.597516 -0.344976
0.419378 -0.907812 0.000142
0.723844 -0.597443 0.345127
0.723855 -0.689952 0.000073
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
-1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
1.000000 0.000000 0.000000
-0.692159 0.638922 -0.335701
-0.409380 0.740805 -0.532556
-0.412488 0.883687 -0.221250
0.882382 -0.434410 0.180804
0.821688 -0.543414 0.171845
0.708571 -0.630045 0.317759
-0.880474 0.404608 -0.247098
-0.850136 0.416251 -0.322498
-0.692159 0.638922 -0.335701
-0.850136 0.416251 -0.322498
-0.808548 0.414154 -0.418003
-0.692159 0.638922 -0.335701
-0.877485 0.416268 -0.238205
-0.880474 0.404608 -0.247098
-0.692159 0.638922 -0.335701
-0.618179 0.752800 0.226155
-0.666532 0.734647 -0.126604
-0.692159 0.638922 -0.335701
-0.692159 0.638922 -0.335701
-0.666532 0.734647 -0.126604
-0.790342 0.551242 -0.267379
-0.692159 0.638922 -0.335701
-0.790342 0.551242 -0.267379
-0.877485 0.416268 -0.238205
0.821688 -0.543414 0.171845
0.732451 -0.680269 0.027365
0.708571 -0.630045 0.317759
0.762697 -0.462340 0.452255
0.829214 -0.462966 0.313156
0.708571 -0.630045 0.317759
0.524497 -0.764856 0.374030
0.089521 -0.472728 0.876649
0.441855 -0.610655 0.657164
0.829214 -0.462966 0.313156
0.874213 -0.438928 0.207590
0.708571 -0.630045 0.317759
0.874213 -0.438928 0.207590
0.882382 -0.434410 0.180804
0.708571 -0.630045 0.317759
0.708571 -0.630045 0.317759
0.732451 -0.680269 0.027365
0.674406 -0.686153 -0.272711
0.708571 -0.630045 0.317759
0.666886 -0.731122 -0.143960
0.524497 -0.764856 0.374030
0.708571 -0.630045 0.317759
0.674406 -0.686153 -0.272711
0.666886 -0.731122 -0.143960
-0.598472 0.785431 0.157887
-0.618179 0.752800 0.226155
-0.692159 0.638922 -0.335701
-0.412488 0.883687 -0.221250
-0.598472 0.785431 0.157887
-0.692159 0.638922 -0.335701
-0.692159 0.638922 -0.335701
-0.808548 0.414154 -0.418003
-0.746428 0.393112 -0.536944
0.441855 -0.610655 0.657164
0.762697 -0.462340 0.452255
0.708571 -0.630045 0.317759
0.441855 -0.610655 0.657164
0.708571 -0.630045 0.317759
0.524497 -0.764856 0.374030
-0.692159 0.638922 -0.335701
-0.746428 0.393112 -0.536944
-0.409380 0.740805 -0.532556
0.000000 -0.865857 0.500292
-0.000000 -0.499847 0.866114
0.419332 -0.453768 0.786292
-0.000000 -0.499847 0.866114
-0.000000 0.000000 1.000000
0.419351 0.000000 0.907824
-0.000000 0.000000 1.000000
-0.000000 0.500139 0.865945
0.419377 0.454030 0.786117
-0.000000 0.500139 0.865945
-0.000000 0.866194 0.499707
0.419363 0.786351 0.453637
0.419363 -0.786038 0.454179
0.419332 -0.453768 0.786292
0.723841 -0.344901 0.597577
0.419332 -0.453768 0.786292
0.419351 0.000000 0.907824
0.723853 0.000000 0.689954
0.419351 0.000000 0.907824
0.419377 0.454030 0.786117
0.723853 0.345040 0.597481
0.419377 0.454030 0.786117
0.419363 0.786351 0.453637
0.723843 0.597611 0.344837
-0.000000 0.866194 0.499707
-0.000000 1.000000 -0.000178
0.419332 0.907833 -0.000172
-0.000000 1.000000 -0.000178
0.000000 0.866025 -0.500001
0.419351 0.786199 -0.453911
0.000000 0.866025 -0.500001
-0.000000 0.499861 -0.866105
0.419377 0.453782 -0.786260
-0.000000 0.499861 -0.866105
-0.000000 -0.000337 -1.000000
0.419363 -0.000313 -0.907819
0.419363 0.786351 0.453637
0.419332 0.907833 -0.000172
0.723841 0.689966 -0.000095
0.419332 0.907833 -0.000172
0.419351 0.786199 -0.453911
0.723853 0.597518 -0.344976
0.419351 0.786199 -0.453911
0.419377 0.453782 -0.786260
0.723854 0.344913 -0.597554
0.419377 0.453782 -0.786260
0.419363 -0.000313 -0.907819
0.723843 -0.000167 -0.689965
-0.000000 -0.000337 -1.000000
-0.000001 -0.500154 -0.865936
0.419333 -0.454065 -0.786120
-0.000001 -0.500154 -0.865936
-0.000000 -0.866026 -0.499999
0.419352 -0.786199 -0.453911
-0.000000 -0.866026 -0.499999
-0.000000 -1.000000 0.000161
0.419378 -0.907812 0.000142
-0.000000 -1.000000 0.000161
0.000000 -0.865857 0.500292
0.419363 -0.786038 0.454179
0.419363 -0.000313 -0.907819
0.419333 -0.454065 -0.786120
0.723841 -0.345066 -0.597481
0.419333 -0.454065 -0.786120
0.419352 -0.786199 -0.453911
0.723855 -0.597516 -0.344976
0.419352 -0.786199 -0.453911
0.419378 -0.907812 0.000142
0.723855 -0.689952 0.000073
0.419378 -0.907812 0.000142
0.419363 -0.786038 0.454179
0.723844 -0.597443 0.345127
</float_array>
          <technique_common>
            <accessor source="#Prop_Left-Normal0-array" count="414" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Prop_Left-UV0">
          <float_array id="Prop_Left-UV0-array" count="306">
0.968389 0.555248
0.960242 0.555278
0.960303 0.506439
0.968216 0.506439
0.952075 0.555345
0.951921 0.506439
0.943909 0.555330
0.943533 0.506439
0.935761 0.555354
0.935622 0.506439
0.961295 0.499748
0.970525 0.499748
0.952075 0.499748
0.942764 0.499748
0.933265 0.499748
0.915144 0.383334
0.945710 0.394972
0.922876 0.375602
0.958731 0.040580
0.937162 0.080578
0.934998 0.023755
0.958251 0.085187
0.941163 0.086320
0.973774 0.075433
0.983663 0.006713
0.986131 0.015034
0.936019 0.165295
0.986467 0.030899
0.982719 0.056405
0.933439 0.393897
0.933439 0.372772
0.944001 0.375602
0.951733 0.383334
0.951404 0.400665
0.937932 0.408444
0.953488 0.408444
0.951404 0.416221
0.945710 0.421915
0.949858 0.181241
0.925947 0.199553
0.959410 0.205257
0.899262 0.170300
0.901667 0.163711
0.908415 0.226549
0.918422 0.160239
0.961993 0.221145
0.960613 0.229867
0.935116 0.231605
0.958433 0.009649
0.908613 0.208863
0.936948 0.040815
0.968389 0.555248
0.960242 0.555278
0.960303 0.506439
0.968216 0.506439
0.952075 0.555345
0.951921 0.506439
0.943909 0.555330
0.943533 0.506439
0.935761 0.555354
0.935622 0.506439
0.961295 0.499748
0.970525 0.499748
0.952075 0.499748
0.942764 0.499748
0.933265 0.499748
0.915144 0.383334
0.945710 0.394972
0.922876 0.375602
0.958731 0.040580
0.937162 0.080578
0.934998 0.023755
0.958251 0.085187
0.941163 0.086320
0.973774 0.075433
0.983663 0.006713
0.986131 0.015034
0.936019 0.165295
0.986467 0.030899
0.982719 0.056405
0.933439 0.393897
0.933439 0.372772
0.944001 0.375602
0.951733 0.383334
0.951404 0.400665
0.937932 0.408444
0.953488 0.408444
0.951404 0.416221
0.945710 0.421915
0.949858 0.181241
0.925947 0.199553
0.959410 0.205257
0.899262 0.170300
0.901667 0.163711
0.908415 0.226549
0.918422 0.160239
0.961993 0.221145
0.960613 0.229867
0.935116 0.231605
0.958433 0.009649
0.908613 0.208863
0.936948 0.040815
0.968389 0.555248
0.960242 0.555278
0.960303 0.506439
0.968216 0.506439
0.952075 0.555345
0.951921 0.506439
0.943909 0.555330
0.943533 0.506439
0.935761 0.555354
0.935622 0.506439
0.961295 0.499748
0.970525 0.499748
0.952075 0.499748
0.942764 0.499748
0.933265 0.499748
0.915144 0.383334
0.945710 0.394972
0.922876 0.375602
0.958731 0.040580
0.937162 0.080578
0.934998 0.023755
0.958251 0.085187
0.941163 0.086320
0.973774 0.075433
0.983663 0.006713
0.986131 0.015034
0.936019 0.165295
0.986467 0.030899
0.982719 0.056405
0.933439 0.393897
0.933439 0.372772
0.944001 0.375602
0.951733 0.383334
0.951404 0.400665
0.937932 0.408444
0.953488 0.408444
0.951404 0.416221
0.945710 0.421915
0.949858 0.181241
0.925947 0.199553
0.959410 0.205257
0.899262 0.170300
0.901667 0.163711
0.908415 0.226549
0.918422 0.160239
0.961993 0.221145
0.960613 0.229867
0.935116 0.231605
0.958433 0.009649
0.908613 0.208863
0.936948 0.040815
</float_array>
          <technique_common>
            <accessor source="#Prop_Left-UV0-array" count="153" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Prop_Left-VERTEX">
          <input semantic="POSITION" source="#Prop_Left-POSITION"/>
        </vertices>
        <triangles count="138" material="WamV">
          <input semantic="VERTEX" offset="0" source="#Prop_Left-VERTEX"/>
          <input semantic="NORMAL" offset="1" source="#Prop_Left-Normal0"/>
          <input semantic="TEXCOORD" offset="2" set="0" source="#Prop_Left-UV0"/>
          <p> 55 0 0 3 1 2 59 2 3 0 3 1 4 4 5 3 5 2 1 6 4 5 7 7 4 8 5 2 9 6 27 10 9 5 11 7 59 12 3 6 13 10 63 14 11 3 15 2 7 16 12 6 17 10 4 18 5 8 19 13 7 20 12 5 21 7 32 22 14 8 23 13 63 24 15 6 25 17 37 26 29 6 27 17 7 28 30 37 29 29 7 30 30 8 31 31 37 32 29 8 33 31 32 34 32 37 35 29 22 36 16 2 37 33 38 38 34 2 39 33 1 40 35 38 41 34 1 42 35 0 43 36 38 44 34 0 45 36 55 46 37 38 47 34 16 48 18 10 49 20 20 50 48 18 51 26 13 52 38 17 53 39 14 54 21 15 55 22 16 56 18 15 57 22 9 58 19 16 59 18 18 60 23 14 61 21 16 62 18 11 63 25 12 64 27 16 65 18 16 66 18 12 67 27 13 68 28 16 69 18 13 70 28 18 71 23 13 72 38 12 73 40 17 74 39 9 75 41 15 76 42 17 77 39 20 78 47 10 79 43 21 80 49 15 81 42 14 82 44 17 83 39 14 84 44 18 85 26 17 86 39 17 87 39 12 88 40 11 89 45 17 90 39 19 91 46 20 92 47 17 93 39 11 94 45 19 95 46 19 96 24 11 97 25 16 98 18 20 99 48 19 100 24 16 101 18 16 102 18 9 103 19 21 104 50 21 105 49 9 106 41 17 107 39 21 108 49 17 109 39 20 110 47 16 111 18 21 112 50 10 113 20 22 114 51 28 115 53 27 116 54 23 117 52 29 118 56 28 119 53 24 120 55 30 121 58 29 122 56 25 123 57 31 124 60 30 125 58 27 126 54 33 127 61 32 128 62 28 129 53 34 130 63 33 131 61 29 132 56 35 133 64 34 134 63 30 135 58 36 136 65 35 137 64 32 138 66 33 139 68 37 140 80 33 141 68 34 142 81 37 143 80 34 144 81 35 145 82 37 146 80 35 147 82 36 148 83 37 149 80 26 150 67 25 151 84 38 152 85 25 153 84 24 154 86 38 155 85 24 156 86 23 157 87 38 158 85 23 159 87 22 160 88 38 161 85 46 162 69 40 163 71 50 164 99 48 165 77 43 166 89 47 167 90 44 168 72 45 169 73 46 170 69 45 171 73 39 172 70 46 173 69 48 174 74 44 175 72 46 176 69 41 177 76 42 178 78 46 179 69 46 180 69 42 181 78 43 182 79 46 183 69 43 184 79 48 185 74 43 186 89 42 187 91 47 188 90 39 189 92 45 190 93 47 191 90 50 192 98 40 193 94 51 194 100 45 195 93 44 196 95 47 197 90 44 198 95 48 199 77 47 200 90 47 201 90 42 202 91 41 203 96 47 204 90 49 205 97 50 206 98 47 207 90 41 208 96 49 209 97 49 210 75 41 211 76 46 212 69 50 213 99 49 214 75 46 215 69 46 216 69 39 217 70 51 218 101 51 219 100 39 220 92 47 221 90 51 222 100 47 223 90 50 224 98 46 225 69 51 226 101 40 227 71 26 228 102 56 229 104 31 230 105 52 231 103 57 232 107 56 233 104 53 234 106 58 235 109 57 236 107 54 237 108 59 238 111 58 239 109 31 240 105 60 241 112 36 242 113 56 243 104 61 244 114 60 245 112 57 246 107 62 247 115 61 248 114 58 249 109 63 250 116 62 251 115 36 252 117 60 253 119 37 254 131 60 255 119 61 256 132 37 257 131 61 258 132 62 259 133 37 260 131 62 261 133 63 262 134 37 263 131 55 264 118 54 265 135 38 266 136 54 267 135 53 268 137 38 269 136 53 270 137 52 271 138 38 272 136 52 273 138 26 274 139 38 275 136 71 276 120 65 277 122 75 278 150 73 279 128 68 280 140 72 281 141 69 282 123 70 283 124 71 284 120 70 285 124 64 286 121 71 287 120 73 288 125 69 289 123 71 290 120 66 291 127 67 292 129 71 293 120 71 294 120 67 295 129 68 296 130 71 297 120 68 298 130 73 299 125 68 300 140 67 301 142 72 302 141 64 303 143 70 304 144 72 305 141 75 306 149 65 307 145 76 308 151 70 309 144 69 310 146 72 311 141 69 312 146 73 313 128 72 314 141 72 315 141 67 316 142 66 317 147 72 318 141 74 319 148 75 320 149 72 321 141 66 322 147 74 323 148 74 324 126 66 325 127 71 326 120 75 327 150 74 328 126 71 329 120 71 330 120 64 331 121 76 332 152 76 333 151 64 334 143 72 335 141 76 336 151 72 337 141 75 338 149 71 339 120 76 340 152 65 341 122 55 342 0 0 343 1 3 344 2 0 345 1 1 346 4 4 347 5 1 348 4 2 349 6 5 350 7 2 351 6 22 352 8 27 353 9 59 354 3 3 355 2 6 356 10 3 357 2 4 358 5 7 359 12 4 360 5 5 361 7 8 362 13 5 363 7 27 364 9 32 365 14 22 366 51 23 367 52 28 368 53 23 369 52 24 370 55 29 371 56 24 372 55 25 373 57 30 374 58 25 375 57 26 376 59 31 377 60 27 378 54 28 379 53 33 380 61 28 381 53 29 382 56 34 383 63 29 384 56 30 385 58 35 386 64 30 387 58 31 388 60 36 389 65 26 390 102 52 391 103 56 392 104 52 393 103 53 394 106 57 395 107 53 396 106 54 397 108 58 398 109 54 399 108 55 400 110 59 401 111 31 402 105 56 403 104 60 404 112 56 405 104 57 406 107 61 407 114 57 408 107 58 409 109 62 410 115 58 411 109 59 412 111 63 413 116</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="propeller" name="propeller">
      <node name="Prop_Left" id="Prop_Left" sid="Prop_Left">
        <matrix sid="matrix">0.010000 0.000000 0.000000 1.256173 0.000000 0.010000 0.000000 -1.027135 0.000000 0.000000 0.010000 0.413727 0.000000 0.000000 0.000000 1.000000</matrix>
        <instance_geometry url="#Prop_Left-lib">
          <bind_material>
            <technique_common>
              <instance_material symbol="WamV" target="#WamV"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="FCOLLADA">
            <visibility>1.000000</visibility>
          </technique>
        </extra>
      </node>
      <extra>
        <technique profile="MAX3D">
          <frame_rate>30.000000</frame_rate>
        </technique>
        <technique profile="FCOLLADA">
          <start_time>0.000000</start_time>
          <end_time>3.333333</end_time>
        </technique>
      </extra>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#propeller"/>
  </scene>
</COLLADA>
