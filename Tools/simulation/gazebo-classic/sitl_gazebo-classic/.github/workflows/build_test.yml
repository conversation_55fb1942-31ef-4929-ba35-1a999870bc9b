name: Build Tests

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        container:
        - 'px4io/px4-dev-simulation-focal:2021-05-31' # Gazebo 11
    container: ${{ matrix.container }}
    steps:
    - uses: actions/checkout@v1
    - name: submodule update
      run: git submodule update --init --recursive
    - name: Install latest mavlink
      run: git clone --depth 1 https://github.com/mavlink/c_library_v2.git /usr/local/include/mavlink && rm -rf /usr/local/include/mavlink/.git
    - name: Cmake Build
      run: |
        mkdir build
        cd build
        cmake ..
        make
    - name: Unit Tests
      working-directory: build
      run: |
        cmake -DENABLE_UNIT_TESTS=On ..
        make
        make test
