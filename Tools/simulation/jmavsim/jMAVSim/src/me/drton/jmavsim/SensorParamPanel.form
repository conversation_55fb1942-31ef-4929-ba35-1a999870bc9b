<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="me.drton.jmavsim.SensorParamPanel">
  <grid id="27dc6" binding="mainPanel" layout-manager="GridLayoutManager" row-count="8" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="0" left="0" bottom="0" right="0"/>
    <constraints>
      <xy x="20" y="20" width="500" height="261"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <component id="eeb85" class="javax.swing.JLabel">
        <constraints>
          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Accel Noise StdDev"/>
        </properties>
      </component>
      <component id="b28e0" class="javax.swing.JSpinner" binding="accelSpinner">
        <constraints>
          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
      <component id="d01db" class="javax.swing.JLabel">
        <constraints>
          <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Gyro Noise StdDev"/>
        </properties>
      </component>
      <component id="1d206" class="javax.swing.JSpinner" binding="gyroSpinner">
        <constraints>
          <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
      <component id="db1b1" class="javax.swing.JLabel">
        <constraints>
          <grid row="3" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Mag Noise StdDev"/>
        </properties>
      </component>
      <component id="75b59" class="javax.swing.JLabel">
        <constraints>
          <grid row="4" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="Pressure Noise StdDev"/>
        </properties>
      </component>
      <component id="a8c5e" class="javax.swing.JLabel">
        <constraints>
          <grid row="5" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="GPS Noise StdDev"/>
        </properties>
      </component>
      <component id="3daf3" class="javax.swing.JSpinner" binding="magSpinner">
        <constraints>
          <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
      <component id="98010" class="javax.swing.JSpinner" binding="presSpinner">
        <constraints>
          <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
      <component id="a4cd8" class="javax.swing.JSpinner" binding="gpsSpinner">
        <constraints>
          <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
      <component id="3d1a1" class="javax.swing.JLabel">
        <constraints>
          <grid row="6" column="0" row-span="1" col-span="2" vsize-policy="0" hsize-policy="0" anchor="0" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="--- VEHICLE ---"/>
        </properties>
      </component>
      <component id="dec3b" class="javax.swing.JLabel">
        <constraints>
          <grid row="7" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="mass"/>
        </properties>
      </component>
      <component id="ec391" class="javax.swing.JSpinner" binding="massSpinner">
        <constraints>
          <grid row="7" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
      </component>
      <component id="df8a0" class="javax.swing.JLabel">
        <constraints>
          <grid row="0" column="0" row-span="1" col-span="2" vsize-policy="0" hsize-policy="0" anchor="0" fill="0" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties>
          <text value="--- SENSORS ---"/>
        </properties>
      </component>
    </children>
  </grid>
</form>
