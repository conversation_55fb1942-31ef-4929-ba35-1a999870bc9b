<?xml version="1.0" ?>
<sdf version="1.4">
  <model name="camera">
    <link name="link">
      <inertial>
        <pose>0.01 0.025 0.025 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <geometry>
          <box>
            <size>0.02 0.05 0.05</size>
          </box>
        </geometry>
      </visual>

      <sensor name="IRLock" type="logical_camera">
        <always_on>true</always_on>
        <update_rate>50.0</update_rate> <!-- according to IRLock spec -->
        <visualize>true</visualize>
        <topic>/irlock</topic>
        <logical_camera>
          <horizontal_fov>0.611</horizontal_fov> <!-- 35 deg -->
          <near>0.1</near>
          <far>15</far> <!-- detection range according to IRLock spec -->
          <aspect_ratio>1.71</aspect_ratio>
        </logical_camera>
        <plugin name="irlock_plugin" filename="libgazebo_irlock_plugin.so">
            <robotNamespace></robotNamespace>
            <beaconName>land_pad</beaconName> <!-- name of the model we want to detect (as defined in the sdf)-->
        </plugin>
      </sensor>
    </link>

  </model>
</sdf>
<!-- vim: set et fenc=utf-8 ff=unix sts=0 sw=4 ts=4 : -->