# Community Supported & Experimental Autopilots

:::tip
For more information about PX4 project autopilot board support levels see: [px4.io/autopilots/](https://px4.io/autopilots/).
:::

## Experimental Autopilots

This category is for experimental autopilots (and autopilot "platforms") that are _not supported_ by either the PX4 project team or by a manufacturer.

- [BeagleBone Blue](../flight_controller/beaglebone_blue.md)
- [Raspberry Pi 2/3 Navio2](../flight_controller/raspberry_pi_navio2.md)
- [Raspberry Pi 2/3/4 PilotPi Shield](../flight_controller/raspberry_pi_pilotpi.md)

Boards in this category must work with at least one PX4 release for a defined vehicle type.
They may not be compatible with the current PX4 release, and compatibility is not maintained by the project for future releases.

## Experimental Vehicles

These are [complete vehicles](../complete_vehicles_mc/index.md) that have a fully integrated autopilot and other hardware (i.e. unlike the other autopilots listed, you can't use them in your own builds).
They are listed in this page because from a PX4 software perspective, they are another autopilot.

- [Bitcraze Crazyflie 2.0](../complete_vehicles_mc/crazyflie2.md) ([Complete Vehicle](../complete_vehicles_mc/index.md))
- [Bitcraze Crazyflie 2.1](../complete_vehicles_mc/crazyflie21.md) ([Complete Vehicle](../complete_vehicles_mc/index.md))
