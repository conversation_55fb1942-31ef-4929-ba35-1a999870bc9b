root = true

[*]
indent_style = space
indent_size = tab
tab_width = 4
insert_final_newline = true

[*.xml]
tab_width = 2
trim_trailing_whitespace = true

[*.php]
insert_final_newline = false

[*.css]
insert_final_newline = false

[*.py]
# Otherwise Python language server complains
trim_trailing_whitespace = true

[*.xsl]
trim_trailing_whitespace = true

[*.md]
trim_trailing_whitespace = true
tab_width = 2

[*.json]
tab_width = 2
trim_trailing_whitespace = true

[*.{yml,yaml}]
tab_width = 2

[*.{c,cpp,cc,h,hpp}]
indent_style = tab
tab_width = 4
trim_trailing_whitespace = true
