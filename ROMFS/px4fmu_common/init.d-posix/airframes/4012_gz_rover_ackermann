#!/bin/sh
# @name <PERSON> Ackermann
# @type Rover
# @class Rover

. ${R}etc/init.d/rc.rover_ackermann_defaults

PX4_SIMULATOR=${PX4_SIMULATOR:=gz}
PX4_GZ_WORLD=${PX4_GZ_WORLD:=rover}
PX4_SIM_MODEL=${PX4_SIM_MODEL:=rover_ackermann}

param set-default SIM_GZ_EN 1 # Gazebo bridge

param set-default NAV_ACC_RAD 0.5

# Ackermann Parameters
param set-default RA_WHEEL_BASE 0.321
param set-default RA_ACC_RAD_GAIN 2
param set-default RA_ACC_RAD_MAX 3
param set-default RA_MAX_STR_ANG 0.5236
param set-default RA_STR_RATE_LIM 360

# Rover Control Parameters
param set-default RO_ACCEL_LIM 3
param set-default RO_DECEL_LIM 6
param set-default RO_JERK_LIM 15
param set-default RO_MAX_THR_SPEED 3.1

# Rover Rate Control Parameters
param set-default RO_YAW_RATE_I 0.1
param set-default RO_YAW_RATE_P 1
param set-default RO_YAW_RATE_LIM 180
param set-default RO_YAW_RATE_CORR 1

# Rover Attitude Control Parameters
param set-default RO_YAW_P 3

# Rover Velocity Control Parameters
param set-default RO_SPEED_LIM 3
param set-default RO_SPEED_I 0.1
param set-default RO_SPEED_P 1
param set-default RO_SPEED_RED 1

# Pure Pursuit parameters
param set-default PP_LOOKAHD_GAIN 1
param set-default PP_LOOKAHD_MAX 10
param set-default PP_LOOKAHD_MIN 1

# Wheels
param set-default SIM_GZ_WH_FUNC1 101
param set-default SIM_GZ_WH_MIN1 0
param set-default SIM_GZ_WH_MAX1 200
param set-default SIM_GZ_WH_DIS1 100

# Steering
param set-default SIM_GZ_SV_MAXA1 30
param set-default SIM_GZ_SV_MINA1 -30
param set-default CA_SV_CS_COUNT 1
param set-default SIM_GZ_SV_FUNC1 201
param set-default SIM_GZ_SV_REV 1
