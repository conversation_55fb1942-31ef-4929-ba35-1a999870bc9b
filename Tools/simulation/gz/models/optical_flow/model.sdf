<?xml version="1.0" encoding="UTF-8"?>
<sdf version='1.9'>
  <model name='optical_flow'>
    <pose>0 0 0 0 0 0</pose>
    <self_collide>false</self_collide>
    <static>false</static>
    <link name="flow_link">
      <inertial>
        <pose>0 0 -0.005 0 0 0</pose>
        <mass>0.050</mass>
        <inertia>
          <ixx>0.00004</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.00004</iyy>
          <iyz>0</iyz>
          <izz>0.00004</izz>
        </inertia>
      </inertial>
      <visual name="optical_flow/visual/housing">
        <geometry>
          <box>
            <size>0.034 0.034 0.01</size>
          </box>
        </geometry>
        <material>
          <ambient>0.1 0.1 0.1 1</ambient>
          <diffuse>0.1 0.1 0.1 1</diffuse>
          <specular>0.01 0.01 0.01 1</specular>
        </material>
      </visual>
      <visual name="optical_flow/visual/lens">
        <pose>0 0 -0.005 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.006</radius>
            <length>0.005</length>
          </cylinder>
        </geometry>
      </visual>
      <visual name="optical_flow/visual/lens_glass">
        <pose>0 0 -0.0075 0 0 0</pose>
        <geometry>
          <sphere>
            <radius>0.0059</radius>
          </sphere>
        </geometry>
        <material>
          <ambient>.4 .4 .5 .95</ambient>
          <diffuse>.4 .4 .5 .95</diffuse>
          <specular>1 1 1 1</specular>
          <emissive>0 0 0 1</emissive>
        </material>
      </visual>
      <sensor name="flow_camera" type="camera">
        <pose>0 0 0 0 1.5707 0</pose>
        <camera>
          <horizontal_fov>0.733038</horizontal_fov>
          <image>
            <width>100</width>
            <height>100</height>
          </image>
          <clip>
            <near>0.1</near>
            <far>30</far>
          </clip>
        </camera>
        <always_on>1</always_on>
        <update_rate>50</update_rate>
        <visualize>true</visualize>
      </sensor>
      <sensor name="optical_flow" type="custom" gz:type="optical_flow">
        <always_on>1</always_on>
        <update_rate>50</update_rate>
        <visualize>true</visualize>
      </sensor>
    </link>
  </model>
</sdf>
