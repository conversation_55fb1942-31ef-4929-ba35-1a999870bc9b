{"nodes": [{"id": "m_internal_combustion_engine_control", "name": "internal_combustion_engine_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_autotune_attitude_control", "name": "fw_autotune_attitude_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_autotune_attitude_control", "name": "mc_autotune_attitude_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_landing_target_estimator", "name": "landing_target_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_local_position_estimator", "name": "local_position_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_temperature_compensation", "name": "temperature_compensation", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lightware_laser_serial", "name": "lightware_laser_serial", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_system_power_simulator", "name": "system_power_simulator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lightware_sf45_serial", "name": "lightware_sf45_serial", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pm_selector_auterion", "name": "pm_selector_auterion", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_attitude_estimator_q", "name": "attitude_estimator_q", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lightware_laser_i2c", "name": "lightware_laser_i2c", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_airspeed_sim", "name": "sensor_airspeed_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_flight_mode_manager", "name": "flight_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_airship_att_control", "name": "airship_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_lat_lon_control", "name": "fw_lat_lon_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_differential", "name": "rover_differential", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mag_bias_estimator", "name": "mag_bias_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_io_bypass_control", "name": "io_bypass_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_is31fl3195", "name": "rgbled_is31fl3195", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_payload_deliverer", "name": "payload_deliverer", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_battery_simulator", "name": "battery_simulator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_simulator_mavlink", "name": "simulator_mavlink", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_pos_control", "name": "rover_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_control_allocator", "name": "control_allocator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_airspeed_selector", "name": "airspeed_selector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_cdcacm_autostart", "name": "cdcacm_autostart", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uxrce_dds_client", "name": "uxrce_dds_client", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gyro_calibration", "name": "gyro_calibration", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vtol_att_control", "name": "vtol_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pca9685_pwm_out", "name": "pca9685_pwm_out", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_frsky_telemetry", "name": "frsky_telemetry", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_ncp5623c", "name": "rgbled_ncp5623c", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_template_module", "name": "template_module", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_<PERSON><PERSON>mann", "name": "r<PERSON>_<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_rate_control", "name": "fw_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_feedback", "name": "camera_feedback", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_baro_sim", "name": "sensor_baro_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uuv_pos_control", "name": "uuv_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_rate_control", "name": "mc_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_mode_manager", "name": "fw_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uuv_att_control", "name": "uuv_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_hott_telemetry", "name": "hott_telemetry", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_capture", "name": "camera_capture", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_trigger", "name": "camera_trigger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ulanding_radar", "name": "ulanding_radar", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_att_control", "name": "fw_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_att_control", "name": "mc_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_agp_sim", "name": "sensor_agp_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_gps_sim", "name": "sensor_gps_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_mag_sim", "name": "sensor_mag_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_manual_control", "name": "manual_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_battery_status", "name": "battery_status", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_pos_control", "name": "mc_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_actuator_test", "name": "actuator_test", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_linux_pwm_out", "name": "linux_pwm_out", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rpm_simulator", "name": "rpm_simulator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_safety_button", "name": "safety_button", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_lp5562", "name": "rgbled_lp5562", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_land_detector", "name": "land_detector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_mecanum", "name": "rover_mecanum", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_simulator_sih", "name": "simulator_sih", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_i2c_launcher", "name": "i2c_launcher", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tune_control", "name": "tune_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ets_airspeed", "name": "ets_airspeed", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sagetech_mxs", "name": "sagetech_mxs", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_led_control", "name": "led_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mpu9250_i2c", "name": "mpu9250_i2c", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pps_capture", "name": "pps_capture", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lsm9ds1_mag", "name": "lsm9ds1_mag", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rpm_capture", "name": "rpm_capture", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_out_sim", "name": "pwm_out_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_esc_battery", "name": "esc_battery", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_microbench", "name": "microbench", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_septentrio", "name": "septentrio", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_iridiumsbd", "name": "iridiumsbd", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_iam20680hp", "name": "iam20680hp", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmi088_i2c", "name": "bmi088_i2c", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tone_alarm", "name": "tone_alarm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_batt_smbus", "name": "batt_smbus", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_teraranger", "name": "terar<PERSON>", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ll40ls_pwm", "name": "ll40ls_pwm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_leddar_one", "name": "leddar_one", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uavcannode", "name": "uavcannode", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_pwm", "name": "rgbled_pwm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_send_event", "name": "send_event", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_input", "name": "pwm_input", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rpi_rc_in", "name": "rpi_rc_in", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uwb_sr150", "name": "uwb_sr150", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms5525dso", "name": "ms5525dso", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tattu_can", "name": "tattu_can", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20608g", "name": "icm20608g", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm42670p", "name": "icm42670p", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm40609d", "name": "icm40609d", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm42688p", "name": "icm42688p", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_adis16507", "name": "adis16507", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_adis16477", "name": "adis16477", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_adis16448", "name": "adis16448", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_adis16470", "name": "adis16470", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_adis16497", "name": "adis16497", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mmc5983ma", "name": "mmc5983ma", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lsm303agr", "name": "lsm303agr", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_board_adc", "name": "board_adc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vertiq_io", "name": "vertiq_io", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_thoneflow", "name": "thoneflow", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vectornav", "name": "vectornav", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tcbp001ta", "name": "tcbp001ta", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mpl3115a2", "name": "mpl3115a2", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gz_bridge", "name": "gz_bridge", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_navigator", "name": "navigator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_commander", "name": "commander", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rc_update", "name": "rc_update", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rc_input", "name": "rc_input", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_roboclaw", "name": "rob<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms4525do", "name": "ms4525do", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20649", "name": "icm20649", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20948", "name": "icm20948", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm45686", "name": "icm45686", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_iim42652", "name": "iim42652", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20602", "name": "icm20602", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm42605", "name": "icm42605", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20689", "name": "icm20689", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_iim42653", "name": "iim42653", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vcm1193l", "name": "vcm1193l", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_qmc5883l", "name": "qmc5883l", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_voxl_esc", "name": "voxl_esc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mappydot", "name": "mappydot", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icp101xx", "name": "icp101xx", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icp201xx", "name": "icp201xx", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_voxl2_io", "name": "voxl2_io", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_neopixel", "name": "neopixel", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gyro_fft", "name": "gyro_fft", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_load_mon", "name": "load_mon", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_failure", "name": "failure", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tap_esc", "name": "tap_esc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_asp5033", "name": "asp5033", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mpu6500", "name": "mpu6500", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mpu9250", "name": "mpu9250", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mpu6000", "name": "mpu6000", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lsm303d", "name": "lsm303d", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lsm9ds1", "name": "lsm9ds1", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pcf8583", "name": "pcf8583", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_msp_osd", "name": "msp_osd", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ak09916", "name": "ak09916", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_iis2mdc", "name": "iis2mdc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ist8308", "name": "ist8308", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ist8310", "name": "ist8310", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_hmc5883", "name": "hmc5883", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lis3mdl", "name": "lis3mdl", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ads1115", "name": "ads1115", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gy_us42", "name": "gy_us42", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_cm8jl65", "name": "cm8jl65", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vl53l0x", "name": "vl53l0x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_afbrs50", "name": "afbrs50", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vl53l1x", "name": "vl53l1x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tf02pro", "name": "tf02pro", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_out", "name": "pwm_out", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_paw3902", "name": "paw3902", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_paa3905", "name": "paa3905", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_px4flow", "name": "px4flow", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pmw3901", "name": "pmw3901", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mpc2520", "name": "mpc2520", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lps22hb", "name": "lps22hb", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lps33hw", "name": "lps33hw", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_crsf_rc", "name": "crsf_rc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ghst_rc", "name": "ghst_rc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sbus_rc", "name": "sbus_rc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensors", "name": "sensors", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dataman", "name": "dataman", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mavlink", "name": "mavlink", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_batmon", "name": "batmon", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms4515", "name": "ms4515", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_l3gd20", "name": "l3gd20", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmi085", "name": "bmi085", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmi055", "name": "bmi055", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmi270", "name": "bmi270", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmi088", "name": "bmi088", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sch16t", "name": "sch16t", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_cyphal", "name": "cyphal", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ina238", "name": "ina238", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_voxlpm", "name": "voxlpm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ina220", "name": "ina220", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ina226", "name": "ina226", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ina228", "name": "ina228", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_irlock", "name": "irlock", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_atxxxx", "name": "atxxxx", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ak8963", "name": "ak8963", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmm150", "name": "bmm150", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmm350", "name": "bmm350", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rm3100", "name": "rm3100", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uavcan", "name": "uavcan", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pga460", "name": "pga460", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tfmini", "name": "tfmini", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ll40ls", "name": "ll40ls", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mb12xx", "name": "mb12xx", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmp581", "name": "bmp581", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms5611", "name": "ms5611", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmp280", "name": "bmp280", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dps310", "name": "dps310", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmp388", "name": "bmp388", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lps25h", "name": "lps25h", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms5837", "name": "ms5837", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_heater", "name": "heater", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dsm_rc", "name": "dsm_rc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled", "name": "rgbled", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gimbal", "name": "gimbal", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_logger", "name": "logger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tests", "name": "tests", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dshot", "name": "dshot", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sht3x", "name": "sht3x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sdp3x", "name": "sdp3x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_px4io", "name": "px4io", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_srf02", "name": "srf02", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_srf05", "name": "srf05", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_spl06", "name": "spl06", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_spa06", "name": "spa06", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_auav", "name": "auav", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ekf2", "name": "ekf2", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bst", "name": "bst", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gps", "name": "gps", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "t_vehicle_angular_velocity_groundtruth", "name": "vehicle_angular_velocity_groundtruth", "type": "topic", "color": "#7bd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_global_position_groundtruth", "name": "vehicle_global_position_groundtruth", "type": "topic", "color": "#418ed8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_distance_sensor_mode_change_request", "name": "distance_sensor_mode_change_request", "type": "topic", "color": "#d84174", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_longitudinal_control_configuration", "name": "longitudinal_control_configuration", "type": "topic", "color": "#d8a141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position_groundtruth", "name": "vehicle_local_position_groundtruth", "type": "topic", "color": "#6ad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_controller_landing_status", "name": "position_controller_landing_status", "type": "topic", "color": "#41d84a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_internal_combustion_engine_control", "name": "internal_combustion_engine_control", "type": "topic", "color": "#d84196", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_internal_combustion_engine_status", "name": "internal_combustion_engine_status", "type": "topic", "color": "#41d89a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_set_manual_control", "name": "gimbal_manager_set_manual_control", "type": "topic", "color": "#6541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_autotune_attitude_control_status", "name": "autotune_attitude_control_status", "type": "topic", "color": "#87d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_longitudinal_setpoint", "name": "fixed_wing_longitudinal_setpoint", "type": "topic", "color": "#d8415d", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position_setpoint", "name": "vehicle_local_position_setpoint", "type": "topic", "color": "#41a5d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_attitude_status", "name": "gimbal_device_attitude_status", "type": "topic", "color": "#41cdd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_lateral_control_configuration", "name": "lateral_control_configuration", "type": "topic", "color": "#d841c3", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude_groundtruth", "name": "vehicle_attitude_groundtruth", "type": "topic", "color": "#4ed841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fw_virtual_attitude_setpoint", "name": "fw_virtual_attitude_setpoint", "type": "topic", "color": "#41d883", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mc_virtual_attitude_setpoint", "name": "mc_virtual_attitude_setpoint", "type": "topic", "color": "#41c1d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_register_ext_component_reply", "name": "register_ext_component_reply", "type": "topic", "color": "#6a41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_lateral_setpoint", "name": "fixed_wing_lateral_setpoint", "type": "topic", "color": "#d8ad41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_set_attitude", "name": "gimbal_manager_set_attitude", "type": "topic", "color": "#cbd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_set_attitude", "name": "gimbal_device_set_attitude", "type": "topic", "color": "#d8be41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_controller_status", "name": "position_controller_status", "type": "topic", "color": "#41d8c1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_information", "name": "gimbal_manager_information", "type": "topic", "color": "#a941d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_controls_status_0", "name": "actuator_controls_status_0", "type": "topic", "color": "#ba41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorControlsStatus.msg"}, {"id": "t_fixed_wing_runway_control", "name": "fixed_wing_runway_control", "type": "topic", "color": "#d85241", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_operator_id", "name": "open_drone_id_operator_id", "type": "topic", "color": "#d85741", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_selector_status", "name": "estimator_selector_status", "type": "topic", "color": "#d88541", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude_setpoint", "name": "vehicle_attitude_setpoint", "type": "topic", "color": "#41d88e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_information", "name": "gimbal_device_information", "type": "topic", "color": "#5f41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_setpoint_triplet", "name": "position_setpoint_triplet", "type": "topic", "color": "#8741d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_control_allocator_status", "name": "control_allocator_status", "type": "topic", "color": "#c5d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tiltrotor_extra_controls", "name": "tiltrotor_extra_controls", "type": "topic", "color": "#c0d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_arm_status", "name": "open_drone_id_arm_status", "type": "topic", "color": "#419ad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_uavcan_parameter_request", "name": "uavcan_parameter_request", "type": "topic", "color": "#d8419c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_throttle_setpoint", "name": "rover_throttle_setpoint", "type": "topic", "color": "#d86341", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_obstacle_distance_fused", "name": "obstacle_distance_fused", "type": "topic", "color": "#d89041", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ObstacleDistance.msg"}, {"id": "t_vehicle_global_position", "name": "vehicle_global_position", "type": "topic", "color": "#d89641", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_position_setpoint", "name": "rover_position_setpoint", "type": "topic", "color": "#d8a741", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_failure_detector_status", "name": "failure_detector_status", "type": "topic", "color": "#d8b241", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_flight_phase_estimation", "name": "flight_phase_estimation", "type": "topic", "color": "#afd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_launch_detection_status", "name": "launch_detection_status", "type": "topic", "color": "#9dd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_thrust_setpoint", "name": "vehicle_thrust_setpoint", "type": "topic", "color": "#5fd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/VehicleThrustSetpoint.msg"}, {"id": "t_vehicle_torque_setpoint", "name": "vehicle_torque_setpoint", "type": "topic", "color": "#41d861", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/VehicleTorqueSetpoint.msg"}, {"id": "t_manual_control_setpoint", "name": "manual_control_setpoint", "type": "topic", "color": "#41abd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_switches", "name": "manual_control_switches", "type": "topic", "color": "#5341d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_visual_odometry", "name": "vehicle_visual_odometry", "type": "topic", "color": "#8141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_velocity_setpoint", "name": "rover_velocity_setpoint", "type": "topic", "color": "#c541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_attitude_setpoint", "name": "rover_attitude_setpoint", "type": "topic", "color": "#d841ad", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_steering_setpoint", "name": "rover_steering_setpoint", "type": "topic", "color": "#d841a1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_mocap_odometry", "name": "vehicle_mocap_odometry", "type": "topic", "color": "#d89c41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_rates_setpoint", "name": "vehicle_rates_setpoint", "type": "topic", "color": "#41d844", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_uavcan_parameter_value", "name": "uavcan_parameter_value", "type": "topic", "color": "#4144d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status_flags", "name": "estimator_status_flags", "type": "topic", "color": "#d841a7", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position", "name": "vehicle_local_position", "type": "topic", "color": "#d84146", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_offboard_control_mode", "name": "offboard_control_mode", "type": "topic", "color": "#d8c941", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_self_id", "name": "open_drone_id_self_id", "type": "topic", "color": "#d8d441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_status", "name": "gimbal_manager_status", "type": "topic", "color": "#41b6d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_sensor_bias", "name": "estimator_sensor_bias", "type": "topic", "color": "#4841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_differential_pressure", "name": "differential_pressure", "type": "topic", "color": "#4e41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_land_detected", "name": "vehicle_land_detected", "type": "topic", "color": "#c041d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_control_mode", "name": "vehicle_control_mode", "type": "topic", "color": "#d1d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_system", "name": "open_drone_id_system", "type": "topic", "color": "#9d41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_servos_trim", "name": "actuator_servos_trim", "type": "topic", "color": "#d841cf", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_outputs_sim", "name": "actuator_outputs_sim", "type": "topic", "color": "#d8417f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorOutputs.msg"}, {"id": "t_sensor_optical_flow", "name": "sensor_optical_flow", "type": "topic", "color": "#d86e41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_rate_setpoint", "name": "rover_rate_setpoint", "type": "topic", "color": "#a9d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_figure_eight_status", "name": "figure_eight_status", "type": "topic", "color": "#81d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_trajectory_setpoint", "name": "trajectory_setpoint", "type": "topic", "color": "#65d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_aux_global_position", "name": "aux_global_position", "type": "topic", "color": "#41d872", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/EstimatorAidSource2d.msg"}, {"id": "t_vehicle_command_ack", "name": "vehicle_command_ack", "type": "topic", "color": "#41d3d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vtol_vehicle_status", "name": "vtol_vehicle_status", "type": "topic", "color": "#41b0d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_target_pose", "name": "landing_target_pose", "type": "topic", "color": "#4166d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_esc_serial_passthru", "name": "esc_serial_passthru", "type": "topic", "color": "#7041d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/MavlinkTunnel.msg"}, {"id": "t_vehicle_constraints", "name": "vehicle_constraints", "type": "topic", "color": "#d84157", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu_status", "name": "vehicle_imu_status", "type": "topic", "color": "#41d866", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_power_button_state", "name": "power_button_state", "type": "topic", "color": "#41d894", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed_validated", "name": "airspeed_validated", "type": "topic", "color": "#4172d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_transponder_report", "name": "transponder_report", "type": "topic", "color": "#5941d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensors_status_imu", "name": "sensors_status_imu", "type": "topic", "color": "#d8416e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear_wheel", "name": "landing_gear_wheel", "type": "topic", "color": "#d84168", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_obstacle_distance", "name": "obstacle_distance", "type": "topic", "color": "#d87941", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ObstacleDistance.msg"}, {"id": "t_sensor_correction", "name": "sensor_correction", "type": "topic", "color": "#d6d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mount_orientation", "name": "mount_orientation", "type": "topic", "color": "#59d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_spoilers_setpoint", "name": "spoilers_setpoint", "type": "topic", "color": "#41d878", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/NormalizedUnsignedSetpoint.msg"}, {"id": "t_sensor_hygrometer", "name": "sensor_hygrometer", "type": "topic", "color": "#41d8c7", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_iridiumsbd_status", "name": "iridiumsbd_status", "type": "topic", "color": "#414ad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_time_estimate", "name": "rtl_time_estimate", "type": "topic", "color": "#8c41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_v1_command", "name": "gimbal_v1_command", "type": "topic", "color": "#d8418a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_telemetry_status", "name": "telemetry_status", "type": "topic", "color": "#d84c41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rc_parameter_map", "name": "rc_parameter_map", "type": "topic", "color": "#d8cf41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_response", "name": "dataman_response", "type": "topic", "color": "#92d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_navigator_status", "name": "navigator_status", "type": "topic", "color": "#42d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_odometry", "name": "vehicle_odometry", "type": "topic", "color": "#41d87d", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status", "name": "estimator_status", "type": "topic", "color": "#41c7d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_selection", "name": "sensor_selection", "type": "topic", "color": "#4189d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude", "name": "vehicle_attitude", "type": "topic", "color": "#415bd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_outputs", "name": "actuator_outputs", "type": "topic", "color": "#9241d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorOutputs.msg"}, {"id": "t_sensor_gyro_fifo", "name": "sensor_gyro_fifo", "type": "topic", "color": "#d841c9", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_combined", "name": "sensor_combined", "type": "topic", "color": "#d87f41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_status", "name": "geofence_status", "type": "topic", "color": "#bad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_servos", "name": "actuator_servos", "type": "topic", "color": "#98d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream_ack", "name": "ulog_stream_ack", "type": "topic", "color": "#41d855", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gps_inject_data", "name": "gps_inject_data", "type": "topic", "color": "#41d86c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command", "name": "vehicle_command", "type": "topic", "color": "#41d89f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_controls", "name": "gimbal_controls", "type": "topic", "color": "#41d8cd", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_request", "name": "dataman_request", "type": "topic", "color": "#41d8d3", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_key_value", "name": "debug_key_value", "type": "topic", "color": "#4155d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_motors", "name": "actuator_motors", "type": "topic", "color": "#4241d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_distance_sensor", "name": "distance_sensor", "type": "topic", "color": "#d84163", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_result", "name": "geofence_result", "type": "topic", "color": "#d84152", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_trigger", "name": "camera_trigger", "type": "topic", "color": "#d84141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_takeoff_status", "name": "takeoff_status", "type": "topic", "color": "#d85d41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_flaps_setpoint", "name": "flaps_setpoint", "type": "topic", "color": "#53d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/NormalizedUnsignedSetpoint.msg"}, {"id": "t_failsafe_flags", "name": "failsafe_flags", "type": "topic", "color": "#41d889", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_satellite_info", "name": "satellite_info", "type": "topic", "color": "#41d8ab", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_status", "name": "vehicle_status", "type": "topic", "color": "#4178d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mission_result", "name": "mission_result", "type": "topic", "color": "#416cd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_armed", "name": "actuator_armed", "type": "topic", "color": "#a341d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_action_request", "name": "action_request", "type": "topic", "color": "#d841d4", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_capture", "name": "camera_capture", "type": "topic", "color": "#d841be", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_battery_status", "name": "battery_status", "type": "topic", "color": "#d84185", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_irlock_report", "name": "irlock_report", "type": "topic", "color": "#d84641", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_test", "name": "actuator_test", "type": "topic", "color": "#41d85b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_logger_status", "name": "logger_status", "type": "topic", "color": "#41d8b6", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_safety_button", "name": "safety_button", "type": "topic", "color": "#41bcd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_home_position", "name": "home_position", "type": "topic", "color": "#417dd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_health_report", "name": "health_report", "type": "topic", "color": "#4161d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_status", "name": "camera_status", "type": "topic", "color": "#af41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_orbit_status", "name": "orbit_status", "type": "topic", "color": "#8cd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_px4io_status", "name": "px4io_status", "type": "topic", "color": "#419fd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_accel", "name": "sensor_accel", "type": "topic", "color": "#cb41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tune_control", "name": "tune_control", "type": "topic", "color": "#d641d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_system_power", "name": "system_power", "type": "topic", "color": "#d841b2", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear", "name": "landing_gear", "type": "topic", "color": "#d84190", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gyro", "name": "sensor_gyro", "type": "topic", "color": "#d86841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_led_control", "name": "led_control", "type": "topic", "color": "#d8b841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_value", "name": "debug_value", "type": "topic", "color": "#d8c341", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_array", "name": "debug_array", "type": "topic", "color": "#76d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_pps_capture", "name": "pps_capture", "type": "topic", "color": "#41d8bc", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_roi", "name": "vehicle_roi", "type": "topic", "color": "#4194d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tecs_status", "name": "tecs_status", "type": "topic", "color": "#7641d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu", "name": "vehicle_imu", "type": "topic", "color": "#9841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_baro", "name": "sensor_baro", "type": "topic", "color": "#b441d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream", "name": "ulog_stream", "type": "topic", "color": "#d841b8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_mag", "name": "sensor_mag", "type": "topic", "color": "#d87441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_esc_status", "name": "esc_status", "type": "topic", "color": "#d88a41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_vect", "name": "debug_vect", "type": "topic", "color": "#a3d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_adc_report", "name": "adc_report", "type": "topic", "color": "#70d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_uwb", "name": "sensor_uwb", "type": "topic", "color": "#d141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gps", "name": "sensor_gps", "type": "topic", "color": "#d84179", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/SensorGps.msg"}, {"id": "t_rtl_status", "name": "rtl_status", "type": "topic", "color": "#d8414c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_pwm_input", "name": "pwm_input", "type": "topic", "color": "#7b41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_input_rc", "name": "input_rc", "type": "topic", "color": "#b4d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed", "name": "airspeed", "type": "topic", "color": "#41d8d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/EstimatorAidSource1d.msg"}, {"id": "t_mission", "name": "mission", "type": "topic", "color": "#48d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gripper", "name": "gripper", "type": "topic", "color": "#41d850", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_cpuload", "name": "cpuload", "type": "topic", "color": "#4150d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_event", "name": "event", "type": "topic", "color": "#41d8b0", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_wind", "name": "wind", "type": "topic", "color": "#41d8a5", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/Wind.msg"}, {"id": "t_rpm", "name": "rpm", "type": "topic", "color": "#4183d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}], "links": [{"source": "m_actuator_test", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_led_control", "target": "t_led_control", "color": "#d8b841", "style": "dashed"}, {"source": "m_failure", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_tests", "target": "t_dataman_request", "color": "#41d8d3", "style": "dashed"}, {"source": "m_io_bypass_control", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_io_bypass_control", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_tune_control", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_pwm_input", "target": "t_pwm_input", "color": "#7b41d8", "style": "dashed"}, {"source": "m_rc_input", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_rc_input", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_rc_input", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_rpi_rc_in", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_batmon", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_dshot", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_dshot", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_uwb_sr150", "target": "t_sensor_uwb", "color": "#d141d8", "style": "dashed"}, {"source": "m_tap_esc", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_tap_esc", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_tap_esc", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_tap_esc", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_tap_esc", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_tap_esc", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_pca9685_pwm_out", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_septentrio", "target": "t_gps_inject_data", "color": "#41d86c", "style": "dashed"}, {"source": "m_septentrio", "target": "t_sensor_gps", "color": "#d84179", "style": "dashed"}, {"source": "m_septentrio", "target": "t_satellite_info", "color": "#41d8ab", "style": "dashed"}, {"source": "m_sht3x", "target": "t_sensor_hygrometer", "color": "#41d8c7", "style": "dashed"}, {"source": "m_iridiumsbd", "target": "t_iridiumsbd_status", "color": "#414ad8", "style": "dashed"}, {"source": "m_hott_telemetry", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_ms4525do", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_ms5525dso", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_sdp3x", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_asp5033", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_auav", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_auav", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_ms4515", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_ets_airspeed", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_sagetech_mxs", "target": "t_transponder_report", "color": "#5941d8", "style": "dashed"}, {"source": "m_tattu_can", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_px4io", "target": "t_safety_button", "color": "#41bcd8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_led_control", "color": "#d8b841", "style": "dashed"}, {"source": "m_px4io", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_px4io", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_px4io", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_px4io", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_px4io_status", "color": "#419fd8", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_icm20649", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm20649", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm20649", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm20608g", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm20608g", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm20608g", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_mpu6500", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_mpu6500", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_mpu6500", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm42670p", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm42670p", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm42670p", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm45686", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm45686", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm45686", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm40609d", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm40609d", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm40609d", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_iim42652", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_iim42652", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_iim42652", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_mpu9250", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_mpu9250", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_mpu9250", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_mpu9250", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_mpu9250_i2c", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_mpu9250_i2c", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_mpu9250_i2c", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm20602", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm20602", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm20602", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm42688p", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm42688p", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm42688p", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm42605", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm42605", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm42605", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_icm20689", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_icm20689", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_icm20689", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_iam20680hp", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_iam20680hp", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_iam20680hp", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_iim42653", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_iim42653", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_iim42653", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_mpu6000", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_mpu6000", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_mpu6000", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_lsm303d", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_lsm303d", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_l3gd20", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_l3gd20", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_lsm9ds1", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_lsm9ds1", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_lsm9ds1", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_bmi085", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_bmi085", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_bmi085", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_bmi055", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_bmi055", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_bmi055", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_bmi270", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_bmi270", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_bmi270", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_bmi088_i2c", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_bmi088_i2c", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_bmi088_i2c", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_bmi088", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_bmi088", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_bmi088", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_sch16t", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_sch16t", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_sch16t", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_adis16507", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_adis16507", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_adis16507", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_adis16477", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_adis16477", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_adis16477", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_adis16448", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_adis16470", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_adis16470", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_adis16470", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_adis16497", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_adis16497", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_adis16497", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_linux_pwm_out", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_linux_pwm_out", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_linux_pwm_out", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_linux_pwm_out", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_linux_pwm_out", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_gps", "target": "t_satellite_info", "color": "#41d8ab", "style": "dashed"}, {"source": "m_gps", "target": "t_sensor_gps", "color": "#d84179", "style": "dashed"}, {"source": "m_gps", "target": "t_gps_inject_data", "color": "#41d86c", "style": "dashed"}, {"source": "m_rpm_simulator", "target": "t_rpm", "color": "#4183d8", "style": "dashed"}, {"source": "m_pcf8583", "target": "t_rpm", "color": "#4183d8", "style": "dashed"}, {"source": "m_cyphal", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_cyphal", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_safety_button", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_safety_button", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_safety_button", "target": "t_safety_button", "color": "#41bcd8", "style": "dashed"}, {"source": "m_safety_button", "target": "t_led_control", "color": "#d8b841", "style": "dashed"}, {"source": "m_tone_alarm", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_ina238", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_voxlpm", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_ina220", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_ina226", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_ina228", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_camera_capture", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_camera_capture", "target": "t_camera_trigger", "color": "#d84141", "style": "dashed"}, {"source": "m_irlock", "target": "t_irlock_report", "color": "#d84641", "style": "dashed"}, {"source": "m_pps_capture", "target": "t_pps_capture", "color": "#41d8bc", "style": "dashed"}, {"source": "m_ak09916", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_ak8963", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_mmc5983ma", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_lsm303agr", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_iis2mdc", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_vcm1193l", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_ist8308", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_ist8310", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_bmm150", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_bmm350", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_qmc5883l", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_lsm9ds1_mag", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_hmc5883", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_rm3100", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_lis3mdl", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_ads1115", "target": "t_adc_report", "color": "#70d841", "style": "dashed"}, {"source": "m_board_adc", "target": "t_system_power", "color": "#d841b2", "style": "dashed"}, {"source": "m_board_adc", "target": "t_adc_report", "color": "#70d841", "style": "dashed"}, {"source": "m_batt_smbus", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_uavcan", "target": "t_uavcan_parameter_value", "color": "#4144d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_uavcan", "target": "t_safety_button", "color": "#41bcd8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_led_control", "color": "#d8b841", "style": "dashed"}, {"source": "m_uavcan", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_uavcan", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_uavcan", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_open_drone_id_arm_status", "color": "#419ad8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_voxl_esc", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_voxl_esc", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_voxl_esc", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_voxl_esc", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_voxl_esc", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_voxl_esc", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_voxl_esc", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_vertiq_io", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_vertiq_io", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_vertiq_io", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_vertiq_io", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_vertiq_io", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_vertiq_io", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_camera_trigger", "color": "#d84141", "style": "dashed"}, {"source": "m_lightware_laser_serial", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_gy_us42", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_srf02", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_teraranger", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_lightware_laser_i2c", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_pga460", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_cm8jl65", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_vl53l0x", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_afbrs50", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_tfmini", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_ulanding_radar", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_ll40ls", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_ll40ls_pwm", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_vl53l1x", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_mb12xx", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_mappydot", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_leddar_one", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_lightware_sf45_serial", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_lightware_sf45_serial", "target": "t_obstacle_distance", "color": "#d87941", "style": "dashed"}, {"source": "m_lightware_sf45_serial", "target": "t_obstacle_distance_fused", "color": "#d89041", "style": "dashed"}, {"source": "m_lightware_sf45_serial", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_lightware_sf45_serial", "target": "t_vehicle_attitude", "color": "#415bd8", "style": "dashed"}, {"source": "m_srf05", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_tf02pro", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_paw3902", "target": "t_sensor_optical_flow", "color": "#d86e41", "style": "dashed"}, {"source": "m_paa3905", "target": "t_sensor_optical_flow", "color": "#d86e41", "style": "dashed"}, {"source": "m_px4flow", "target": "t_sensor_optical_flow", "color": "#d86e41", "style": "dashed"}, {"source": "m_pmw3901", "target": "t_sensor_optical_flow", "color": "#d86e41", "style": "dashed"}, {"source": "m_thoneflow", "target": "t_sensor_optical_flow", "color": "#d86e41", "style": "dashed"}, {"source": "m_vectornav", "target": "t_sensor_selection", "color": "#4189d8", "style": "dashed"}, {"source": "m_vectornav", "target": "t_estimator_status", "color": "#41c7d8", "style": "dashed"}, {"source": "m_vectornav", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_vectornav", "target": "t_sensor_gps", "color": "#d84179", "style": "dashed"}, {"source": "m_bmp581", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_ms5611", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_icp101xx", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_icp201xx", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_mpc2520", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_bmp280", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_dps310", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_tcbp001ta", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_spl06", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_spa06", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_lps22hb", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_lps33hw", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_bmp388", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_mpl3115a2", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_lps25h", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_ms5837", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_rpm_capture", "target": "t_rpm", "color": "#4183d8", "style": "dashed"}, {"source": "m_rpm_capture", "target": "t_pwm_input", "color": "#7b41d8", "style": "dashed"}, {"source": "m_voxl2_io", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_voxl2_io", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_voxl2_io", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_voxl2_io", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_voxl2_io", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_voxl2_io", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_uavcannode", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_uavcannode", "target": "t_led_control", "color": "#d8b841", "style": "dashed"}, {"source": "m_uavcannode", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_uavcannode", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_uavcannode", "target": "t_gps_inject_data", "color": "#41d86c", "style": "dashed"}, {"source": "m_uavcannode", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_crsf_rc", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_ghst_rc", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_dsm_rc", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_dsm_rc", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_dsm_rc", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_sbus_rc", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_steering_setpoint", "color": "#d841a1", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_velocity_setpoint", "color": "#c541d8", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_throttle_setpoint", "color": "#d86341", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_position_controller_status", "color": "#41d8c1", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_rate_setpoint", "color": "#a9d841", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_position_setpoint", "color": "#d8a741", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_attitude_setpoint", "color": "#d841ad", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_fw_lat_lon_control", "target": "t_tecs_status", "color": "#7641d8", "style": "dashed"}, {"source": "m_fw_lat_lon_control", "target": "t_flight_phase_estimation", "color": "#afd841", "style": "dashed"}, {"source": "m_ekf2", "target": "t_sensor_selection", "color": "#4189d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_selector_status", "color": "#d88541", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_global_position", "color": "#d89641", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_odometry", "color": "#41d87d", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_attitude", "color": "#415bd8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status_flags", "color": "#d841a7", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_sensor_bias", "color": "#4841d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_wind", "color": "#41d8a5", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status", "color": "#41c7d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_local_position", "color": "#d84146", "style": "dashed"}, {"source": "m_uxrce_dds_client", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_payload_deliverer", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_payload_deliverer", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_payload_deliverer", "target": "t_gripper", "color": "#41d850", "style": "dashed"}, {"source": "m_landing_target_estimator", "target": "t_landing_target_pose", "color": "#4166d8", "style": "dashed"}, {"source": "m_fw_autotune_attitude_control", "target": "t_autotune_attitude_control_status", "color": "#87d841", "style": "dashed"}, {"source": "m_land_detector", "target": "t_vehicle_land_detected", "color": "#c041d8", "style": "dashed"}, {"source": "m_mc_autotune_attitude_control", "target": "t_autotune_attitude_control_status", "color": "#87d841", "style": "dashed"}, {"source": "m_fw_att_control", "target": "t_vehicle_rates_setpoint", "color": "#41d844", "style": "dashed"}, {"source": "m_fw_att_control", "target": "t_landing_gear_wheel", "color": "#d84168", "style": "dashed"}, {"source": "m_mc_att_control", "target": "t_vehicle_rates_setpoint", "color": "#41d844", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_spoilers_setpoint", "color": "#41d878", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#41d844", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_flaps_setpoint", "color": "#53d841", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_steering_setpoint", "color": "#d841a1", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_velocity_setpoint", "color": "#c541d8", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_throttle_setpoint", "color": "#d86341", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_rate_setpoint", "color": "#a9d841", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_position_setpoint", "color": "#d8a741", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_attitude_setpoint", "color": "#d841ad", "style": "dashed"}, {"source": "m_camera_feedback", "target": "t_camera_capture", "color": "#d841be", "style": "dashed"}, {"source": "m_system_power_simulator", "target": "t_system_power", "color": "#d841b2", "style": "dashed"}, {"source": "m_sensor_agp_sim", "target": "t_aux_global_position", "color": "#41d872", "style": "dashed"}, {"source": "m_sensor_baro_sim", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_global_position_groundtruth", "color": "#418ed8", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_angular_velocity_groundtruth", "color": "#7bd841", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_local_position_groundtruth", "color": "#6ad841", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_airspeed", "color": "#41d8d8", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_attitude_groundtruth", "color": "#4ed841", "style": "dashed"}, {"source": "m_battery_simulator", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_battery_simulator", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_outputs_sim", "color": "#d8417f", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_sensor_airspeed_sim", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_outputs", "color": "#9241d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_angular_velocity_groundtruth", "color": "#7bd841", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_local_position_groundtruth", "color": "#6ad841", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_attitude_groundtruth", "color": "#4ed841", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_global_position_groundtruth", "color": "#418ed8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_optical_flow", "color": "#d86e41", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_obstacle_distance", "color": "#d87941", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_gps", "color": "#d84179", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_gimbal_device_information", "color": "#5f41d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_gimbal_device_attitude_status", "color": "#41cdd8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_visual_odometry", "color": "#8141d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_angular_velocity_groundtruth", "color": "#7bd841", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_irlock_report", "color": "#d84641", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_local_position_groundtruth", "color": "#6ad841", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_attitude_groundtruth", "color": "#4ed841", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_optical_flow", "color": "#d86e41", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_global_position_groundtruth", "color": "#418ed8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_rpm", "color": "#4183d8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_esc_status", "color": "#d88a41", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_mocap_odometry", "color": "#d89c41", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_visual_odometry", "color": "#8141d8", "style": "dashed"}, {"source": "m_sensor_gps_sim", "target": "t_sensor_gps", "color": "#d84179", "style": "dashed"}, {"source": "m_sensor_mag_sim", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_navigator", "target": "t_position_setpoint_triplet", "color": "#8741d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_time_estimate", "color": "#8c41d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_roi", "color": "#4194d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission", "color": "#48d841", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_land_detected", "color": "#c041d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_navigator_status", "color": "#42d841", "style": "dashed"}, {"source": "m_navigator", "target": "t_home_position", "color": "#417dd8", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission_result", "color": "#416cd8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_status", "color": "#4178d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_global_position", "color": "#d89641", "style": "dashed"}, {"source": "m_navigator", "target": "t_distance_sensor_mode_change_request", "color": "#d84174", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_navigator", "target": "t_transponder_report", "color": "#5941d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_status", "color": "#bad841", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_result", "color": "#d84152", "style": "dashed"}, {"source": "m_navigator", "target": "t_dataman_request", "color": "#41d8d3", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_status", "color": "#d8414c", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_send_event", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_send_event", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_send_event", "target": "t_led_control", "color": "#d8b841", "style": "dashed"}, {"source": "m_commander", "target": "t_failsafe_flags", "color": "#41d889", "style": "dashed"}, {"source": "m_commander", "target": "t_event", "color": "#41d8b0", "style": "dashed"}, {"source": "m_commander", "target": "t_led_control", "color": "#d8b841", "style": "dashed"}, {"source": "m_commander", "target": "t_power_button_state", "color": "#41d894", "style": "dashed"}, {"source": "m_commander", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_commander", "target": "t_home_position", "color": "#417dd8", "style": "dashed"}, {"source": "m_commander", "target": "t_register_ext_component_reply", "color": "#6a41d8", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_status", "color": "#4178d8", "style": "dashed"}, {"source": "m_commander", "target": "t_health_report", "color": "#4161d8", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_test", "color": "#41d85b", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_armed", "color": "#a341d8", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_control_mode", "color": "#d1d841", "style": "dashed"}, {"source": "m_commander", "target": "t_failure_detector_status", "color": "#d8b241", "style": "dashed"}, {"source": "m_esc_battery", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_trajectory_setpoint", "color": "#65d841", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_landing_gear", "color": "#d84190", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_vehicle_constraints", "color": "#d84157", "style": "dashed"}, {"source": "m_manual_control", "target": "t_landing_gear", "color": "#d84190", "style": "dashed"}, {"source": "m_manual_control", "target": "t_action_request", "color": "#d841d4", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_status", "color": "#4178d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_switches", "color": "#5341d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_setpoint", "color": "#41abd8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_uuv_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#41d88e", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#41d88e", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_thrust_setpoint", "color": "#5fd841", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_torque_setpoint", "color": "#41d861", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_position_controller_status", "color": "#41d8c1", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_actuator_controls_status_0", "color": "#ba41d8", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#41d844", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_control_allocator_status", "color": "#c5d841", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_servos_trim", "color": "#d841cf", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_servos", "color": "#98d841", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_selection", "color": "#4189d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_combined", "color": "#d87f41", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu", "color": "#9841d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu_status", "color": "#41d866", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensors_status_imu", "color": "#d8416e", "style": "dashed"}, {"source": "m_sensors", "target": "t_airspeed", "color": "#41d8d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_orbit_status", "color": "#8cd841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_longitudinal_setpoint", "color": "#d8415d", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_landing_gear", "color": "#d84190", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_figure_eight_status", "color": "#81d841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_position_controller_landing_status", "color": "#41d84a", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_vehicle_local_position_setpoint", "color": "#41a5d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_lateral_control_configuration", "color": "#d841c3", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_longitudinal_control_configuration", "color": "#d8a141", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_lateral_setpoint", "color": "#d8ad41", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_launch_detection_status", "color": "#9dd841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_spoilers_setpoint", "color": "#41d878", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_runway_control", "color": "#d85241", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_flaps_setpoint", "color": "#53d841", "style": "dashed"}, {"source": "m_airship_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#5fd841", "style": "dashed"}, {"source": "m_airship_att_control", "target": "t_vehicle_torque_setpoint", "color": "#41d861", "style": "dashed"}, {"source": "m_uuv_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#5fd841", "style": "dashed"}, {"source": "m_uuv_att_control", "target": "t_vehicle_torque_setpoint", "color": "#41d861", "style": "dashed"}, {"source": "m_dataman", "target": "t_dataman_response", "color": "#92d841", "style": "dashed"}, {"source": "m_internal_combustion_engine_control", "target": "t_internal_combustion_engine_status", "color": "#41d89a", "style": "dashed"}, {"source": "m_internal_combustion_engine_control", "target": "t_internal_combustion_engine_control", "color": "#d84196", "style": "dashed"}, {"source": "m_load_mon", "target": "t_cpuload", "color": "#4150d8", "style": "dashed"}, {"source": "m_local_position_estimator", "target": "t_vehicle_global_position", "color": "#d89641", "style": "dashed"}, {"source": "m_local_position_estimator", "target": "t_estimator_status", "color": "#41c7d8", "style": "dashed"}, {"source": "m_local_position_estimator", "target": "t_vehicle_odometry", "color": "#41d87d", "style": "dashed"}, {"source": "m_local_position_estimator", "target": "t_vehicle_local_position", "color": "#d84146", "style": "dashed"}, {"source": "m_mavlink", "target": "t_irlock_report", "color": "#d84641", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_array", "color": "#76d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_system", "color": "#9d41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_camera_status", "color": "#af41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_trajectory_setpoint", "color": "#65d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_baro", "color": "#b441d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_telemetry_status", "color": "#d84c41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_operator_id", "color": "#d85741", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mission", "color": "#48d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_rates_setpoint", "color": "#41d844", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_accel", "color": "#cb41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gyro", "color": "#d86841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_optical_flow", "color": "#d86e41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_mag", "color": "#d87441", "style": "dashed"}, {"source": "m_mavlink", "target": "t_obstacle_distance", "color": "#d87941", "style": "dashed"}, {"source": "m_mavlink", "target": "t_tune_control", "color": "#d641d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gyro_fifo", "color": "#d841c9", "style": "dashed"}, {"source": "m_mavlink", "target": "t_ulog_stream_ack", "color": "#41d855", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_global_position", "color": "#d89641", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_mocap_odometry", "color": "#d89c41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_landing_target_pose", "color": "#4166d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude", "color": "#415bd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_fw_virtual_attitude_setpoint", "color": "#41d883", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_key_value", "color": "#4155d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gps_inject_data", "color": "#41d86c", "style": "dashed"}, {"source": "m_mavlink", "target": "t_uavcan_parameter_request", "color": "#d8419c", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude_setpoint", "color": "#41d88e", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_value", "color": "#d8c341", "style": "dashed"}, {"source": "m_mavlink", "target": "t_differential_pressure", "color": "#4e41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_mavlink", "target": "t_offboard_control_mode", "color": "#d8c941", "style": "dashed"}, {"source": "m_mavlink", "target": "t_rc_parameter_map", "color": "#d8cf41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_self_id", "color": "#d8d441", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gps", "color": "#d84179", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_mavlink", "target": "t_transponder_report", "color": "#5941d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_manager_set_attitude", "color": "#cbd841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_information", "color": "#5f41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_airspeed", "color": "#41d8d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_distance_sensor", "color": "#d84163", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_manager_set_manual_control", "color": "#6541d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_event", "color": "#41d8b0", "style": "dashed"}, {"source": "m_mavlink", "target": "t_input_rc", "color": "#b4d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_esc_serial_passthru", "color": "#7041d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_dataman_request", "color": "#41d8d3", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_vect", "color": "#a3d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_attitude_status", "color": "#41cdd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mc_virtual_attitude_setpoint", "color": "#41c1d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_visual_odometry", "color": "#8141d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_local_position", "color": "#d84146", "style": "dashed"}, {"source": "m_battery_status", "target": "t_battery_status", "color": "#d84185", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_device_attitude_status", "color": "#41cdd8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_v1_command", "color": "#d8418a", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_device_set_attitude", "color": "#d8be41", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_manager_status", "color": "#41b6d8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_controls", "color": "#41d8cd", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_manager_information", "color": "#a941d8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_gimbal", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_mount_orientation", "color": "#59d841", "style": "dashed"}, {"source": "m_logger", "target": "t_ulog_stream", "color": "#d841b8", "style": "dashed"}, {"source": "m_logger", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_logger", "target": "t_logger_status", "color": "#41d8b6", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_tiltrotor_extra_controls", "color": "#c0d841", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#5fd841", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_attitude_setpoint", "color": "#41d88e", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vtol_vehicle_status", "color": "#41b0d8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_torque_setpoint", "color": "#41d861", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_spoilers_setpoint", "color": "#41d878", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_flaps_setpoint", "color": "#53d841", "style": "dashed"}, {"source": "m_airspeed_selector", "target": "t_airspeed_validated", "color": "#4172d8", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_vehicle_command", "color": "#41d89f", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_sensor_correction", "color": "#d6d841", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_vehicle_command_ack", "color": "#41d3d8", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_led_control", "color": "#d8b841", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_takeoff_status", "color": "#d85d41", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#41d88e", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_constraints", "color": "#d84157", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_local_position_setpoint", "color": "#41a5d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_trajectory_setpoint", "color": "#65d841", "style": "dashed"}, {"source": "m_rc_update", "target": "t_manual_control_switches", "color": "#5341d8", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_steering_setpoint", "color": "#d841a1", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_velocity_setpoint", "color": "#c541d8", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_throttle_setpoint", "color": "#d86341", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_actuator_motors", "color": "#4241d8", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_rate_setpoint", "color": "#a9d841", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_position_setpoint", "color": "#d8a741", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_attitude_setpoint", "color": "#d841ad", "style": "dashed"}, {"source": "m_attitude_estimator_q", "target": "t_vehicle_attitude", "color": "#415bd8", "style": "dashed"}, {"source": "t_vehicle_status", "target": "m_i2c_launcher", "color": "#4178d8", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_microbench", "color": "#41d889", "style": "normal"}, {"source": "t_sensor_gyro_fifo", "target": "m_microbench", "color": "#d841c9", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_microbench", "color": "#d86841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_microbench", "color": "#d84146", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_failure", "color": "#41d3d8", "style": "normal"}, {"source": "t_input_rc", "target": "m_tests", "color": "#b4d841", "style": "normal"}, {"source": "t_dataman_response", "target": "m_tests", "color": "#92d841", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_io_bypass_control", "color": "#9241d8", "style": "normal"}, {"source": "t_adc_report", "target": "m_rc_input", "color": "#70d841", "style": "normal"}, {"source": "t_battery_status", "target": "m_rc_input", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_rc_input", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_rc_input", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rc_input", "color": "#415bd8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_dshot", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_dshot", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_dshot", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_dshot", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_dshot", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_dshot", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_dshot", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_dshot", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_dshot", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_dshot", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_dshot", "color": "#d84168", "style": "normal"}, {"source": "t_sensor_uwb", "target": "m_uwb_sr150", "color": "#d141d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_tap_esc", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_tap_esc", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_tap_esc", "color": "#d84190", "style": "normal"}, {"source": "t_led_control", "target": "m_tap_esc", "color": "#d8b841", "style": "normal"}, {"source": "t_tune_control", "target": "m_tap_esc", "color": "#d641d8", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_tap_esc", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_tap_esc", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_tap_esc", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_tap_esc", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_tap_esc", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_tap_esc", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_tap_esc", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_tap_esc", "color": "#d84168", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pca9685_pwm_out", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_pca9685_pwm_out", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pca9685_pwm_out", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pca9685_pwm_out", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_pca9685_pwm_out", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pca9685_pwm_out", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_pca9685_pwm_out", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pca9685_pwm_out", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pca9685_pwm_out", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pca9685_pwm_out", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_pca9685_pwm_out", "color": "#d84168", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_septentrio", "color": "#41d86c", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_roboclaw", "color": "#a341d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_bst", "color": "#415bd8", "style": "normal"}, {"source": "t_battery_status", "target": "m_bst", "color": "#d84185", "style": "normal"}, {"source": "t_battery_status", "target": "m_frsky_telemetry", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_frsky_telemetry", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_frsky_telemetry", "color": "#d89641", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_frsky_telemetry", "color": "#d84146", "style": "normal"}, {"source": "t_home_position", "target": "m_hott_telemetry", "color": "#417dd8", "style": "normal"}, {"source": "t_battery_status", "target": "m_hott_telemetry", "color": "#d84185", "style": "normal"}, {"source": "t_esc_status", "target": "m_hott_telemetry", "color": "#d88a41", "style": "normal"}, {"source": "t_airspeed", "target": "m_hott_telemetry", "color": "#41d8d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_sagetech_mxs", "color": "#c041d8", "style": "normal"}, {"source": "t_transponder_report", "target": "m_sagetech_mxs", "color": "#5941d8", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_sagetech_mxs", "color": "#d84179", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_sagetech_mxs", "color": "#4178d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_px4io", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_px4io", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_px4io", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_px4io", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_px4io", "color": "#41d850", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_px4io", "color": "#4178d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_px4io", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_px4io", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_px4io", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_px4io", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_px4io", "color": "#41d89f", "style": "normal"}, {"source": "t_px4io_status", "target": "m_px4io", "color": "#419fd8", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_px4io", "color": "#d84168", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_linux_pwm_out", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_linux_pwm_out", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_linux_pwm_out", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_linux_pwm_out", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_linux_pwm_out", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_linux_pwm_out", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_linux_pwm_out", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_linux_pwm_out", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_linux_pwm_out", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_linux_pwm_out", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_linux_pwm_out", "color": "#d84168", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_gps", "color": "#41d86c", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_cdcacm_autostart", "color": "#a341d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_cyphal", "color": "#41d85b", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_cyphal", "color": "#a341d8", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_cyphal", "color": "#d84179", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_safety_button", "color": "#a341d8", "style": "normal"}, {"source": "t_tune_control", "target": "m_tone_alarm", "color": "#d641d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pm_selector_auterion", "color": "#a341d8", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_ina238", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ina238", "color": "#4178d8", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_voxlpm", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_voxlpm", "color": "#4178d8", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_ina220", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ina220", "color": "#4178d8", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_ina226", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ina226", "color": "#4178d8", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_ina228", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ina228", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_camera_capture", "color": "#41d89f", "style": "normal"}, {"source": "t_pps_capture", "target": "m_camera_capture", "color": "#41d8bc", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_pps_capture", "color": "#d84179", "style": "normal"}, {"source": "t_input_rc", "target": "m_msp_osd", "color": "#b4d841", "style": "normal"}, {"source": "t_home_position", "target": "m_msp_osd", "color": "#417dd8", "style": "normal"}, {"source": "t_battery_status", "target": "m_msp_osd", "color": "#d84185", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_msp_osd", "color": "#4172d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_msp_osd", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_msp_osd", "color": "#d89641", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_msp_osd", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_msp_osd", "color": "#d84146", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_atxxxx", "color": "#4178d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_atxxxx", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_atxxxx", "color": "#d84146", "style": "normal"}, {"source": "t_adc_report", "target": "m_board_adc", "color": "#70d841", "style": "normal"}, {"source": "t_open_drone_id_system", "target": "m_uavcan", "color": "#9d41d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_uavcan", "color": "#a341d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_uavcan", "color": "#41abd8", "style": "normal"}, {"source": "t_open_drone_id_operator_id", "target": "m_uavcan", "color": "#d85741", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_uavcan", "color": "#c041d8", "style": "normal"}, {"source": "t_tune_control", "target": "m_uavcan", "color": "#d641d8", "style": "normal"}, {"source": "t_home_position", "target": "m_uavcan", "color": "#417dd8", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_uavcan", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_uavcan", "color": "#41d850", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_uavcan", "color": "#4178d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_uavcan", "color": "#41d85b", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_uavcan", "color": "#41d86c", "style": "normal"}, {"source": "t_uavcan_parameter_request", "target": "m_uavcan", "color": "#d8419c", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_uavcan", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_uavcan", "color": "#d84196", "style": "normal"}, {"source": "t_led_control", "target": "m_uavcan", "color": "#d8b841", "style": "normal"}, {"source": "t_landing_gear", "target": "m_uavcan", "color": "#d84190", "style": "normal"}, {"source": "t_open_drone_id_self_id", "target": "m_uavcan", "color": "#d8d441", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_uavcan", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_uavcan", "color": "#d84168", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_uavcan", "color": "#41d8cd", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_uavcan", "color": "#d84146", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_voxl_esc", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_voxl_esc", "color": "#d84196", "style": "normal"}, {"source": "t_led_control", "target": "m_voxl_esc", "color": "#d8b841", "style": "normal"}, {"source": "t_landing_gear", "target": "m_voxl_esc", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_voxl_esc", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_voxl_esc", "color": "#41d850", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_voxl_esc", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_voxl_esc", "color": "#4178d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_voxl_esc", "color": "#41d85b", "style": "normal"}, {"source": "t_esc_serial_passthru", "target": "m_voxl_esc", "color": "#7041d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_voxl_esc", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_voxl_esc", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_voxl_esc", "color": "#41d8cd", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_voxl_esc", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_voxl_esc", "color": "#d1d841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_voxl_esc", "color": "#d84168", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_vertiq_io", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_vertiq_io", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_vertiq_io", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_vertiq_io", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_vertiq_io", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_test", "target": "m_vertiq_io", "color": "#41d85b", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_vertiq_io", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_vertiq_io", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_vertiq_io", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_vertiq_io", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_vertiq_io", "color": "#d84168", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_camera_trigger", "color": "#41d89f", "style": "normal"}, {"source": "t_pps_capture", "target": "m_camera_trigger", "color": "#41d8bc", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_camera_trigger", "color": "#d84146", "style": "normal"}, {"source": "t_distance_sensor_mode_change_request", "target": "m_lightware_laser_i2c", "color": "#d84174", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_lightware_laser_i2c", "color": "#4178d8", "style": "normal"}, {"source": "t_pwm_input", "target": "m_ll40ls_pwm", "color": "#7b41d8", "style": "normal"}, {"source": "t_obstacle_distance", "target": "m_lightware_sf45_serial", "color": "#d87941", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_lightware_sf45_serial", "color": "#d84163", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_lightware_sf45_serial", "color": "#415bd8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pwm_out", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_pwm_out", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pwm_out", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pwm_out", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_pwm_out", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pwm_out", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_pwm_out", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pwm_out", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pwm_out", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pwm_out", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_pwm_out", "color": "#d84168", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_heater", "color": "#cb41d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_voxl2_io", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_voxl2_io", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_voxl2_io", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_voxl2_io", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_voxl2_io", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_voxl2_io", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_voxl2_io", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_voxl2_io", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_voxl2_io", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_voxl2_io", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_voxl2_io", "color": "#d84168", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_crsf_rc", "color": "#415bd8", "style": "normal"}, {"source": "t_battery_status", "target": "m_crsf_rc", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_crsf_rc", "color": "#4178d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_ghst_rc", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_dsm_rc", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_dsm_rc", "color": "#4178d8", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_is31fl3195", "color": "#d8b841", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled", "color": "#d8b841", "style": "normal"}, {"source": "t_led_control", "target": "m_neopixel", "color": "#d8b841", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_ncp5623c", "color": "#d8b841", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_pwm", "color": "#d8b841", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_lp5562", "color": "#d8b841", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_template_module", "color": "#d87f41", "style": "normal"}, {"source": "t_rover_steering_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d841a1", "style": "normal"}, {"source": "t_rover_velocity_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#c541d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_rover_<PERSON><PERSON>mann", "color": "#4241d8", "style": "normal"}, {"source": "t_rover_throttle_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d86341", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_<PERSON><PERSON>mann", "color": "#8741d8", "style": "normal"}, {"source": "t_actuator_servos", "target": "m_rover_<PERSON><PERSON>mann", "color": "#98d841", "style": "normal"}, {"source": "t_position_controller_status", "target": "m_rover_<PERSON><PERSON>mann", "color": "#41d8c1", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d8c941", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#41abd8", "style": "normal"}, {"source": "t_rover_rate_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#a9d841", "style": "normal"}, {"source": "t_rover_position_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d8a741", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d1d841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#65d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_<PERSON><PERSON>mann", "color": "#415bd8", "style": "normal"}, {"source": "t_rover_attitude_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d841ad", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d84146", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_lat_lon_control", "color": "#c041d8", "style": "normal"}, {"source": "t_wind", "target": "m_fw_lat_lon_control", "color": "#41d8a5", "style": "normal"}, {"source": "t_fixed_wing_longitudinal_setpoint", "target": "m_fw_lat_lon_control", "color": "#d8415d", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_lat_lon_control", "color": "#4172d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_lat_lon_control", "color": "#4178d8", "style": "normal"}, {"source": "t_longitudinal_control_configuration", "target": "m_fw_lat_lon_control", "color": "#d8a141", "style": "normal"}, {"source": "t_lateral_control_configuration", "target": "m_fw_lat_lon_control", "color": "#d841c3", "style": "normal"}, {"source": "t_fixed_wing_lateral_setpoint", "target": "m_fw_lat_lon_control", "color": "#d8ad41", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_lat_lon_control", "color": "#d1d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_lat_lon_control", "color": "#415bd8", "style": "normal"}, {"source": "t_flaps_setpoint", "target": "m_fw_lat_lon_control", "color": "#53d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_lat_lon_control", "color": "#d84146", "style": "normal"}, {"source": "t_airspeed", "target": "m_ekf2", "color": "#41d8d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_ekf2", "color": "#c041d8", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_ekf2", "color": "#d84163", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_ekf2", "color": "#9dd841", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_ekf2", "color": "#4189d8", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_ekf2", "color": "#d87f41", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_ekf2", "color": "#9841d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ekf2", "color": "#4178d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_ekf2", "color": "#4172d8", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_ekf2", "color": "#8141d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_ekf2", "color": "#41d89f", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_ekf2", "color": "#d8416e", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_ekf2", "color": "#4166d8", "style": "normal"}, {"source": "t_aux_global_position", "target": "m_ekf2", "color": "#41d872", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_uxrce_dds_client", "color": "#41d3d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_payload_deliverer", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_landing_target_estimator", "color": "#415bd8", "style": "normal"}, {"source": "t_irlock_report", "target": "m_landing_target_estimator", "color": "#d84641", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_landing_target_estimator", "color": "#d84146", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_autotune_attitude_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_autotune_attitude_control", "color": "#4178d8", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_land_detector", "color": "#d85d41", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_land_detector", "color": "#8741d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_land_detector", "color": "#d1d841", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_land_detector", "color": "#4189d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_land_detector", "color": "#4178d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_land_detector", "color": "#4172d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_land_detector", "color": "#a341d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_land_detector", "color": "#d89641", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_land_detector", "color": "#41d866", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_land_detector", "color": "#9dd841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_land_detector", "color": "#65d841", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_land_detector", "color": "#5fd841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_land_detector", "color": "#d84146", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_gyro_calibration", "color": "#d6d841", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_gyro_calibration", "color": "#cb41d8", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_gyro_calibration", "color": "#d86841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_gyro_calibration", "color": "#4178d8", "style": "normal"}, {"source": "t_actuator_controls_status_0", "target": "m_mc_autotune_attitude_control", "color": "#ba41d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_autotune_attitude_control", "color": "#4178d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_autotune_attitude_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_torque_setpoint", "target": "m_mc_autotune_attitude_control", "color": "#41d861", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_att_control", "color": "#c041d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_fw_att_control", "color": "#41d88e", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_fw_att_control", "color": "#87d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_att_control", "color": "#4178d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_att_control", "color": "#4172d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_att_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_att_control", "color": "#d1d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_att_control", "color": "#415bd8", "style": "normal"}, {"source": "t_fixed_wing_runway_control", "target": "m_fw_att_control", "color": "#d85241", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_att_control", "color": "#d84146", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_att_control", "color": "#c041d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mc_att_control", "color": "#41d88e", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_mc_att_control", "color": "#87d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_att_control", "color": "#4178d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_att_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_att_control", "color": "#d1d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mc_att_control", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_att_control", "color": "#d84146", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_fw_rate_control", "color": "#c5d841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_rate_control", "color": "#c041d8", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_fw_rate_control", "color": "#41d844", "style": "normal"}, {"source": "t_battery_status", "target": "m_fw_rate_control", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_rate_control", "color": "#4178d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_rate_control", "color": "#4172d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_rate_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_rate_control", "color": "#d1d841", "style": "normal"}, {"source": "t_rover_steering_setpoint", "target": "m_rover_mecanum", "color": "#d841a1", "style": "normal"}, {"source": "t_rover_velocity_setpoint", "target": "m_rover_mecanum", "color": "#c541d8", "style": "normal"}, {"source": "t_rover_throttle_setpoint", "target": "m_rover_mecanum", "color": "#d86341", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_rover_mecanum", "color": "#4241d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_mecanum", "color": "#8741d8", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_rover_mecanum", "color": "#d8c941", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_mecanum", "color": "#41abd8", "style": "normal"}, {"source": "t_rover_rate_setpoint", "target": "m_rover_mecanum", "color": "#a9d841", "style": "normal"}, {"source": "t_rover_position_setpoint", "target": "m_rover_mecanum", "color": "#d8a741", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_mecanum", "color": "#d1d841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_mecanum", "color": "#65d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_mecanum", "color": "#415bd8", "style": "normal"}, {"source": "t_rover_attitude_setpoint", "target": "m_rover_mecanum", "color": "#d841ad", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_mecanum", "color": "#d84146", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_camera_feedback", "color": "#41cdd8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_camera_feedback", "color": "#d89641", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_camera_feedback", "color": "#415bd8", "style": "normal"}, {"source": "t_camera_trigger", "target": "m_camera_feedback", "color": "#d84141", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_agp_sim", "color": "#418ed8", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_baro_sim", "color": "#418ed8", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_simulator_sih", "color": "#9241d8", "style": "normal"}, {"source": "t_actuator_outputs_sim", "target": "m_simulator_sih", "color": "#d8417f", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_battery_simulator", "color": "#41d89f", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_battery_simulator", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_battery_simulator", "color": "#4178d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pwm_out_sim", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_pwm_out_sim", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pwm_out_sim", "color": "#d84190", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pwm_out_sim", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_pwm_out_sim", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pwm_out_sim", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_pwm_out_sim", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pwm_out_sim", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pwm_out_sim", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pwm_out_sim", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_pwm_out_sim", "color": "#d84168", "style": "normal"}, {"source": "t_vehicle_local_position_groundtruth", "target": "m_sensor_airspeed_sim", "color": "#6ad841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_sensor_airspeed_sim", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_airspeed_sim", "color": "#418ed8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_gz_bridge", "color": "#4241d8", "style": "normal"}, {"source": "t_internal_combustion_engine_control", "target": "m_gz_bridge", "color": "#d84196", "style": "normal"}, {"source": "t_landing_gear", "target": "m_gz_bridge", "color": "#d84190", "style": "normal"}, {"source": "t_gimbal_device_set_attitude", "target": "m_gz_bridge", "color": "#d8be41", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_gz_bridge", "color": "#d841cf", "style": "normal"}, {"source": "t_gripper", "target": "m_gz_bridge", "color": "#41d850", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_gz_bridge", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_gz_bridge", "color": "#41d8cd", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_gz_bridge", "color": "#41abd8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_gz_bridge", "color": "#41d85b", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_gz_bridge", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_gz_bridge", "color": "#d84168", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_simulator_mavlink", "color": "#9241d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_simulator_mavlink", "color": "#d84185", "style": "normal"}, {"source": "t_actuator_outputs_sim", "target": "m_simulator_mavlink", "color": "#d8417f", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_simulator_mavlink", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_simulator_mavlink", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_local_position_groundtruth", "target": "m_sensor_gps_sim", "color": "#6ad841", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_gps_sim", "color": "#418ed8", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_mag_sim", "color": "#418ed8", "style": "normal"}, {"source": "t_vehicle_attitude_groundtruth", "target": "m_sensor_mag_sim", "color": "#4ed841", "style": "normal"}, {"source": "t_mission", "target": "m_navigator", "color": "#48d841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_navigator", "color": "#c041d8", "style": "normal"}, {"source": "t_wind", "target": "m_navigator", "color": "#41d8a5", "style": "normal"}, {"source": "t_geofence_status", "target": "m_navigator", "color": "#bad841", "style": "normal"}, {"source": "t_dataman_response", "target": "m_navigator", "color": "#92d841", "style": "normal"}, {"source": "t_rtl_status", "target": "m_navigator", "color": "#d8414c", "style": "normal"}, {"source": "t_home_position", "target": "m_navigator", "color": "#417dd8", "style": "normal"}, {"source": "t_position_controller_landing_status", "target": "m_navigator", "color": "#41d84a", "style": "normal"}, {"source": "t_position_controller_status", "target": "m_navigator", "color": "#41d8c1", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_navigator", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_navigator", "color": "#d89641", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_navigator", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_navigator", "color": "#4166d8", "style": "normal"}, {"source": "t_transponder_report", "target": "m_navigator", "color": "#5941d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_navigator", "color": "#d84146", "style": "normal"}, {"source": "t_cpuload", "target": "m_send_event", "color": "#4150d8", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_send_event", "color": "#41d889", "style": "normal"}, {"source": "t_battery_status", "target": "m_send_event", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_send_event", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_send_event", "color": "#41d89f", "style": "normal"}, {"source": "t_safety_button", "target": "m_commander", "color": "#41bcd8", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_commander", "color": "#8c41d8", "style": "normal"}, {"source": "t_vtol_vehicle_status", "target": "m_commander", "color": "#41b0d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_commander", "color": "#a341d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_commander", "color": "#41abd8", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_commander", "color": "#b441d8", "style": "normal"}, {"source": "t_telemetry_status", "target": "m_commander", "color": "#d84c41", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_commander", "color": "#c041d8", "style": "normal"}, {"source": "t_navigator_status", "target": "m_commander", "color": "#42d841", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_commander", "color": "#cb41d8", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_commander", "color": "#d86841", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_commander", "color": "#d87441", "style": "normal"}, {"source": "t_action_request", "target": "m_commander", "color": "#d841d4", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_commander", "color": "#4189d8", "style": "normal"}, {"source": "t_home_position", "target": "m_commander", "color": "#417dd8", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_commander", "color": "#d88541", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_commander", "color": "#4178d8", "style": "normal"}, {"source": "t_mission_result", "target": "m_commander", "color": "#416cd8", "style": "normal"}, {"source": "t_esc_status", "target": "m_commander", "color": "#d88a41", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_commander", "color": "#4172d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_commander", "color": "#d89641", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_commander", "color": "#41d866", "style": "normal"}, {"source": "t_system_power", "target": "m_commander", "color": "#d841b2", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_commander", "color": "#415bd8", "style": "normal"}, {"source": "t_estimator_status_flags", "target": "m_commander", "color": "#d841a7", "style": "normal"}, {"source": "t_cpuload", "target": "m_commander", "color": "#4150d8", "style": "normal"}, {"source": "t_iridiumsbd_status", "target": "m_commander", "color": "#414ad8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_commander", "color": "#4241d8", "style": "normal"}, {"source": "t_power_button_state", "target": "m_commander", "color": "#41d894", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_commander", "color": "#4841d8", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_commander", "color": "#4e41d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_commander", "color": "#d84185", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_commander", "color": "#d8c941", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_commander", "color": "#5341d8", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_commander", "color": "#d84179", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_commander", "color": "#41d89f", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_commander", "color": "#d6d841", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_commander", "color": "#d8416e", "style": "normal"}, {"source": "t_wind", "target": "m_commander", "color": "#41d8a5", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_commander", "color": "#d84163", "style": "normal"}, {"source": "t_event", "target": "m_commander", "color": "#41d8b0", "style": "normal"}, {"source": "t_geofence_result", "target": "m_commander", "color": "#d84152", "style": "normal"}, {"source": "t_logger_status", "target": "m_commander", "color": "#41d8b6", "style": "normal"}, {"source": "t_estimator_status", "target": "m_commander", "color": "#41c7d8", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_commander", "color": "#41d3d8", "style": "normal"}, {"source": "t_pwm_input", "target": "m_commander", "color": "#7b41d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_commander", "color": "#d84146", "style": "normal"}, {"source": "t_esc_status", "target": "m_esc_battery", "color": "#d88a41", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_esc_battery", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_esc_battery", "color": "#4178d8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_gyro_fft", "color": "#4189d8", "style": "normal"}, {"source": "t_sensor_gyro_fifo", "target": "m_gyro_fft", "color": "#d841c9", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_gyro_fft", "color": "#d86841", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_gyro_fft", "color": "#41d866", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_flight_mode_manager", "color": "#d85d41", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_flight_mode_manager", "color": "#c041d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_flight_mode_manager", "color": "#41d88e", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_flight_mode_manager", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_flight_mode_manager", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_flight_mode_manager", "color": "#d1d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_flight_mode_manager", "color": "#d84146", "style": "normal"}, {"source": "t_action_request", "target": "m_manual_control", "color": "#d841d4", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_manual_control", "color": "#5341d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_manual_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_manual_control", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_uuv_pos_control", "color": "#d1d841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_uuv_pos_control", "color": "#65d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_uuv_pos_control", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_uuv_pos_control", "color": "#d84146", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_pos_control", "color": "#8741d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_rover_pos_control", "color": "#41d88e", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_rover_pos_control", "color": "#d89641", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_pos_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_pos_control", "color": "#d1d841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_pos_control", "color": "#65d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_pos_control", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_pos_control", "color": "#d84146", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_mc_rate_control", "color": "#c5d841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_rate_control", "color": "#c041d8", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mc_rate_control", "color": "#41d844", "style": "normal"}, {"source": "t_battery_status", "target": "m_mc_rate_control", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_rate_control", "color": "#4178d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_rate_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_rate_control", "color": "#d1d841", "style": "normal"}, {"source": "t_tiltrotor_extra_controls", "target": "m_control_allocator", "color": "#c0d841", "style": "normal"}, {"source": "t_rpm", "target": "m_control_allocator", "color": "#4183d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_control_allocator", "color": "#4178d8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_control_allocator", "color": "#5341d8", "style": "normal"}, {"source": "t_spoilers_setpoint", "target": "m_control_allocator", "color": "#41d878", "style": "normal"}, {"source": "t_vehicle_torque_setpoint", "target": "m_control_allocator", "color": "#41d861", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_control_allocator", "color": "#d1d841", "style": "normal"}, {"source": "t_flaps_setpoint", "target": "m_control_allocator", "color": "#53d841", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_control_allocator", "color": "#5fd841", "style": "normal"}, {"source": "t_failure_detector_status", "target": "m_control_allocator", "color": "#d8b241", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_sensors", "color": "#cb41d8", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_sensors", "color": "#d86841", "style": "normal"}, {"source": "t_sensor_optical_flow", "target": "m_sensors", "color": "#d86e41", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_sensors", "color": "#d87441", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_sensors", "color": "#4189d8", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_sensors", "color": "#4841d8", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_sensors", "color": "#9841d8", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_sensors", "color": "#4e41d8", "style": "normal"}, {"source": "t_adc_report", "target": "m_sensors", "color": "#70d841", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_sensors", "color": "#41d866", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_sensors", "color": "#d6d841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_sensors", "color": "#d1d841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_mode_manager", "color": "#c041d8", "style": "normal"}, {"source": "t_wind", "target": "m_fw_mode_manager", "color": "#41d8a5", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_fw_mode_manager", "color": "#8741d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_mode_manager", "color": "#4172d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_mode_manager", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_fw_mode_manager", "color": "#d89641", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_mode_manager", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_fw_mode_manager", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_mode_manager", "color": "#d1d841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_fw_mode_manager", "color": "#65d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_mode_manager", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_mode_manager", "color": "#d84146", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_airship_att_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_airship_att_control", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_uuv_att_control", "color": "#41d88e", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_uuv_att_control", "color": "#41d844", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_uuv_att_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_uuv_att_control", "color": "#d1d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_uuv_att_control", "color": "#415bd8", "style": "normal"}, {"source": "t_dataman_request", "target": "m_dataman", "color": "#41d8d3", "style": "normal"}, {"source": "t_rpm", "target": "m_internal_combustion_engine_control", "color": "#4183d8", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_internal_combustion_engine_control", "color": "#4241d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_internal_combustion_engine_control", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_internal_combustion_engine_control", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_local_position_estimator", "color": "#c041d8", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_local_position_estimator", "color": "#d84163", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_local_position_estimator", "color": "#d87f41", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_local_position_estimator", "color": "#a341d8", "style": "normal"}, {"source": "t_vehicle_mocap_odometry", "target": "m_local_position_estimator", "color": "#d89c41", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_local_position_estimator", "color": "#41d89f", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_local_position_estimator", "color": "#4166d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_local_position_estimator", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_local_position_estimator", "color": "#8141d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_local_position_estimator", "color": "#d84146", "style": "normal"}, {"source": "t_camera_trigger", "target": "m_mavlink", "color": "#d84141", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mavlink", "color": "#d87441", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_mavlink", "color": "#d88541", "style": "normal"}, {"source": "t_esc_status", "target": "m_mavlink", "color": "#d88a41", "style": "normal"}, {"source": "t_obstacle_distance_fused", "target": "m_mavlink", "color": "#d89041", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_mavlink", "color": "#d89641", "style": "normal"}, {"source": "t_gimbal_device_set_attitude", "target": "m_mavlink", "color": "#d8be41", "style": "normal"}, {"source": "t_debug_value", "target": "m_mavlink", "color": "#d8c341", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_mavlink", "color": "#d6d841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mavlink", "color": "#d1d841", "style": "normal"}, {"source": "t_input_rc", "target": "m_mavlink", "color": "#b4d841", "style": "normal"}, {"source": "t_debug_vect", "target": "m_mavlink", "color": "#a3d841", "style": "normal"}, {"source": "t_dataman_response", "target": "m_mavlink", "color": "#92d841", "style": "normal"}, {"source": "t_orbit_status", "target": "m_mavlink", "color": "#8cd841", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_mavlink", "color": "#87d841", "style": "normal"}, {"source": "t_figure_eight_status", "target": "m_mavlink", "color": "#81d841", "style": "normal"}, {"source": "t_vehicle_angular_velocity_groundtruth", "target": "m_mavlink", "color": "#7bd841", "style": "normal"}, {"source": "t_debug_array", "target": "m_mavlink", "color": "#76d841", "style": "normal"}, {"source": "t_vehicle_local_position_groundtruth", "target": "m_mavlink", "color": "#6ad841", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_mavlink", "color": "#5fd841", "style": "normal"}, {"source": "t_mount_orientation", "target": "m_mavlink", "color": "#59d841", "style": "normal"}, {"source": "t_vehicle_attitude_groundtruth", "target": "m_mavlink", "color": "#4ed841", "style": "normal"}, {"source": "t_mission", "target": "m_mavlink", "color": "#48d841", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mavlink", "color": "#41d844", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_mavlink", "color": "#41d866", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_mavlink", "color": "#41d86c", "style": "normal"}, {"source": "t_vehicle_odometry", "target": "m_mavlink", "color": "#41d87d", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mavlink", "color": "#41d88e", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_mavlink", "color": "#41d889", "style": "normal"}, {"source": "t_internal_combustion_engine_status", "target": "m_mavlink", "color": "#41d89a", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_mavlink", "color": "#41d89f", "style": "normal"}, {"source": "t_wind", "target": "m_mavlink", "color": "#41d8a5", "style": "normal"}, {"source": "t_satellite_info", "target": "m_mavlink", "color": "#41d8ab", "style": "normal"}, {"source": "t_event", "target": "m_mavlink", "color": "#41d8b0", "style": "normal"}, {"source": "t_position_controller_status", "target": "m_mavlink", "color": "#41d8c1", "style": "normal"}, {"source": "t_sensor_hygrometer", "target": "m_mavlink", "color": "#41d8c7", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_mavlink", "color": "#41cdd8", "style": "normal"}, {"source": "t_airspeed", "target": "m_mavlink", "color": "#41d8d8", "style": "normal"}, {"source": "t_estimator_status", "target": "m_mavlink", "color": "#41c7d8", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_mavlink", "color": "#41d3d8", "style": "normal"}, {"source": "t_gimbal_manager_status", "target": "m_mavlink", "color": "#41b6d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mavlink", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_mavlink", "color": "#41a5d8", "style": "normal"}, {"source": "t_open_drone_id_arm_status", "target": "m_mavlink", "color": "#419ad8", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_mavlink", "color": "#418ed8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_mavlink", "color": "#4189d8", "style": "normal"}, {"source": "t_rpm", "target": "m_mavlink", "color": "#4183d8", "style": "normal"}, {"source": "t_home_position", "target": "m_mavlink", "color": "#417dd8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mavlink", "color": "#4178d8", "style": "normal"}, {"source": "t_mission_result", "target": "m_mavlink", "color": "#416cd8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_mavlink", "color": "#4172d8", "style": "normal"}, {"source": "t_health_report", "target": "m_mavlink", "color": "#4161d8", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_mavlink", "color": "#4166d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mavlink", "color": "#415bd8", "style": "normal"}, {"source": "t_debug_key_value", "target": "m_mavlink", "color": "#4155d8", "style": "normal"}, {"source": "t_cpuload", "target": "m_mavlink", "color": "#4150d8", "style": "normal"}, {"source": "t_uavcan_parameter_value", "target": "m_mavlink", "color": "#4144d8", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_mavlink", "color": "#4841d8", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_mavlink", "color": "#4e41d8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_mavlink", "color": "#5341d8", "style": "normal"}, {"source": "t_transponder_report", "target": "m_mavlink", "color": "#5941d8", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_mavlink", "color": "#5f41d8", "style": "normal"}, {"source": "t_register_ext_component_reply", "target": "m_mavlink", "color": "#6a41d8", "style": "normal"}, {"source": "t_tecs_status", "target": "m_mavlink", "color": "#7641d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_mavlink", "color": "#8741d8", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_mavlink", "color": "#8c41d8", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_mavlink", "color": "#9241d8", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_mavlink", "color": "#9841d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_mavlink", "color": "#a341d8", "style": "normal"}, {"source": "t_gimbal_manager_information", "target": "m_mavlink", "color": "#a941d8", "style": "normal"}, {"source": "t_camera_status", "target": "m_mavlink", "color": "#af41d8", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_mavlink", "color": "#b441d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mavlink", "color": "#c041d8", "style": "normal"}, {"source": "t_camera_capture", "target": "m_mavlink", "color": "#d841be", "style": "normal"}, {"source": "t_ulog_stream", "target": "m_mavlink", "color": "#d841b8", "style": "normal"}, {"source": "t_gimbal_v1_command", "target": "m_mavlink", "color": "#d8418a", "style": "normal"}, {"source": "t_battery_status", "target": "m_mavlink", "color": "#d84185", "style": "normal"}, {"source": "t_actuator_outputs_sim", "target": "m_mavlink", "color": "#d8417f", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_mavlink", "color": "#d84179", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_mavlink", "color": "#d84163", "style": "normal"}, {"source": "t_geofence_result", "target": "m_mavlink", "color": "#d84152", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mavlink", "color": "#d84146", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_battery_status", "color": "#afd841", "style": "normal"}, {"source": "t_adc_report", "target": "m_battery_status", "color": "#70d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_battery_status", "color": "#4178d8", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_gimbal", "color": "#5f41d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_gimbal", "color": "#c041d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_gimbal", "color": "#8741d8", "style": "normal"}, {"source": "t_gimbal_manager_set_manual_control", "target": "m_gimbal", "color": "#6541d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_gimbal", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_gimbal", "color": "#d89641", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_gimbal", "color": "#41abd8", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_gimbal", "color": "#41cdd8", "style": "normal"}, {"source": "t_gimbal_manager_set_attitude", "target": "m_gimbal", "color": "#cbd841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_gimbal", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_roi", "target": "m_gimbal", "color": "#4194d8", "style": "normal"}, {"source": "t_ulog_stream_ack", "target": "m_logger", "color": "#41d855", "style": "normal"}, {"source": "t_battery_status", "target": "m_logger", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_logger", "color": "#4178d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_logger", "color": "#41abd8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_logger", "color": "#41d89f", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_vtol_att_control", "color": "#8741d8", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_vtol_att_control", "color": "#41a5d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_vtol_att_control", "color": "#c041d8", "style": "normal"}, {"source": "t_action_request", "target": "m_vtol_att_control", "color": "#d841d4", "style": "normal"}, {"source": "t_home_position", "target": "m_vtol_att_control", "color": "#417dd8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_vtol_att_control", "color": "#4172d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_vtol_att_control", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_vtol_att_control", "color": "#415bd8", "style": "normal"}, {"source": "t_fw_virtual_attitude_setpoint", "target": "m_vtol_att_control", "color": "#41d883", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_vtol_att_control", "color": "#41d89f", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_vtol_att_control", "color": "#d1d841", "style": "normal"}, {"source": "t_tecs_status", "target": "m_vtol_att_control", "color": "#7641d8", "style": "normal"}, {"source": "t_mc_virtual_attitude_setpoint", "target": "m_vtol_att_control", "color": "#41c1d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_vtol_att_control", "color": "#d84146", "style": "normal"}, {"source": "t_airspeed", "target": "m_airspeed_selector", "color": "#41d8d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_airspeed_selector", "color": "#c041d8", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_airspeed_selector", "color": "#5fd841", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_airspeed_selector", "color": "#d88541", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_airspeed_selector", "color": "#afd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_airspeed_selector", "color": "#4178d8", "style": "normal"}, {"source": "t_tecs_status", "target": "m_airspeed_selector", "color": "#7641d8", "style": "normal"}, {"source": "t_estimator_status", "target": "m_airspeed_selector", "color": "#41c7d8", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_airspeed_selector", "color": "#9dd841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_airspeed_selector", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_airspeed_selector", "color": "#d84146", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_temperature_compensation", "color": "#cb41d8", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_temperature_compensation", "color": "#d86841", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_temperature_compensation", "color": "#d87441", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_temperature_compensation", "color": "#41d89f", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_temperature_compensation", "color": "#b441d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_pos_control", "color": "#c041d8", "style": "normal"}, {"source": "t_vehicle_constraints", "target": "m_mc_pos_control", "color": "#d84157", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_pos_control", "color": "#d1d841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_mc_pos_control", "color": "#65d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_pos_control", "color": "#d84146", "style": "normal"}, {"source": "t_input_rc", "target": "m_rc_update", "color": "#b4d841", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_rc_update", "color": "#5341d8", "style": "normal"}, {"source": "t_rc_parameter_map", "target": "m_rc_update", "color": "#d8cf41", "style": "normal"}, {"source": "t_rover_steering_setpoint", "target": "m_rover_differential", "color": "#d841a1", "style": "normal"}, {"source": "t_rover_velocity_setpoint", "target": "m_rover_differential", "color": "#c541d8", "style": "normal"}, {"source": "t_rover_throttle_setpoint", "target": "m_rover_differential", "color": "#d86341", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_rover_differential", "color": "#4241d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_differential", "color": "#8741d8", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_rover_differential", "color": "#d8c941", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_differential", "color": "#41abd8", "style": "normal"}, {"source": "t_rover_rate_setpoint", "target": "m_rover_differential", "color": "#a9d841", "style": "normal"}, {"source": "t_rover_position_setpoint", "target": "m_rover_differential", "color": "#d8a741", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_differential", "color": "#d1d841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_differential", "color": "#65d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_differential", "color": "#415bd8", "style": "normal"}, {"source": "t_rover_attitude_setpoint", "target": "m_rover_differential", "color": "#d841ad", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_differential", "color": "#d84146", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_attitude_estimator_q", "color": "#d87f41", "style": "normal"}, {"source": "t_vehicle_mocap_odometry", "target": "m_attitude_estimator_q", "color": "#d89c41", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_attitude_estimator_q", "color": "#415bd8", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_attitude_estimator_q", "color": "#8141d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_attitude_estimator_q", "color": "#d84146", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mag_bias_estimator", "color": "#d87441", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mag_bias_estimator", "color": "#4178d8", "style": "normal"}]}