#!/bin/sh
#
# Fixed wing default parameters
#
# NOTE: Script variables are declared/initialized/unset in the rcS script.
#

set VEHICLE_TYPE fw

# MAV_TYPE_FIXED_WING 1
param set-default MAV_TYPE 1

#
# Default parameters for fixed wing UAVs.
#
param set-default COM_VEL_FS_EVH 3

param set-default COM_POS_LOW_EPH 50

# Disable preflight disarm to not interfere with external launching
param set-default COM_DISARM_PRFLT -1

param set-default EKF2_ARSP_THR 8
param set-default EKF2_FUSE_BETA 1
param set-default EKF2_MAG_ACCLIM 0
param set-default EKF2_REQ_EPH 10
param set-default EKF2_REQ_EPV 10
param set-default EKF2_REQ_HDRIFT 0.5
param set-default EKF2_REQ_SACC 1
param set-default EKF2_REQ_VDRIFT 1
param set-default EKF2_RNG_QLTY_T 3

param set-default RTL_TYPE 1
param set-default RTL_RETURN_ALT 100
param set-default RTL_DESCEND_ALT 100
param set-default RTL_LAND_DELAY -1

#
# FW uses L1 distance for acceptance radius.
# Set a smaller NAV_ACC_RAD for vertical acceptance distance.
#
param set-default NAV_ACC_RAD 10

param set-default MIS_TAKEOFF_ALT 25
param set-default MIS_TKO_LAND_REQ 2

#
# FW takeoff acceleration can easily exceed ublox GPS 2G default.
#
param set-default GPS_UBX_DYNMODEL 8

param set-default SYS_HAS_NUM_ASPD 1 # by default require an airspeed sensor
