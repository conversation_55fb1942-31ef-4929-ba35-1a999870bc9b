# Запуск з кидка (Мультикоптер)

<Badge type="tip" text="PX4 v1.15" /> <Badge type="warning" text="Experimental" />

:::warning
Експериментальні налаштування
This feature was introduced in PX4 v1.15.

- Його ще не широко тестували на різних конфігураціях транспортних засобів або сценаріях.
- Більшість тестувань проведено в режимі позиціонування.
  Інші режими також повинні працювати.

:::

Ця функція дозволяє запускати багторот з фіксованої позиції, після чого кидати його в повітря.
The vehicle turns on the motors only after the launch is detected, and then operates according to its current mode.

When throw launch is enabled, the vehicle is initially armed in a "lockdown" state, in which the propellers do not spin.
Гвинтики не будуть активуватися, поки транспортний засіб не буде кинутий або роззброєний, а сигнал готовності буде продовжувати грати протягом цього часу.
Транспортний засіб не буде автоматично роззброєний після увімкнення, і його потрібно роззброїти вручну, якщо ви вирішите не кидати його.

Транспортний засіб виявляє, що його викинули на підставі досягнення певної швидкості (5 м/с), а потім запускає двигуни на вершині кидка (як тільки він визначає, що почав падати).
Вам потрібно кинути транспортний засіб настільки високо, щоб він міг стабілізувати свою висоту добре до того, як впаде десь близько до людей або перешкод.

Примітки:

- Режим за замовчуванням вимкнено, і його необхідно активувати, використовуючи [параметр](#parameters), перш ніж озброювати.
- Коли ввімкнено, ви не можете злітати з землі, використовуючи звичайні режими.
- Транспортувати транспортний засіб необхідно після встановлення зброї та до кидка.
  Зокрема, кидок не повинен виконуватися з рухомої платформи.
  Причина полягає в тому, що умова запуску двигунів залежить від абсолютної швидкості багатокоптера і не враховує будь-якого додаткового руху.
  Спроба викинути дрон з рухомої платформи може призвести до того, що двигуни будуть запущені заздалегідь.

## Безпека

:::warning
Кидок запуску небезпечний, оскільки вимагає, щоб оператор тримав озброєний багтрекер і був у непосредній близькості, коли він літає.
:::

Перед тестуванням переконайтеся, що літак може злетіти у звичайному положенні або режимах відльоту.
Також переконайтеся, що гвинти не обертаються при увімкненні після активації функції.

Окрім того:

1. Носіть засоби безпеки.
  Захист для очей та рукавички для роботи рекомендовані.
2. Маєте легкий доступний та протестований [вимикач вимкнення](../config/safety.md#kill-switch).
  Нагадайте оператору бути уважним та використовувати вимикач аварійного вимкнення за потреби.
  Пілоти часто забувають, що транспортні засоби можна замінити, але вони - ні!
3. Тестуйте якомога більше без гвинтів.
  Утримуйте інструменти для зняття гвинтів пропелерів поруч/легкодоступними.
4. Перевірте цю функцію з принаймні двома людьми — один керує літаком, інший — пультом дистанційного керування.
5. Пам'ятайте, що після кидка точна поведінка літака може бути важко передбачити, оскільки вона сильно залежить від способу кидка.
  Іноді воно залишатиметься на місці ідеально, але іноді (наприклад, через великий кочення), воно може відхилятися в один бік під час стабілізації.
  Дотримуйтеся безпечної відстані!

Під час першого польоту нового транспортного засобу ми рекомендуємо виконати [Тест запуску без гвинтів (Throw Launch test without propellers)](#throw-launch-pretest) (див. нижче).

## Запуск з катапульти чи підкиданням Pretest

Запуск кидання без пропелерів може бути використаний, щоб підтвердити, що озброєння не відбувається передчасно, і для оператора зрозуміти, чого очікувати під час польоту.

Кроки для цього тесту:

1. Демонтуйте пропелери.
2. Встановіть [COM_THROW_EN](../advanced_config/parameter_reference.md#COM_THROW_EN) на `Увімкнено`.
3. Озброїте літак.
  Двигуни не повинні крутитися, але транспортний засіб повинен бути збройований і продовжувати відтворювати мелодію зброювання.
4. Киньте літак приблизно на 2 м у повітря.
  Якщо літак не буде кидати достатньо високо, двигуни не ввімкнуться.
5. Двигуни повинні запуститися одразу після перетинання вершини.
6. Увімкніть вимикач вбивства (ідеально, щоб це робив друга особа, яка керує RC).
7. Спіймай дрон.
  Не забувайте використовувати захисні рукавички!

## Запуск з катапульти чи підкиданням

Кроки для запуску з кидка:

1. Встановіть [COM_THROW_EN](../advanced_config/parameter_reference.md#COM_THROW_EN) на `Увімкнено`.
2. Озброїте літак.
  Пропелери не повинні обертатися, але транспортний засіб повинен бути збройований і продовжувати відтворювати мелодію зброювання.
3. Викиньте літак від себе, вперед і вгору (рекомендується близько 2 м відстані та 2 м вгору).
  - Транспортний засіб повинен досягти швидкості [COM_THROW_SPEED](../advanced_config/parameter_reference.md#COM_THROW_SPEED), щоб виявити запуск, яка за замовчуванням встановлена на 5 м/с.
    Якщо цю швидкість не досягнуто, двигуни не запустяться, і літак впаде на землю.
  - Спробуйте уникати надмірного обертання під час кидка, оскільки це може призвести до відмови дрона або непередбачуваної поведінки.
    Точне значення "надмірного обертання" залежить від платформи: наприклад, [PX4Vision](../complete_vehicles_mc/px4_vision_kit.md), яка використовувалася для тестування, все ще вдалося відновитися після 2-3 повних обертань.
4. Після виявлення швидкості вниз (транспортний засіб досягає свого апексу і починає падати), мотори повинні увімкнутися, і транспортний засіб почне летіти в поточному режимі.

## Параметри

Наступні параметри можуть бути використані для увімкнення та налаштування запуску з кидка:

- [COM_THROW_EN](../advanced_config/parameter_reference.md#COM_THROW_EN) увімкнує функцію.
- [COM_THROW_SPEED](../advanced_config/parameter_reference.md#COM_THROW_SPEED) визначає мінімальну швидкість, яку повинен досягти літак, щоб виявити кидок.
  Якщо цього не буде досягнуто, двигуни не включаться.

## Дивіться також

- [Режим відльоту (фіксований крило) > Катапульт/ручний запуск](../flight_modes_fw/takeoff.md#catapult-hand-launch).

<!--
Notes:
https://github.com/PX4/PX4-Autopilot/pull/23822
https://github.com/PX4/PX4-Autopilot/blob/371a99c3221dd09dce0b218c45df405188d96cfd/src/modules/commander/Commander.cpp#L1894-L1896 - lockdown setting
-->
