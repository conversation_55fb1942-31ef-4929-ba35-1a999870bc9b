# Конфігурація літального апарату  з нерухомим крилом

Fixed-wing configuration and calibration follows the same high level steps as other frames: selection of firmware, configuration of the frame including actuator/motor geometry and output mappings, sensor configuration and calibration, configuration of safety and other features, and finally tuning.

:::info
This topic is the recommended entry point when performing first-time configuration and calibration of a new multicopter frame.
:::

Основними кроками є:

- [Standard Configuration](../config/index.md)

- [Autotune](../config/autotune_fw.md) - PID Tuning

  ::: info
  Autotune simplifies the manual process described in: [Fixed-wing Rate/Attitude Controller Tuning Guide](../config_fw/pid_tuning_guide_fixedwing.md).

:::

- [Fixed-wing Altitude/Position Controller Tuning Guide](../config_fw/position_tuning_guide_fixedwing.md)

- [Fixed-wing Trimming Guide](../config_fw/trimming_guide_fixedwing.md)
