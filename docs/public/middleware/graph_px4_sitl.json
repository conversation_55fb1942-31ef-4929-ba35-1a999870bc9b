{"nodes": [{"id": "m_fw_autotune_attitude_control", "name": "fw_autotune_attitude_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_autotune_attitude_control", "name": "mc_autotune_attitude_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_landing_target_estimator", "name": "landing_target_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_local_position_estimator", "name": "local_position_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_temperature_compensation", "name": "temperature_compensation", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_system_power_simulator", "name": "system_power_simulator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_attitude_estimator_q", "name": "attitude_estimator_q", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_airship_att_control", "name": "airship_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_flight_mode_manager", "name": "flight_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_airspeed_sim", "name": "sensor_airspeed_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_lat_lon_control", "name": "fw_lat_lon_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mag_bias_estimator", "name": "mag_bias_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_differential", "name": "rover_differential", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_airspeed_selector", "name": "airspeed_selector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_control_allocator", "name": "control_allocator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_payload_deliverer", "name": "payload_deliverer", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_pos_control", "name": "rover_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_battery_simulator", "name": "battery_simulator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_simulator_mavlink", "name": "simulator_mavlink", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fake_magnetometer", "name": "fake_magnetometer", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_px4_mavlink_debug", "name": "px4_mavlink_debug", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_work_item_example", "name": "work_item_example", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gyro_calibration", "name": "gyro_calibration", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uxrce_dds_client", "name": "uxrce_dds_client", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vtol_att_control", "name": "vtol_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_feedback", "name": "camera_feedback", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_mode_manager", "name": "fw_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_rate_control", "name": "fw_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_rate_control", "name": "mc_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_<PERSON><PERSON>mann", "name": "r<PERSON>_<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_baro_sim", "name": "sensor_baro_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uuv_att_control", "name": "uuv_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uuv_pos_control", "name": "uuv_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_trigger", "name": "camera_trigger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_att_control", "name": "fw_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_manual_control", "name": "manual_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_att_control", "name": "mc_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_pos_control", "name": "mc_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_agp_sim", "name": "sensor_agp_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_gps_sim", "name": "sensor_gps_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensor_mag_sim", "name": "sensor_mag_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_px4_simple_app", "name": "px4_simple_app", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_land_detector", "name": "land_detector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rover_mecanum", "name": "rover_mecanum", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_simulator_sih", "name": "simulator_sih", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_actuator_test", "name": "actuator_test", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tune_control", "name": "tune_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_out_sim", "name": "pwm_out_sim", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tone_alarm", "name": "tone_alarm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_send_event", "name": "send_event", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_commander", "name": "commander", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_navigator", "name": "navigator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rc_update", "name": "rc_update", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gz_bridge", "name": "gz_bridge", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gyro_fft", "name": "gyro_fft", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_load_mon", "name": "load_mon", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fake_gps", "name": "fake_gps", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fake_imu", "name": "fake_imu", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_msp_osd", "name": "msp_osd", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dataman", "name": "dataman", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mavlink", "name": "mavlink", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensors", "name": "sensors", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_failure", "name": "failure", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gimbal", "name": "gimbal", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_logger", "name": "logger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tests", "name": "tests", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ekf2", "name": "ekf2", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gps", "name": "gps", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "t_vehicle_angular_velocity_groundtruth", "name": "vehicle_angular_velocity_groundtruth", "type": "topic", "color": "#d8a941", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_global_position_groundtruth", "name": "vehicle_global_position_groundtruth", "type": "topic", "color": "#abd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_controller_landing_status", "name": "position_controller_landing_status", "type": "topic", "color": "#41d845", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_longitudinal_control_configuration", "name": "longitudinal_control_configuration", "type": "topic", "color": "#d8416e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position_groundtruth", "name": "vehicle_local_position_groundtruth", "type": "topic", "color": "#d84161", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_set_manual_control", "name": "gimbal_manager_set_manual_control", "type": "topic", "color": "#ccd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_longitudinal_setpoint", "name": "fixed_wing_longitudinal_setpoint", "type": "topic", "color": "#41d8b4", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_autotune_attitude_control_status", "name": "autotune_attitude_control_status", "type": "topic", "color": "#7141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position_setpoint", "name": "vehicle_local_position_setpoint", "type": "topic", "color": "#415fd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_attitude_status", "name": "gimbal_device_attitude_status", "type": "topic", "color": "#77d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_lateral_control_configuration", "name": "lateral_control_configuration", "type": "topic", "color": "#d84196", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude_groundtruth", "name": "vehicle_attitude_groundtruth", "type": "topic", "color": "#6ad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mc_virtual_attitude_setpoint", "name": "mc_virtual_attitude_setpoint", "type": "topic", "color": "#41d886", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fw_virtual_attitude_setpoint", "name": "fw_virtual_attitude_setpoint", "type": "topic", "color": "#41c8d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_register_ext_component_reply", "name": "register_ext_component_reply", "type": "topic", "color": "#d8415b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_lateral_setpoint", "name": "fixed_wing_lateral_setpoint", "type": "topic", "color": "#d85441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_set_attitude", "name": "gimbal_manager_set_attitude", "type": "topic", "color": "#9ed841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_controller_status", "name": "position_controller_status", "type": "topic", "color": "#b2d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_controls_status_0", "name": "actuator_controls_status_0", "type": "topic", "color": "#41d880", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorControlsStatus.msg"}, {"id": "t_gimbal_manager_information", "name": "gimbal_manager_information", "type": "topic", "color": "#4159d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_set_attitude", "name": "gimbal_device_set_attitude", "type": "topic", "color": "#d841a3", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_selector_status", "name": "estimator_selector_status", "type": "topic", "color": "#d84141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_runway_control", "name": "fixed_wing_runway_control", "type": "topic", "color": "#d89641", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude_setpoint", "name": "vehicle_attitude_setpoint", "type": "topic", "color": "#d8ca41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_information", "name": "gimbal_device_information", "type": "topic", "color": "#91d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_setpoint_triplet", "name": "position_setpoint_triplet", "type": "topic", "color": "#5d41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tiltrotor_extra_controls", "name": "tiltrotor_extra_controls", "type": "topic", "color": "#d84e41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_control_allocator_status", "name": "control_allocator_status", "type": "topic", "color": "#a5d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_position_setpoint", "name": "rover_position_setpoint", "type": "topic", "color": "#d89c41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_setpoint", "name": "manual_control_setpoint", "type": "topic", "color": "#63d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_failure_detector_status", "name": "failure_detector_status", "type": "topic", "color": "#50d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_thrust_setpoint", "name": "vehicle_thrust_setpoint", "type": "topic", "color": "#41d852", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/VehicleThrustSetpoint.msg"}, {"id": "t_vehicle_global_position", "name": "vehicle_global_position", "type": "topic", "color": "#41d8ae", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_torque_setpoint", "name": "vehicle_torque_setpoint", "type": "topic", "color": "#4180d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/VehicleTorqueSetpoint.msg"}, {"id": "t_rover_steering_setpoint", "name": "rover_steering_setpoint", "type": "topic", "color": "#5641d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_switches", "name": "manual_control_switches", "type": "topic", "color": "#7741d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_visual_odometry", "name": "vehicle_visual_odometry", "type": "topic", "color": "#9141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_throttle_setpoint", "name": "rover_throttle_setpoint", "type": "topic", "color": "#a541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_launch_detection_status", "name": "launch_detection_status", "type": "topic", "color": "#b241d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_attitude_setpoint", "name": "rover_attitude_setpoint", "type": "topic", "color": "#bf41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_flight_phase_estimation", "name": "flight_phase_estimation", "type": "topic", "color": "#d841d1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_velocity_setpoint", "name": "rover_velocity_setpoint", "type": "topic", "color": "#d8418f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_rates_setpoint", "name": "vehicle_rates_setpoint", "type": "topic", "color": "#d84741", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position", "name": "vehicle_local_position", "type": "topic", "color": "#bfd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status_flags", "name": "estimator_status_flags", "type": "topic", "color": "#41b4d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_mocap_odometry", "name": "vehicle_mocap_odometry", "type": "topic", "color": "#d8419c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_differential_pressure", "name": "differential_pressure", "type": "topic", "color": "#d88941", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_status", "name": "gimbal_manager_status", "type": "topic", "color": "#d8b041", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_offboard_control_mode", "name": "offboard_control_mode", "type": "topic", "color": "#84d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_land_detected", "name": "vehicle_land_detected", "type": "topic", "color": "#41a7d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_sensor_bias", "name": "estimator_sensor_bias", "type": "topic", "color": "#6341d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_control_mode", "name": "vehicle_control_mode", "type": "topic", "color": "#41d8d5", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_outputs_sim", "name": "actuator_outputs_sim", "type": "topic", "color": "#4166d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorOutputs.msg"}, {"id": "t_actuator_servos_trim", "name": "actuator_servos_trim", "type": "topic", "color": "#d841ca", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_trajectory_setpoint", "name": "trajectory_setpoint", "type": "topic", "color": "#d86141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vtol_vehicle_status", "name": "vtol_vehicle_status", "type": "topic", "color": "#8bd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_figure_eight_status", "name": "figure_eight_status", "type": "topic", "color": "#41d85f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_aux_global_position", "name": "aux_global_position", "type": "topic", "color": "#414bd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/EstimatorAidSource2d.msg"}, {"id": "t_vehicle_constraints", "name": "vehicle_constraints", "type": "topic", "color": "#4941d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rover_rate_setpoint", "name": "rover_rate_setpoint", "type": "topic", "color": "#6a41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_target_pose", "name": "landing_target_pose", "type": "topic", "color": "#9e41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_optical_flow", "name": "sensor_optical_flow", "type": "topic", "color": "#d841b0", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command_ack", "name": "vehicle_command_ack", "type": "topic", "color": "#d84168", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_power_button_state", "name": "power_button_state", "type": "topic", "color": "#d8a341", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu_status", "name": "vehicle_imu_status", "type": "topic", "color": "#d8bd41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed_validated", "name": "airspeed_validated", "type": "topic", "color": "#41d866", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensors_status_imu", "name": "sensors_status_imu", "type": "topic", "color": "#4152d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear_wheel", "name": "landing_gear_wheel", "type": "topic", "color": "#d341d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_transponder_report", "name": "transponder_report", "type": "topic", "color": "#d8417b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mount_orientation", "name": "mount_orientation", "type": "topic", "color": "#71d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_time_estimate", "name": "rtl_time_estimate", "type": "topic", "color": "#41d84b", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_v1_command", "name": "gimbal_v1_command", "type": "topic", "color": "#41d88d", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_spoilers_setpoint", "name": "spoilers_setpoint", "type": "topic", "color": "#41d8ce", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/NormalizedUnsignedSetpoint.msg"}, {"id": "t_sensor_correction", "name": "sensor_correction", "type": "topic", "color": "#418dd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_outputs", "name": "actuator_outputs", "type": "topic", "color": "#d87541", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorOutputs.msg"}, {"id": "t_rc_parameter_map", "name": "rc_parameter_map", "type": "topic", "color": "#d8b641", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status", "name": "estimator_status", "type": "topic", "color": "#41d873", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_telemetry_status", "name": "telemetry_status", "type": "topic", "color": "#419ad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude", "name": "vehicle_attitude", "type": "topic", "color": "#416cd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_navigator_status", "name": "navigator_status", "type": "topic", "color": "#4341d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_odometry", "name": "vehicle_odometry", "type": "topic", "color": "#7e41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_selection", "name": "sensor_selection", "type": "topic", "color": "#d841c4", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_response", "name": "dataman_response", "type": "topic", "color": "#d841b6", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gyro_fifo", "name": "sensor_gyro_fifo", "type": "topic", "color": "#d84154", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_servos", "name": "actuator_servos", "type": "topic", "color": "#d87b41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream_ack", "name": "ulog_stream_ack", "type": "topic", "color": "#d88241", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_motors", "name": "actuator_motors", "type": "topic", "color": "#d3d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_request", "name": "dataman_request", "type": "topic", "color": "#b9d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_controls", "name": "gimbal_controls", "type": "topic", "color": "#7ed841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_key_value", "name": "debug_key_value", "type": "topic", "color": "#5dd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_distance_sensor", "name": "distance_sensor", "type": "topic", "color": "#41d8c8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_combined", "name": "sensor_combined", "type": "topic", "color": "#41d5d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command", "name": "vehicle_command", "type": "topic", "color": "#4193d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_result", "name": "geofence_result", "type": "topic", "color": "#4179d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gps_inject_data", "name": "gps_inject_data", "type": "topic", "color": "#c641d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_status", "name": "geofence_status", "type": "topic", "color": "#d841bd", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_trigger", "name": "camera_trigger", "type": "topic", "color": "#c6d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_takeoff_status", "name": "takeoff_status", "type": "topic", "color": "#49d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_failsafe_flags", "name": "failsafe_flags", "type": "topic", "color": "#43d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mission_result", "name": "mission_result", "type": "topic", "color": "#41d859", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_flaps_setpoint", "name": "flaps_setpoint", "type": "topic", "color": "#41d879", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/NormalizedUnsignedSetpoint.msg"}, {"id": "t_actuator_armed", "name": "actuator_armed", "type": "topic", "color": "#41c1d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_capture", "name": "camera_capture", "type": "topic", "color": "#41bbd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_satellite_info", "name": "satellite_info", "type": "topic", "color": "#4173d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_action_request", "name": "action_request", "type": "topic", "color": "#b941d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_battery_status", "name": "battery_status", "type": "topic", "color": "#cc41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_status", "name": "vehicle_status", "type": "topic", "color": "#d8414e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_status", "name": "camera_status", "type": "topic", "color": "#d86e41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_irlock_report", "name": "irlock_report", "type": "topic", "color": "#41d8bb", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_health_report", "name": "health_report", "type": "topic", "color": "#8441d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_logger_status", "name": "logger_status", "type": "topic", "color": "#8b41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_home_position", "name": "home_position", "type": "topic", "color": "#d841d7", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_test", "name": "actuator_test", "type": "topic", "color": "#d84175", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tune_control", "name": "tune_control", "type": "topic", "color": "#d86841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_accel", "name": "sensor_accel", "type": "topic", "color": "#d8d141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear", "name": "landing_gear", "type": "topic", "color": "#56d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_orbit_status", "name": "orbit_status", "type": "topic", "color": "#41d86c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_system_power", "name": "system_power", "type": "topic", "color": "#41d8a7", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu", "name": "vehicle_imu", "type": "topic", "color": "#d85b41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream", "name": "ulog_stream", "type": "topic", "color": "#41d893", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gyro", "name": "sensor_gyro", "type": "topic", "color": "#41ced8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tecs_status", "name": "tecs_status", "type": "topic", "color": "#41a1d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_baro", "name": "sensor_baro", "type": "topic", "color": "#ab41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_array", "name": "debug_array", "type": "topic", "color": "#d841a9", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_value", "name": "debug_value", "type": "topic", "color": "#d84189", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_roi", "name": "vehicle_roi", "type": "topic", "color": "#d84147", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_esc_status", "name": "esc_status", "type": "topic", "color": "#d88f41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gps", "name": "sensor_gps", "type": "topic", "color": "#41d89a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/SensorGps.msg"}, {"id": "t_sensor_mag", "name": "sensor_mag", "type": "topic", "color": "#41d8a1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_vect", "name": "debug_vect", "type": "topic", "color": "#4186d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_status", "name": "rtl_status", "type": "topic", "color": "#5041d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed", "name": "airspeed", "type": "topic", "color": "#98d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/EstimatorAidSource1d.msg"}, {"id": "t_input_rc", "name": "input_rc", "type": "topic", "color": "#41aed8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gripper", "name": "gripper", "type": "topic", "color": "#d8c441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_cpuload", "name": "cpuload", "type": "topic", "color": "#41d8c1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mission", "name": "mission", "type": "topic", "color": "#4145d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_event", "name": "event", "type": "topic", "color": "#d84182", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_wind", "name": "wind", "type": "topic", "color": "#d8d741", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/Wind.msg"}, {"id": "t_rpm", "name": "rpm", "type": "topic", "color": "#9841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}], "links": [{"source": "m_camera_trigger", "target": "t_camera_trigger", "color": "#c6d841", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_gps", "target": "t_satellite_info", "color": "#4173d8", "style": "dashed"}, {"source": "m_gps", "target": "t_sensor_gps", "color": "#41d89a", "style": "dashed"}, {"source": "m_gps", "target": "t_gps_inject_data", "color": "#c641d8", "style": "dashed"}, {"source": "m_tone_alarm", "target": "t_tune_control", "color": "#d86841", "style": "dashed"}, {"source": "m_airship_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#41d852", "style": "dashed"}, {"source": "m_airship_att_control", "target": "t_vehicle_torque_setpoint", "color": "#4180d8", "style": "dashed"}, {"source": "m_airspeed_selector", "target": "t_airspeed_validated", "color": "#41d866", "style": "dashed"}, {"source": "m_attitude_estimator_q", "target": "t_vehicle_attitude", "color": "#416cd8", "style": "dashed"}, {"source": "m_camera_feedback", "target": "t_camera_capture", "color": "#41bbd8", "style": "dashed"}, {"source": "m_commander", "target": "t_home_position", "color": "#d841d7", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_armed", "color": "#41c1d8", "style": "dashed"}, {"source": "m_commander", "target": "t_health_report", "color": "#8441d8", "style": "dashed"}, {"source": "m_commander", "target": "t_failure_detector_status", "color": "#50d841", "style": "dashed"}, {"source": "m_commander", "target": "t_power_button_state", "color": "#d8a341", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_control_mode", "color": "#41d8d5", "style": "dashed"}, {"source": "m_commander", "target": "t_register_ext_component_reply", "color": "#d8415b", "style": "dashed"}, {"source": "m_commander", "target": "t_failsafe_flags", "color": "#43d841", "style": "dashed"}, {"source": "m_commander", "target": "t_event", "color": "#d84182", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_test", "color": "#d84175", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_status", "color": "#d8414e", "style": "dashed"}, {"source": "m_commander", "target": "t_tune_control", "color": "#d86841", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_servos", "color": "#d87b41", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_servos_trim", "color": "#d841ca", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_control_allocator_status", "color": "#a5d841", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_motors", "color": "#d3d841", "style": "dashed"}, {"source": "m_dataman", "target": "t_dataman_response", "color": "#d841b6", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_selector_status", "color": "#d84141", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_odometry", "color": "#7e41d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_sensor_selection", "color": "#d841c4", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status_flags", "color": "#41b4d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_attitude", "color": "#416cd8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status", "color": "#41d873", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_global_position", "color": "#41d8ae", "style": "dashed"}, {"source": "m_ekf2", "target": "t_wind", "color": "#d8d741", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_local_position", "color": "#bfd841", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_sensor_bias", "color": "#6341d8", "style": "dashed"}, {"source": "m_send_event", "target": "t_tune_control", "color": "#d86841", "style": "dashed"}, {"source": "m_send_event", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_landing_gear", "color": "#56d841", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_trajectory_setpoint", "color": "#d86141", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_vehicle_constraints", "color": "#4941d8", "style": "dashed"}, {"source": "m_fw_att_control", "target": "t_vehicle_rates_setpoint", "color": "#d84741", "style": "dashed"}, {"source": "m_fw_att_control", "target": "t_landing_gear_wheel", "color": "#d341d8", "style": "dashed"}, {"source": "m_fw_autotune_attitude_control", "target": "t_autotune_attitude_control_status", "color": "#7141d8", "style": "dashed"}, {"source": "m_fw_lat_lon_control", "target": "t_flight_phase_estimation", "color": "#d841d1", "style": "dashed"}, {"source": "m_fw_lat_lon_control", "target": "t_tecs_status", "color": "#41a1d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_flaps_setpoint", "color": "#41d879", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_spoilers_setpoint", "color": "#41d8ce", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_figure_eight_status", "color": "#41d85f", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_landing_gear", "color": "#56d841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_runway_control", "color": "#d89641", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_longitudinal_setpoint", "color": "#41d8b4", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_orbit_status", "color": "#41d86c", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_launch_detection_status", "color": "#b241d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_position_controller_landing_status", "color": "#41d845", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_vehicle_local_position_setpoint", "color": "#415fd8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_longitudinal_control_configuration", "color": "#d8416e", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_lateral_control_configuration", "color": "#d84196", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_lateral_setpoint", "color": "#d85441", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_flaps_setpoint", "color": "#41d879", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_spoilers_setpoint", "color": "#41d8ce", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#d84741", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_v1_command", "color": "#41d88d", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_controls", "color": "#7ed841", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_device_attitude_status", "color": "#77d841", "style": "dashed"}, {"source": "m_gimbal", "target": "t_mount_orientation", "color": "#71d841", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_manager_status", "color": "#d8b041", "style": "dashed"}, {"source": "m_gimbal", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_device_set_attitude", "color": "#d841a3", "style": "dashed"}, {"source": "m_gimbal", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_manager_information", "color": "#4159d8", "style": "dashed"}, {"source": "m_land_detector", "target": "t_vehicle_land_detected", "color": "#41a7d8", "style": "dashed"}, {"source": "m_landing_target_estimator", "target": "t_landing_target_pose", "color": "#9e41d8", "style": "dashed"}, {"source": "m_load_mon", "target": "t_cpuload", "color": "#41d8c1", "style": "dashed"}, {"source": "m_local_position_estimator", "target": "t_vehicle_odometry", "color": "#7e41d8", "style": "dashed"}, {"source": "m_local_position_estimator", "target": "t_vehicle_global_position", "color": "#41d8ae", "style": "dashed"}, {"source": "m_local_position_estimator", "target": "t_vehicle_local_position", "color": "#bfd841", "style": "dashed"}, {"source": "m_local_position_estimator", "target": "t_estimator_status", "color": "#41d873", "style": "dashed"}, {"source": "m_logger", "target": "t_logger_status", "color": "#8b41d8", "style": "dashed"}, {"source": "m_logger", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_logger", "target": "t_ulog_stream", "color": "#41d893", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_switches", "color": "#7741d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_landing_gear", "color": "#56d841", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_action_request", "color": "#b941d8", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_status", "color": "#d8414e", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_setpoint", "color": "#63d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_airspeed", "color": "#98d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_information", "color": "#91d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_rates_setpoint", "color": "#d84741", "style": "dashed"}, {"source": "m_mavlink", "target": "t_distance_sensor", "color": "#41d8c8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_offboard_control_mode", "color": "#84d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_visual_odometry", "color": "#9141d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_landing_target_pose", "color": "#9e41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_attitude_status", "color": "#77d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_baro", "color": "#ab41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gyro", "color": "#41ced8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gps_inject_data", "color": "#c641d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_fw_virtual_attitude_setpoint", "color": "#41c8d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_battery_status", "color": "#cc41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_key_value", "color": "#5dd841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_input_rc", "color": "#41aed8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_trajectory_setpoint", "color": "#d86141", "style": "dashed"}, {"source": "m_mavlink", "target": "t_telemetry_status", "color": "#419ad8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_optical_flow", "color": "#d841b0", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_array", "color": "#d841a9", "style": "dashed"}, {"source": "m_mavlink", "target": "t_tune_control", "color": "#d86841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_camera_status", "color": "#d86e41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_vect", "color": "#4186d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_mocap_odometry", "color": "#d8419c", "style": "dashed"}, {"source": "m_mavlink", "target": "t_ulog_stream_ack", "color": "#d88241", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude", "color": "#416cd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_differential_pressure", "color": "#d88941", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_value", "color": "#d84189", "style": "dashed"}, {"source": "m_mavlink", "target": "t_transponder_report", "color": "#d8417b", "style": "dashed"}, {"source": "m_mavlink", "target": "t_rc_parameter_map", "color": "#d8b641", "style": "dashed"}, {"source": "m_mavlink", "target": "t_event", "color": "#d84182", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mc_virtual_attitude_setpoint", "color": "#41d886", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mission", "color": "#4145d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gps", "color": "#41d89a", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_mag", "color": "#41d8a1", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_global_position", "color": "#41d8ae", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_accel", "color": "#d8d141", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_manager_set_manual_control", "color": "#ccd841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_irlock_report", "color": "#41d8bb", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gyro_fifo", "color": "#d84154", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_local_position", "color": "#bfd841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_dataman_request", "color": "#b9d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_manager_set_attitude", "color": "#9ed841", "style": "dashed"}, {"source": "m_mc_att_control", "target": "t_vehicle_rates_setpoint", "color": "#d84741", "style": "dashed"}, {"source": "m_mc_autotune_attitude_control", "target": "t_autotune_attitude_control_status", "color": "#7141d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_trajectory_setpoint", "color": "#d86141", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_constraints", "color": "#4941d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_takeoff_status", "color": "#49d841", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_local_position_setpoint", "color": "#415fd8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_actuator_controls_status_0", "color": "#41d880", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#d84741", "style": "dashed"}, {"source": "m_navigator", "target": "t_home_position", "color": "#d841d7", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_status", "color": "#d841bd", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_land_detected", "color": "#41a7d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_time_estimate", "color": "#41d84b", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_result", "color": "#4179d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission_result", "color": "#41d859", "style": "dashed"}, {"source": "m_navigator", "target": "t_transponder_report", "color": "#d8417b", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission", "color": "#4145d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_navigator_status", "color": "#4341d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_global_position", "color": "#41d8ae", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_status", "color": "#5041d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_roi", "color": "#d84147", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_status", "color": "#d8414e", "style": "dashed"}, {"source": "m_navigator", "target": "t_dataman_request", "color": "#b9d841", "style": "dashed"}, {"source": "m_navigator", "target": "t_position_setpoint_triplet", "color": "#5d41d8", "style": "dashed"}, {"source": "m_payload_deliverer", "target": "t_gripper", "color": "#d8c441", "style": "dashed"}, {"source": "m_payload_deliverer", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_payload_deliverer", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_rc_update", "target": "t_manual_control_switches", "color": "#7741d8", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_actuator_servos", "color": "#d87b41", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_position_controller_status", "color": "#b2d841", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_velocity_setpoint", "color": "#d8418f", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_rate_setpoint", "color": "#6a41d8", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_attitude_setpoint", "color": "#bf41d8", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_position_setpoint", "color": "#d89c41", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_throttle_setpoint", "color": "#a541d8", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_actuator_motors", "color": "#d3d841", "style": "dashed"}, {"source": "m_rover_<PERSON><PERSON>mann", "target": "t_rover_steering_setpoint", "color": "#5641d8", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_velocity_setpoint", "color": "#d8418f", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_attitude_setpoint", "color": "#bf41d8", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_position_setpoint", "color": "#d89c41", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_throttle_setpoint", "color": "#a541d8", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_actuator_motors", "color": "#d3d841", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_steering_setpoint", "color": "#5641d8", "style": "dashed"}, {"source": "m_rover_differential", "target": "t_rover_rate_setpoint", "color": "#6a41d8", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_velocity_setpoint", "color": "#d8418f", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_position_setpoint", "color": "#d89c41", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_throttle_setpoint", "color": "#a541d8", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_actuator_motors", "color": "#d3d841", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_steering_setpoint", "color": "#5641d8", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_attitude_setpoint", "color": "#bf41d8", "style": "dashed"}, {"source": "m_rover_mecanum", "target": "t_rover_rate_setpoint", "color": "#6a41d8", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_position_controller_status", "color": "#b2d841", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_thrust_setpoint", "color": "#41d852", "style": "dashed"}, {"source": "m_rover_pos_control", "target": "t_vehicle_torque_setpoint", "color": "#4180d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensors_status_imu", "color": "#4152d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_airspeed", "color": "#98d841", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_selection", "color": "#d841c4", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu", "color": "#d85b41", "style": "dashed"}, {"source": "m_sensors", "target": "t_differential_pressure", "color": "#d88941", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_combined", "color": "#41d5d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu_status", "color": "#d8bd41", "style": "dashed"}, {"source": "m_battery_simulator", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_battery_simulator", "target": "t_battery_status", "color": "#cc41d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_gimbal_device_information", "color": "#91d841", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_distance_sensor", "color": "#41d8c8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_visual_odometry", "color": "#9141d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_gimbal_device_attitude_status", "color": "#77d841", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_baro", "color": "#ab41d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_gyro", "color": "#41ced8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_attitude_groundtruth", "color": "#6ad841", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_armed", "color": "#41c1d8", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_optical_flow", "color": "#d841b0", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_outputs", "color": "#d87541", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_servos", "color": "#d87b41", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_differential_pressure", "color": "#d88941", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_esc_status", "color": "#d88f41", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_angular_velocity_groundtruth", "color": "#d8a941", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_test", "color": "#d84175", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_gps", "color": "#41d89a", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_mag", "color": "#41d8a1", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_sensor_accel", "color": "#d8d141", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_local_position_groundtruth", "color": "#d84161", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_actuator_motors", "color": "#d3d841", "style": "dashed"}, {"source": "m_gz_bridge", "target": "t_vehicle_global_position_groundtruth", "color": "#abd841", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_servos", "color": "#d87b41", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_armed", "color": "#41c1d8", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_outputs_sim", "color": "#4166d8", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_motors", "color": "#d3d841", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_test", "color": "#d84175", "style": "dashed"}, {"source": "m_pwm_out_sim", "target": "t_actuator_outputs", "color": "#d87541", "style": "dashed"}, {"source": "m_sensor_agp_sim", "target": "t_aux_global_position", "color": "#414bd8", "style": "dashed"}, {"source": "m_sensor_airspeed_sim", "target": "t_differential_pressure", "color": "#d88941", "style": "dashed"}, {"source": "m_sensor_baro_sim", "target": "t_sensor_baro", "color": "#ab41d8", "style": "dashed"}, {"source": "m_sensor_gps_sim", "target": "t_sensor_gps", "color": "#41d89a", "style": "dashed"}, {"source": "m_sensor_mag_sim", "target": "t_sensor_mag", "color": "#41d8a1", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_visual_odometry", "color": "#9141d8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_rpm", "color": "#9841d8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_baro", "color": "#ab41d8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_gyro", "color": "#41ced8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_attitude_groundtruth", "color": "#6ad841", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_input_rc", "color": "#41aed8", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_optical_flow", "color": "#d841b0", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_mocap_odometry", "color": "#d8419c", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_differential_pressure", "color": "#d88941", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_esc_status", "color": "#d88f41", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_angular_velocity_groundtruth", "color": "#d8a941", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_mag", "color": "#41d8a1", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_accel", "color": "#d8d141", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_local_position_groundtruth", "color": "#d84161", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_irlock_report", "color": "#41d8bb", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_sensor_gyro_fifo", "color": "#d84154", "style": "dashed"}, {"source": "m_simulator_mavlink", "target": "t_vehicle_global_position_groundtruth", "color": "#abd841", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_airspeed", "color": "#98d841", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_distance_sensor", "color": "#41d8c8", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_sensor_accel", "color": "#d8d141", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_local_position_groundtruth", "color": "#d84161", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_angular_velocity_groundtruth", "color": "#d8a941", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_sensor_gyro_fifo", "color": "#d84154", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_sensor_gyro", "color": "#41ced8", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_attitude_groundtruth", "color": "#6ad841", "style": "dashed"}, {"source": "m_simulator_sih", "target": "t_vehicle_global_position_groundtruth", "color": "#abd841", "style": "dashed"}, {"source": "m_system_power_simulator", "target": "t_system_power", "color": "#41d8a7", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_sensor_correction", "color": "#418dd8", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_uuv_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#41d852", "style": "dashed"}, {"source": "m_uuv_att_control", "target": "t_vehicle_torque_setpoint", "color": "#4180d8", "style": "dashed"}, {"source": "m_uuv_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_uxrce_dds_client", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_flaps_setpoint", "color": "#41d879", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#41d852", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_spoilers_setpoint", "color": "#41d8ce", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_tiltrotor_extra_controls", "color": "#d84e41", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vtol_vehicle_status", "color": "#8bd841", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_attitude_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_command_ack", "color": "#d84168", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_torque_setpoint", "color": "#4180d8", "style": "dashed"}, {"source": "m_actuator_test", "target": "t_actuator_test", "color": "#d84175", "style": "dashed"}, {"source": "m_failure", "target": "t_vehicle_command", "color": "#4193d8", "style": "dashed"}, {"source": "m_tests", "target": "t_dataman_request", "color": "#b9d841", "style": "dashed"}, {"source": "m_tune_control", "target": "t_tune_control", "color": "#d86841", "style": "dashed"}, {"source": "m_fake_gps", "target": "t_sensor_gps", "color": "#41d89a", "style": "dashed"}, {"source": "m_fake_imu", "target": "t_esc_status", "color": "#d88f41", "style": "dashed"}, {"source": "m_fake_imu", "target": "t_sensor_accel", "color": "#d8d141", "style": "dashed"}, {"source": "m_fake_imu", "target": "t_sensor_gyro_fifo", "color": "#d84154", "style": "dashed"}, {"source": "m_fake_imu", "target": "t_sensor_gyro", "color": "#41ced8", "style": "dashed"}, {"source": "m_fake_magnetometer", "target": "t_sensor_mag", "color": "#41d8a1", "style": "dashed"}, {"source": "m_px4_mavlink_debug", "target": "t_debug_key_value", "color": "#5dd841", "style": "dashed"}, {"source": "m_px4_mavlink_debug", "target": "t_debug_vect", "color": "#4186d8", "style": "dashed"}, {"source": "m_px4_mavlink_debug", "target": "t_debug_value", "color": "#d84189", "style": "dashed"}, {"source": "m_px4_mavlink_debug", "target": "t_debug_array", "color": "#d841a9", "style": "dashed"}, {"source": "m_px4_simple_app", "target": "t_vehicle_attitude", "color": "#416cd8", "style": "dashed"}, {"source": "t_vehicle_local_position", "target": "m_camera_trigger", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_camera_trigger", "color": "#4193d8", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_gps", "color": "#c641d8", "style": "normal"}, {"source": "t_home_position", "target": "m_msp_osd", "color": "#d841d7", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_msp_osd", "color": "#416cd8", "style": "normal"}, {"source": "t_input_rc", "target": "m_msp_osd", "color": "#41aed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_msp_osd", "color": "#41d8ae", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_msp_osd", "color": "#41d866", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_msp_osd", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_msp_osd", "color": "#d8414e", "style": "normal"}, {"source": "t_battery_status", "target": "m_msp_osd", "color": "#cc41d8", "style": "normal"}, {"source": "t_tune_control", "target": "m_tone_alarm", "color": "#d86841", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_airship_att_control", "color": "#63d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_airship_att_control", "color": "#d8414e", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_airspeed_selector", "color": "#d84141", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_airspeed_selector", "color": "#41d852", "style": "normal"}, {"source": "t_airspeed", "target": "m_airspeed_selector", "color": "#98d841", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_airspeed_selector", "color": "#d841d1", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_airspeed_selector", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_airspeed_selector", "color": "#41a7d8", "style": "normal"}, {"source": "t_tecs_status", "target": "m_airspeed_selector", "color": "#41a1d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_airspeed_selector", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_airspeed_selector", "color": "#d8414e", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_airspeed_selector", "color": "#b241d8", "style": "normal"}, {"source": "t_estimator_status", "target": "m_airspeed_selector", "color": "#41d873", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_attitude_estimator_q", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_attitude_estimator_q", "color": "#9141d8", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_attitude_estimator_q", "color": "#41d5d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_attitude_estimator_q", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_mocap_odometry", "target": "m_attitude_estimator_q", "color": "#d8419c", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_camera_feedback", "color": "#77d841", "style": "normal"}, {"source": "t_camera_trigger", "target": "m_camera_feedback", "color": "#c6d841", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_camera_feedback", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_camera_feedback", "color": "#416cd8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_commander", "color": "#7741d8", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_commander", "color": "#d84141", "style": "normal"}, {"source": "t_vtol_vehicle_status", "target": "m_commander", "color": "#8bd841", "style": "normal"}, {"source": "t_logger_status", "target": "m_commander", "color": "#8b41d8", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_commander", "color": "#84d841", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_commander", "color": "#41d8c8", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_commander", "color": "#ab41d8", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_commander", "color": "#41ced8", "style": "normal"}, {"source": "t_action_request", "target": "m_commander", "color": "#b941d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_commander", "color": "#63d841", "style": "normal"}, {"source": "t_battery_status", "target": "m_commander", "color": "#cc41d8", "style": "normal"}, {"source": "t_home_position", "target": "m_commander", "color": "#d841d7", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_commander", "color": "#41c1d8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_commander", "color": "#d841c4", "style": "normal"}, {"source": "t_estimator_status_flags", "target": "m_commander", "color": "#41b4d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_commander", "color": "#41a7d8", "style": "normal"}, {"source": "t_telemetry_status", "target": "m_commander", "color": "#419ad8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_commander", "color": "#4193d8", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_commander", "color": "#41d84b", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_commander", "color": "#418dd8", "style": "normal"}, {"source": "t_geofence_result", "target": "m_commander", "color": "#4179d8", "style": "normal"}, {"source": "t_mission_result", "target": "m_commander", "color": "#41d859", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_commander", "color": "#416cd8", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_commander", "color": "#d88941", "style": "normal"}, {"source": "t_esc_status", "target": "m_commander", "color": "#d88f41", "style": "normal"}, {"source": "t_event", "target": "m_commander", "color": "#d84182", "style": "normal"}, {"source": "t_power_button_state", "target": "m_commander", "color": "#d8a341", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_commander", "color": "#41d866", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_commander", "color": "#d8bd41", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_commander", "color": "#d84168", "style": "normal"}, {"source": "t_estimator_status", "target": "m_commander", "color": "#41d873", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_commander", "color": "#4152d8", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_commander", "color": "#41d89a", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_commander", "color": "#41d8a1", "style": "normal"}, {"source": "t_system_power", "target": "m_commander", "color": "#41d8a7", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_commander", "color": "#41d8ae", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_commander", "color": "#d8d141", "style": "normal"}, {"source": "t_navigator_status", "target": "m_commander", "color": "#4341d8", "style": "normal"}, {"source": "t_wind", "target": "m_commander", "color": "#d8d741", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_commander", "color": "#d3d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_commander", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_commander", "color": "#bfd841", "style": "normal"}, {"source": "t_cpuload", "target": "m_commander", "color": "#41d8c1", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_commander", "color": "#6341d8", "style": "normal"}, {"source": "t_flaps_setpoint", "target": "m_control_allocator", "color": "#41d879", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_control_allocator", "color": "#7741d8", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_control_allocator", "color": "#41d852", "style": "normal"}, {"source": "t_spoilers_setpoint", "target": "m_control_allocator", "color": "#41d8ce", "style": "normal"}, {"source": "t_tiltrotor_extra_controls", "target": "m_control_allocator", "color": "#d84e41", "style": "normal"}, {"source": "t_rpm", "target": "m_control_allocator", "color": "#9841d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_control_allocator", "color": "#41d8d5", "style": "normal"}, {"source": "t_failure_detector_status", "target": "m_control_allocator", "color": "#50d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_control_allocator", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_torque_setpoint", "target": "m_control_allocator", "color": "#4180d8", "style": "normal"}, {"source": "t_dataman_request", "target": "m_dataman", "color": "#b9d841", "style": "normal"}, {"source": "t_aux_global_position", "target": "m_ekf2", "color": "#414bd8", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_ekf2", "color": "#4152d8", "style": "normal"}, {"source": "t_airspeed", "target": "m_ekf2", "color": "#98d841", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_ekf2", "color": "#d85b41", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_ekf2", "color": "#d841c4", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_ekf2", "color": "#41d8c8", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_ekf2", "color": "#9141d8", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_ekf2", "color": "#9e41d8", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_ekf2", "color": "#41d5d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_ekf2", "color": "#41d866", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_ekf2", "color": "#41a7d8", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_ekf2", "color": "#b241d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ekf2", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_ekf2", "color": "#4193d8", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_send_event", "color": "#43d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_send_event", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_send_event", "color": "#4193d8", "style": "normal"}, {"source": "t_cpuload", "target": "m_send_event", "color": "#41d8c1", "style": "normal"}, {"source": "t_battery_status", "target": "m_send_event", "color": "#cc41d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_flight_mode_manager", "color": "#41d8d5", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_flight_mode_manager", "color": "#49d841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_flight_mode_manager", "color": "#41a7d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_flight_mode_manager", "color": "#4193d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_flight_mode_manager", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_flight_mode_manager", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_flight_mode_manager", "color": "#d8ca41", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_att_control", "color": "#416cd8", "style": "normal"}, {"source": "t_fixed_wing_runway_control", "target": "m_fw_att_control", "color": "#d89641", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_att_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_att_control", "color": "#41a7d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_att_control", "color": "#41d866", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_att_control", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_att_control", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_fw_att_control", "color": "#d8ca41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_att_control", "color": "#63d841", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_fw_att_control", "color": "#7141d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_autotune_attitude_control", "color": "#63d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_autotune_attitude_control", "color": "#d8414e", "style": "normal"}, {"source": "t_flaps_setpoint", "target": "m_fw_lat_lon_control", "color": "#41d879", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_lat_lon_control", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_lat_lon_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_wind", "target": "m_fw_lat_lon_control", "color": "#d8d741", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_lat_lon_control", "color": "#41d866", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_lat_lon_control", "color": "#41a7d8", "style": "normal"}, {"source": "t_fixed_wing_longitudinal_setpoint", "target": "m_fw_lat_lon_control", "color": "#41d8b4", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_lat_lon_control", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_lat_lon_control", "color": "#d8414e", "style": "normal"}, {"source": "t_longitudinal_control_configuration", "target": "m_fw_lat_lon_control", "color": "#d8416e", "style": "normal"}, {"source": "t_lateral_control_configuration", "target": "m_fw_lat_lon_control", "color": "#d84196", "style": "normal"}, {"source": "t_fixed_wing_lateral_setpoint", "target": "m_fw_lat_lon_control", "color": "#d85441", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_mode_manager", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_fw_mode_manager", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_mode_manager", "color": "#41d8d5", "style": "normal"}, {"source": "t_wind", "target": "m_fw_mode_manager", "color": "#d8d741", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_mode_manager", "color": "#41d866", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_fw_mode_manager", "color": "#d86141", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_mode_manager", "color": "#41a7d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_mode_manager", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_fw_mode_manager", "color": "#4193d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_fw_mode_manager", "color": "#5d41d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_mode_manager", "color": "#d8414e", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_mode_manager", "color": "#63d841", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_fw_rate_control", "color": "#d84741", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_rate_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_rate_control", "color": "#41a7d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_rate_control", "color": "#41d866", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_rate_control", "color": "#d8414e", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_rate_control", "color": "#63d841", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_fw_rate_control", "color": "#a5d841", "style": "normal"}, {"source": "t_battery_status", "target": "m_fw_rate_control", "color": "#cc41d8", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_gimbal", "color": "#91d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_gimbal", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_gimbal", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_gimbal", "color": "#41a7d8", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_gimbal", "color": "#77d841", "style": "normal"}, {"source": "t_gimbal_manager_set_manual_control", "target": "m_gimbal", "color": "#ccd841", "style": "normal"}, {"source": "t_vehicle_roi", "target": "m_gimbal", "color": "#d84147", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_gimbal", "color": "#4193d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_gimbal", "color": "#5d41d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_gimbal", "color": "#63d841", "style": "normal"}, {"source": "t_gimbal_manager_set_attitude", "target": "m_gimbal", "color": "#9ed841", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_gyro_calibration", "color": "#418dd8", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_gyro_calibration", "color": "#d8d141", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_gyro_calibration", "color": "#d8414e", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_gyro_calibration", "color": "#41ced8", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_gyro_fft", "color": "#d8bd41", "style": "normal"}, {"source": "t_sensor_gyro_fifo", "target": "m_gyro_fft", "color": "#d84154", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_gyro_fft", "color": "#41ced8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_gyro_fft", "color": "#d841c4", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_land_detector", "color": "#41c1d8", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_land_detector", "color": "#41d852", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_land_detector", "color": "#d841c4", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_land_detector", "color": "#d86141", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_land_detector", "color": "#41d8d5", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_land_detector", "color": "#49d841", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_land_detector", "color": "#41d866", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_land_detector", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_land_detector", "color": "#d8bd41", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_land_detector", "color": "#b241d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_land_detector", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_land_detector", "color": "#bfd841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_land_detector", "color": "#5d41d8", "style": "normal"}, {"source": "t_irlock_report", "target": "m_landing_target_estimator", "color": "#41d8bb", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_landing_target_estimator", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_landing_target_estimator", "color": "#416cd8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_local_position_estimator", "color": "#41c1d8", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_local_position_estimator", "color": "#41d8c8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_local_position_estimator", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_local_position_estimator", "color": "#9141d8", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_local_position_estimator", "color": "#9e41d8", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_local_position_estimator", "color": "#41d5d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_local_position_estimator", "color": "#41a7d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_local_position_estimator", "color": "#4193d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_local_position_estimator", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_mocap_odometry", "target": "m_local_position_estimator", "color": "#d8419c", "style": "normal"}, {"source": "t_ulog_stream_ack", "target": "m_logger", "color": "#d88241", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_logger", "color": "#4193d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_logger", "color": "#d8414e", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_logger", "color": "#63d841", "style": "normal"}, {"source": "t_battery_status", "target": "m_logger", "color": "#cc41d8", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mag_bias_estimator", "color": "#41d8a1", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mag_bias_estimator", "color": "#d8414e", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_manual_control", "color": "#7741d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_manual_control", "color": "#63d841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_manual_control", "color": "#d8414e", "style": "normal"}, {"source": "t_action_request", "target": "m_manual_control", "color": "#b941d8", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_mavlink", "color": "#d84141", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mavlink", "color": "#d84741", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_mavlink", "color": "#d85b41", "style": "normal"}, {"source": "t_camera_status", "target": "m_mavlink", "color": "#d86e41", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_mavlink", "color": "#d87541", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_mavlink", "color": "#d88941", "style": "normal"}, {"source": "t_esc_status", "target": "m_mavlink", "color": "#d88f41", "style": "normal"}, {"source": "t_vehicle_angular_velocity_groundtruth", "target": "m_mavlink", "color": "#d8a941", "style": "normal"}, {"source": "t_gimbal_manager_status", "target": "m_mavlink", "color": "#d8b041", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_mavlink", "color": "#d8bd41", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mavlink", "color": "#d8ca41", "style": "normal"}, {"source": "t_wind", "target": "m_mavlink", "color": "#d8d741", "style": "normal"}, {"source": "t_camera_trigger", "target": "m_mavlink", "color": "#c6d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mavlink", "color": "#bfd841", "style": "normal"}, {"source": "t_position_controller_status", "target": "m_mavlink", "color": "#b2d841", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_mavlink", "color": "#abd841", "style": "normal"}, {"source": "t_airspeed", "target": "m_mavlink", "color": "#98d841", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_mavlink", "color": "#91d841", "style": "normal"}, {"source": "t_mount_orientation", "target": "m_mavlink", "color": "#71d841", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_mavlink", "color": "#77d841", "style": "normal"}, {"source": "t_vehicle_attitude_groundtruth", "target": "m_mavlink", "color": "#6ad841", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mavlink", "color": "#63d841", "style": "normal"}, {"source": "t_debug_key_value", "target": "m_mavlink", "color": "#5dd841", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_mavlink", "color": "#43d841", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_mavlink", "color": "#41d84b", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_mavlink", "color": "#41d852", "style": "normal"}, {"source": "t_mission_result", "target": "m_mavlink", "color": "#41d859", "style": "normal"}, {"source": "t_figure_eight_status", "target": "m_mavlink", "color": "#41d85f", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_mavlink", "color": "#41d866", "style": "normal"}, {"source": "t_orbit_status", "target": "m_mavlink", "color": "#41d86c", "style": "normal"}, {"source": "t_estimator_status", "target": "m_mavlink", "color": "#41d873", "style": "normal"}, {"source": "t_gimbal_v1_command", "target": "m_mavlink", "color": "#41d88d", "style": "normal"}, {"source": "t_ulog_stream", "target": "m_mavlink", "color": "#41d893", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mavlink", "color": "#41d8a1", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_mavlink", "color": "#41d89a", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_mavlink", "color": "#41d8ae", "style": "normal"}, {"source": "t_cpuload", "target": "m_mavlink", "color": "#41d8c1", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_mavlink", "color": "#41d8c8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mavlink", "color": "#41d8d5", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_mavlink", "color": "#41c1d8", "style": "normal"}, {"source": "t_camera_capture", "target": "m_mavlink", "color": "#41bbd8", "style": "normal"}, {"source": "t_input_rc", "target": "m_mavlink", "color": "#41aed8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mavlink", "color": "#41a7d8", "style": "normal"}, {"source": "t_tecs_status", "target": "m_mavlink", "color": "#41a1d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_mavlink", "color": "#4193d8", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_mavlink", "color": "#418dd8", "style": "normal"}, {"source": "t_debug_vect", "target": "m_mavlink", "color": "#4186d8", "style": "normal"}, {"source": "t_geofence_result", "target": "m_mavlink", "color": "#4179d8", "style": "normal"}, {"source": "t_satellite_info", "target": "m_mavlink", "color": "#4173d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mavlink", "color": "#416cd8", "style": "normal"}, {"source": "t_actuator_outputs_sim", "target": "m_mavlink", "color": "#4166d8", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_mavlink", "color": "#415fd8", "style": "normal"}, {"source": "t_gimbal_manager_information", "target": "m_mavlink", "color": "#4159d8", "style": "normal"}, {"source": "t_mission", "target": "m_mavlink", "color": "#4145d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_mavlink", "color": "#5d41d8", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_mavlink", "color": "#6341d8", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_mavlink", "color": "#7141d8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_mavlink", "color": "#7741d8", "style": "normal"}, {"source": "t_vehicle_odometry", "target": "m_mavlink", "color": "#7e41d8", "style": "normal"}, {"source": "t_health_report", "target": "m_mavlink", "color": "#8441d8", "style": "normal"}, {"source": "t_rpm", "target": "m_mavlink", "color": "#9841d8", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_mavlink", "color": "#9e41d8", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_mavlink", "color": "#ab41d8", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_mavlink", "color": "#c641d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_mavlink", "color": "#cc41d8", "style": "normal"}, {"source": "t_home_position", "target": "m_mavlink", "color": "#d841d7", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_mavlink", "color": "#d841c4", "style": "normal"}, {"source": "t_dataman_response", "target": "m_mavlink", "color": "#d841b6", "style": "normal"}, {"source": "t_debug_array", "target": "m_mavlink", "color": "#d841a9", "style": "normal"}, {"source": "t_gimbal_device_set_attitude", "target": "m_mavlink", "color": "#d841a3", "style": "normal"}, {"source": "t_debug_value", "target": "m_mavlink", "color": "#d84189", "style": "normal"}, {"source": "t_event", "target": "m_mavlink", "color": "#d84182", "style": "normal"}, {"source": "t_transponder_report", "target": "m_mavlink", "color": "#d8417b", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_mavlink", "color": "#d84168", "style": "normal"}, {"source": "t_vehicle_local_position_groundtruth", "target": "m_mavlink", "color": "#d84161", "style": "normal"}, {"source": "t_register_ext_component_reply", "target": "m_mavlink", "color": "#d8415b", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mavlink", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mc_att_control", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_att_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_att_control", "color": "#41a7d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_att_control", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_att_control", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mc_att_control", "color": "#d8ca41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_att_control", "color": "#63d841", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_mc_att_control", "color": "#7141d8", "style": "normal"}, {"source": "t_actuator_controls_status_0", "target": "m_mc_autotune_attitude_control", "color": "#41d880", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_autotune_attitude_control", "color": "#d8414e", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_autotune_attitude_control", "color": "#63d841", "style": "normal"}, {"source": "t_vehicle_torque_setpoint", "target": "m_mc_autotune_attitude_control", "color": "#4180d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_mc_pos_control", "color": "#d86141", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_pos_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_vehicle_constraints", "target": "m_mc_pos_control", "color": "#4941d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_pos_control", "color": "#41a7d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_pos_control", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mc_rate_control", "color": "#d84741", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_rate_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_rate_control", "color": "#41a7d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_rate_control", "color": "#d8414e", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_rate_control", "color": "#63d841", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_mc_rate_control", "color": "#a5d841", "style": "normal"}, {"source": "t_battery_status", "target": "m_mc_rate_control", "color": "#cc41d8", "style": "normal"}, {"source": "t_home_position", "target": "m_navigator", "color": "#d841d7", "style": "normal"}, {"source": "t_mission", "target": "m_navigator", "color": "#4145d8", "style": "normal"}, {"source": "t_geofence_status", "target": "m_navigator", "color": "#d841bd", "style": "normal"}, {"source": "t_dataman_response", "target": "m_navigator", "color": "#d841b6", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_navigator", "color": "#41d8ae", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_navigator", "color": "#9e41d8", "style": "normal"}, {"source": "t_wind", "target": "m_navigator", "color": "#d8d741", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_navigator", "color": "#41a7d8", "style": "normal"}, {"source": "t_transponder_report", "target": "m_navigator", "color": "#d8417b", "style": "normal"}, {"source": "t_rtl_status", "target": "m_navigator", "color": "#5041d8", "style": "normal"}, {"source": "t_position_controller_landing_status", "target": "m_navigator", "color": "#41d845", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_navigator", "color": "#d8414e", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_navigator", "color": "#4193d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_navigator", "color": "#bfd841", "style": "normal"}, {"source": "t_position_controller_status", "target": "m_navigator", "color": "#b2d841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_payload_deliverer", "color": "#4193d8", "style": "normal"}, {"source": "t_rc_parameter_map", "target": "m_rc_update", "color": "#d8b641", "style": "normal"}, {"source": "t_input_rc", "target": "m_rc_update", "color": "#41aed8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_rc_update", "color": "#7741d8", "style": "normal"}, {"source": "t_actuator_servos", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d87b41", "style": "normal"}, {"source": "t_rover_velocity_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d8418f", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_rover_<PERSON><PERSON>mann", "color": "#84d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_<PERSON><PERSON>mann", "color": "#416cd8", "style": "normal"}, {"source": "t_rover_attitude_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#bf41d8", "style": "normal"}, {"source": "t_rover_position_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d89c41", "style": "normal"}, {"source": "t_rover_throttle_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#a541d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_<PERSON><PERSON>mann", "color": "#41d8d5", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d86141", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_rover_<PERSON><PERSON>mann", "color": "#d3d841", "style": "normal"}, {"source": "t_rover_steering_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#5641d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_<PERSON><PERSON>mann", "color": "#bfd841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_<PERSON><PERSON>mann", "color": "#5d41d8", "style": "normal"}, {"source": "t_position_controller_status", "target": "m_rover_<PERSON><PERSON>mann", "color": "#b2d841", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#63d841", "style": "normal"}, {"source": "t_rover_rate_setpoint", "target": "m_rover_<PERSON><PERSON>mann", "color": "#6a41d8", "style": "normal"}, {"source": "t_rover_velocity_setpoint", "target": "m_rover_differential", "color": "#d8418f", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_rover_differential", "color": "#84d841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_differential", "color": "#416cd8", "style": "normal"}, {"source": "t_rover_position_setpoint", "target": "m_rover_differential", "color": "#d89c41", "style": "normal"}, {"source": "t_rover_throttle_setpoint", "target": "m_rover_differential", "color": "#a541d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_differential", "color": "#41d8d5", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_differential", "color": "#d86141", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_rover_differential", "color": "#d3d841", "style": "normal"}, {"source": "t_rover_steering_setpoint", "target": "m_rover_differential", "color": "#5641d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_differential", "color": "#bfd841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_differential", "color": "#5d41d8", "style": "normal"}, {"source": "t_rover_attitude_setpoint", "target": "m_rover_differential", "color": "#bf41d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_differential", "color": "#63d841", "style": "normal"}, {"source": "t_rover_rate_setpoint", "target": "m_rover_differential", "color": "#6a41d8", "style": "normal"}, {"source": "t_rover_velocity_setpoint", "target": "m_rover_mecanum", "color": "#d8418f", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_mecanum", "color": "#416cd8", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_rover_mecanum", "color": "#84d841", "style": "normal"}, {"source": "t_rover_position_setpoint", "target": "m_rover_mecanum", "color": "#d89c41", "style": "normal"}, {"source": "t_rover_throttle_setpoint", "target": "m_rover_mecanum", "color": "#a541d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_mecanum", "color": "#41d8d5", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_mecanum", "color": "#d86141", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_rover_mecanum", "color": "#d3d841", "style": "normal"}, {"source": "t_rover_steering_setpoint", "target": "m_rover_mecanum", "color": "#5641d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_mecanum", "color": "#bfd841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_mecanum", "color": "#5d41d8", "style": "normal"}, {"source": "t_rover_attitude_setpoint", "target": "m_rover_mecanum", "color": "#bf41d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_mecanum", "color": "#63d841", "style": "normal"}, {"source": "t_rover_rate_setpoint", "target": "m_rover_mecanum", "color": "#6a41d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rover_pos_control", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_rover_pos_control", "color": "#41d8ae", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_rover_pos_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_rover_pos_control", "color": "#d86141", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_rover_pos_control", "color": "#bfd841", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_rover_pos_control", "color": "#5d41d8", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_rover_pos_control", "color": "#d8ca41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_rover_pos_control", "color": "#63d841", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_sensors", "color": "#d841c4", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_sensors", "color": "#d85b41", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_sensors", "color": "#41d8a1", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_sensors", "color": "#d88941", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_sensors", "color": "#d8d141", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_sensors", "color": "#41d8d5", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_sensors", "color": "#d8bd41", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_sensors", "color": "#41ced8", "style": "normal"}, {"source": "t_sensor_optical_flow", "target": "m_sensors", "color": "#d841b0", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_sensors", "color": "#418dd8", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_sensors", "color": "#6341d8", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_battery_simulator", "color": "#d841d1", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_battery_simulator", "color": "#4193d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_battery_simulator", "color": "#d8414e", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_gz_bridge", "color": "#41c1d8", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_gz_bridge", "color": "#d841ca", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_gz_bridge", "color": "#7ed841", "style": "normal"}, {"source": "t_landing_gear", "target": "m_gz_bridge", "color": "#56d841", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_gz_bridge", "color": "#d3d841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_gz_bridge", "color": "#4193d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_gz_bridge", "color": "#d84175", "style": "normal"}, {"source": "t_gripper", "target": "m_gz_bridge", "color": "#d8c441", "style": "normal"}, {"source": "t_gimbal_device_set_attitude", "target": "m_gz_bridge", "color": "#d841a3", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_gz_bridge", "color": "#63d841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_gz_bridge", "color": "#d341d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pwm_out_sim", "color": "#41c1d8", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pwm_out_sim", "color": "#d841ca", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_pwm_out_sim", "color": "#7ed841", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pwm_out_sim", "color": "#56d841", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pwm_out_sim", "color": "#d3d841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pwm_out_sim", "color": "#4193d8", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pwm_out_sim", "color": "#d84175", "style": "normal"}, {"source": "t_gripper", "target": "m_pwm_out_sim", "color": "#d8c441", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pwm_out_sim", "color": "#63d841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_pwm_out_sim", "color": "#d341d8", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_agp_sim", "color": "#abd841", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_airspeed_sim", "color": "#abd841", "style": "normal"}, {"source": "t_vehicle_local_position_groundtruth", "target": "m_sensor_airspeed_sim", "color": "#d84161", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_sensor_airspeed_sim", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_baro_sim", "color": "#abd841", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_gps_sim", "color": "#abd841", "style": "normal"}, {"source": "t_vehicle_local_position_groundtruth", "target": "m_sensor_gps_sim", "color": "#d84161", "style": "normal"}, {"source": "t_vehicle_global_position_groundtruth", "target": "m_sensor_mag_sim", "color": "#abd841", "style": "normal"}, {"source": "t_vehicle_attitude_groundtruth", "target": "m_sensor_mag_sim", "color": "#6ad841", "style": "normal"}, {"source": "t_actuator_outputs_sim", "target": "m_simulator_mavlink", "color": "#4166d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_simulator_mavlink", "color": "#cc41d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_simulator_mavlink", "color": "#4193d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_simulator_mavlink", "color": "#d8414e", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_simulator_mavlink", "color": "#d87541", "style": "normal"}, {"source": "t_actuator_outputs_sim", "target": "m_simulator_sih", "color": "#4166d8", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_simulator_sih", "color": "#d87541", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_temperature_compensation", "color": "#41d8a1", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_temperature_compensation", "color": "#d8d141", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_temperature_compensation", "color": "#ab41d8", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_temperature_compensation", "color": "#41ced8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_temperature_compensation", "color": "#4193d8", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_uuv_att_control", "color": "#d84741", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_uuv_att_control", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_uuv_att_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_uuv_att_control", "color": "#d8ca41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_uuv_att_control", "color": "#63d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_uuv_pos_control", "color": "#bfd841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_uuv_pos_control", "color": "#d86141", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_uuv_pos_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_uuv_pos_control", "color": "#416cd8", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_uxrce_dds_client", "color": "#d84168", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_vtol_att_control", "color": "#41d8d5", "style": "normal"}, {"source": "t_action_request", "target": "m_vtol_att_control", "color": "#b941d8", "style": "normal"}, {"source": "t_fw_virtual_attitude_setpoint", "target": "m_vtol_att_control", "color": "#41c8d8", "style": "normal"}, {"source": "t_home_position", "target": "m_vtol_att_control", "color": "#d841d7", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_vtol_att_control", "color": "#41a7d8", "style": "normal"}, {"source": "t_tecs_status", "target": "m_vtol_att_control", "color": "#41a1d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_vtol_att_control", "color": "#4193d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_vtol_att_control", "color": "#416cd8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_vtol_att_control", "color": "#41d866", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_vtol_att_control", "color": "#415fd8", "style": "normal"}, {"source": "t_mc_virtual_attitude_setpoint", "target": "m_vtol_att_control", "color": "#41d886", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_vtol_att_control", "color": "#bfd841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_vtol_att_control", "color": "#d8414e", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_vtol_att_control", "color": "#5d41d8", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_failure", "color": "#d84168", "style": "normal"}, {"source": "t_input_rc", "target": "m_tests", "color": "#41aed8", "style": "normal"}, {"source": "t_dataman_response", "target": "m_tests", "color": "#d841b6", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fake_magnetometer", "color": "#416cd8", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_work_item_example", "color": "#d8d141", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_work_item_example", "color": "#d8414e", "style": "normal"}]}