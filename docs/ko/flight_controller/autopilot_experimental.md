# Community Supported & Experimental Autopilots

:::tip
For more information about PX4 project autopilot board support levels see: [px4.io/autopilots/](https://px4.io/autopilots/).
:::

## 시험단계 자동비행장치

This category is for experimental autopilots (and autopilot "platforms") that are _not supported_ by either the PX4 project team or by a manufacturer.

- [BeagleBone Blue](../flight_controller/beaglebone_blue.md)
- [Raspberry Pi 2/3 Navio2](../flight_controller/raspberry_pi_navio2.md)
- [Raspberry Pi 2/3/4 PilotPi Shield](../flight_controller/raspberry_pi_pilotpi.md)

이 카테고리의 보드는 정의된 기체 유형에 대해 하나 이상의 PX4 버전에서 작동하여야합니다.
현재, PX4 릴리스와 호환되지 않을 수 있으며, 향후 릴리스를 위해 프로젝트에서 호환성을 유지하지 않습니다.

## 실험 기체

These are [complete vehicles](../complete_vehicles_mc/index.md) that have a fully integrated autopilot and other hardware (i.e. unlike the other autopilots listed, you can't use them in your own builds).
PX4 소프트웨어 관점에서 볼 때 다른 자동조종장치이기 때문에 이 페이지에 나열되어 있습니다.

- [Bitcraze Crazyflie 2.0](../complete_vehicles_mc/crazyflie2.md) ([Complete Vehicle](../complete_vehicles_mc/index.md))
- [Bitcraze Crazyflie 2.1](../complete_vehicles_mc/crazyflie21.md) ([Complete Vehicle](../complete_vehicles_mc/index.md))
