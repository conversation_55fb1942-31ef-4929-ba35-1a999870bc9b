# Compass Power Compensation

Компаси (магнітометри) повинні бути встановлені якнайдалі від кабелів, які переносять великі струми, оскільки вони створюють магнітні поля, які можуть вплинути на показання компасу.

Ця тема пояснює, як компенсувати виниклі магнітні поля у випадках, коли переміщення компасу нереалістичне.

:::tip
Переміщення компасу подалі від кабелів, що переносять потужність, є найпростішим і найефективнішим способом вирішення цієї проблеми, оскільки сила магнітних полів зменшується квадратично з відстані від кабелю.
:::

:::info
Процес продемонстрований для багатороторного вертольота, але є так само ефективним для інших типів транспортних засобів.
:::

<a id="when"></a>

## Коли застосовна компенсація потужності?

Виконання компенсації потужності рекомендується лише у випадку, якщо виконуються всі наступні умови:

1. Компас не може бути віддалений від кабелів, які переносять потужність.

2. Існує сильна кореляція між показаннями компасу та встановленим значенням тяги та/або поточним зарядом батареї.

   ![Corrupted mag](../../assets/advanced_config/corrupted_mag.png)

3. Кабелі дрона всі закріплені на місці / не рухаються (розраховані параметри компенсації будуть недійсними, якщо кабелі, які переносять струм, можуть рухатися).

<a id="how"></a>

## Як компенсувати компас

1. Переконайтеся, що ваш дрон працює на версії прошивки, що підтримує компенсацію потужності (поточний майстер або версії від v.1.11.0).

2. Виконайте [стандартну калібрування компасу](../config/compass.md#compass-calibration).

3. Встановіть параметр [SDLOG_MODE](../advanced_config/parameter_reference.md#SDLOG_MODE) на 2, щоб увімкнути журналювання даних після завантаження.

4. Встановіть прапорець параметра [SDLOG_PROFILE](../advanced_config/parameter_reference.md#SDLOG_PROFILE) для _Порівняння датчиків_ (біт 6), щоб отримати більше точок даних.

5. Закріпіть дрон таким чином, щоб він не міг рухатися, та прикріпіть пропелери (щоб двигуни могли витягувати такий самий струм, як у повітрі).
   У цьому прикладі транспортний засіб закріплюється за допомогою ременів.

   ![strap](../../assets/advanced_config/strap.png)

6. Увімкніть живлення транспортного засобу та переключіться у режим польоту [ACRO](../flight_modes_mc/acro.md) (використання цього режиму забезпечує, що транспортний засіб не намагатиметься компенсувати рух, що виникає від ременів).

   - Збройте транспортний засіб і повільно підніміть оберти двигунів до максимуму.
   - Повільно знизьте швидкість обертання двигунів до нуля.
   - Звільнити автомобіль

   Проведіть тест обережно і уважно моніторте вібрації.

:::

7. Отримайте ulog та скористайтеся Python-скриптом [mag_compensation.py](https://github.com/PX4/PX4-Autopilot/blob/main/src/modules/sensors/vehicle_magnetometer/mag_compensation/python/mag_compensation.py), щоб ідентифікувати параметри компенсації.

   ```sh
   python mag_compensation.py ~/path/to/log/logfile.ulg <type> [--instance <number>]
   ```

   де:

   - `<type>`: `current` or `thrust` (power signal used for compensation)
   - `--instance <number>` (optional): The number is `0` (default) or `1`, the instance of the current or thrust signal to use.

   Якщо ваш журнал не містить вимірювань поточного заряду батареї, вам потрібно закоментувати відповідні рядки у Python-скрипті, щоб він робив розрахунок тільки для тяги.

:::

8. Скрипт поверне ідентифікацію параметрів для тяги, а також для поточного і виведе їх у консоль.
   Цифри, які з'являються зі скрипту, показують "якість підгонки" для кожного екземпляру компасу, а також те, як виглядатиме дані після компенсації за запропонованими значеннями.
   Якщо є вимірювання струму, використання компенсації поточного звичайно дає кращі результати.
   Ось приклад журналу, де відповідність струму добра, але параметри тяги непридатні, оскільки відношення не є лінійним.

   ![line fit](../../assets/advanced_config/line_fit.png)

9. Після ідентифікації параметрів, компенсацію потужності потрібно ввімкнути, встановивши [CAL_MAG_COMP_TYP](../advanced_config/parameter_reference.md#CAL_MAG_COMP_TYP) на значення 1 (при використанні параметрів тяги) або 2 (при використанні параметрів струму).
   Додатково, параметри компенсації для кожної вісі кожного компасу повинні бути встановлені.

   ![comp params](../../assets/advanced_config/comp_params.png)
