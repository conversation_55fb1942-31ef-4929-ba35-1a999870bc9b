<?xml version="1.0" ?>
<sdf version="1.5">

  <!--
    This is the underwater.world file which starts the Gazebo simulation based on the autonomous underwater vehicle
    <PERSON>po<PERSON><PERSON> from the Technical University Hamburg-Harburg (TUHH).
    https://www.tuhh.de/mum/forschung/forschungsgebiete-und-projekte/flow-field-estimation-with-a-swarm-of-auvs.html
  -->

  <world name="default">
    <!-- A global light source -->
    <light type="directional" name="sun">
        <cast_shadows>true</cast_shadows>
        <pose>0 0 2 0 0 0</pose>
        <diffuse>0.8 0.8 0.8 1</diffuse>
        <specular>0.2 0.2 0.2 1</specular>
        <attenuation>
          <range>1000</range>
          <constant>0.9</constant>
          <linear>0.01</linear>
          <quadratic>0.001</quadratic>
        </attenuation>
        <direction>-0.001 0.001 -0.9</direction>
    </light>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>47.333439</latitude_deg>
      <longitude_deg>8.547097</longitude_deg>
      <elevation>1.7</elevation>
    </spherical_coordinates>
    <!-- Add World Coordinate Frame -->
    <scene>
     <origin_visual>false</origin_visual>
    </scene>
    <model name="world_frame">
      <static>true</static>
      <pose>0 0 0 0 0 0</pose>
      <link name="world_frame">
        <visual name='coordinate_x'>
        <pose>0 10 0 1.570796 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.0025</radius>
            <length>20</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='coordinate_y'>
        <pose>10 0 0 0 1.570796 0</pose>
        <geometry>
          <cylinder>
            <radius>0.0025</radius>
            <length>20</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Green</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='coordinate_z'>
        <pose>0 0 -10 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.0025</radius>
            <length>20</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      </link>
    </model>
    <include>
      <uri>model://ocean</uri>
      <pose>0 0 0 0 0 0</pose>
    </include>
    <!-- Add physics -->
    <physics name='default_physics' default='0' type='ode'>
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>10</iters>
          <sor>1.3</sor>
          <use_dynamic_moi_rescaling>0</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
      <magnetic_field>6.0e-6 2.3e-5 -4.2e-5</magnetic_field>
    </physics>
  </world>
</sdf>
