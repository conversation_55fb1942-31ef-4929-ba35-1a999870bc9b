# Tachometers (Revolution Counters)

Tachometers (also known as [revolution-counter sensors](https://en.wikipedia.org/wiki/Tachometer#In_automobiles,_trucks,_tractors_and_aircraft)) can be used to measure the rate of rotation turning vehicle parts like rotors, engines, or wheels.

::: info
Currently PX4 just logs RPM data: it is not used for state estimation or control.
:::

This section lists the tachometer sensors supported by PX4 (linked to more detailed documentation).
More detailed setup and configuration information is provided in the topics linked below (and sidebar).

![TFRPM01A](../../assets/hardware/sensors/tfrpm/tfrpm01_electronics.jpg)

## Supported Hardware

- [ThunderFly TFRPM01 Tachometer](../sensor/thunderfly_tachometer.md)
