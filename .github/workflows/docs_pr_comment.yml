name: Docs - Comment Workflow
on:
  workflow_run:
    workflows: ["Docs - Check for flaws in PX4 Guide Source"]
    types:
      - completed
jobs:
  comment:
    permissions:
      pull-requests: write # for marocchino/sticky-pull-request-comment
    name: Comments
    runs-on: ubuntu-latest
    steps:
      - name: 'Download PR artifact'
        uses: actions/github-script@v6
        with:
          script: |
            let allArtifacts = await github.rest.actions.listWorkflowRunArtifacts({
               owner: context.repo.owner,
               repo: context.repo.repo,
               run_id: context.payload.workflow_run.id,
            });
            let matchArtifact = allArtifacts.data.artifacts.filter((artifact) => {
              return artifact.name == "pr_number"
            })[0];
            let download = await github.rest.actions.downloadArtifact({
               owner: context.repo.owner,
               repo: context.repo.repo,
               artifact_id: matchArtifact.id,
               archive_format: 'zip',
            });
            let fs = require('fs');
            fs.writeFileSync(`${process.env.GITHUB_WORKSPACE}/pr_number.zip`, Buffer.from(download.data));

      - name: 'Unzip artifact'
        run: unzip pr_number.zip

      # Doesn't work across workflows
      #- name: Get artifacts from flaw checker workflow
      #  uses: actions/download-artifact@v3
      #  with:
      #    name: logs_and_errors
      #    #path: ./logs

      - name: Read errorsFilteredByPrPages.md file
        id: read-errors-by-page
        uses: juliangruber/read-file-action@v1
        with:
          path: ./errorsFilteredByPrPages.md

      - name: Read PR number
        id: read-error-pr-number
        uses: juliangruber/read-file-action@v1
        with:
          path: ./pr_number

      - name: File detail info
        run: |
          echo "${{ steps.read-errors-by-page.outputs.content }}"
          echo "${{ steps.read-error-pr-number.outputs.content }}"

      - name: Create or update comment
        id: comment_to_pr
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          recreate: true
          number: ${{ steps.read-error-pr-number.outputs.content }}
          header: flaws
          message: ${{ steps.read-errors-by-page.outputs.content || 'No flaws found' }}

      #- name: Dump GitHub context
      #  env:
      #    GITHUB_CONTEXT: ${{ toJSON(github) }}
      #  run: echo "$GITHUB_CONTEXT"

      # Would like to do this, but it doesn't work (for me).
      # Moving to time-based, or triggering on workflow
      #- name: Wait for artifacts upload to succeed
      #  uses: lewagon/wait-on-check-action@v1.3.1
      #  with:
      #    ref: ${{ github.ref }}
      #    check-name: 'Archive production artifacts'
      #    repo-token: ${{ secrets.GITHUB_TOKEN }}
      #    wait-interval: 80

      # Not needed for now - trying to trigger off the workflow
      #- name: Sleep for 80 seconds
      #  run: sleep 80s
      #  shell: bash
      #- name: Find Comment
      #  uses: peter-evans/find-comment@v2
      #  id: fc
      #  with:
      #    issue-number: ${{ steps.read-error-pr-number.outputs.content }}
      #    comment-author: 'github-actions[bot]'
      #    body-includes: Flaws (may be none)

      #- name: Create or update comment
      #  uses: peter-evans/create-or-update-comment@v3
      #  with:
      #    comment-id: ${{ steps.fc.outputs.comment-id }}
      #    issue-number: ${{ steps.read-error-pr-number.outputs.content }}
      #    body: |
      #      Flaws (may be none)
      #      ${{ steps.read-errors-by-page.outputs.content }}
      #    edit-mode: replace
