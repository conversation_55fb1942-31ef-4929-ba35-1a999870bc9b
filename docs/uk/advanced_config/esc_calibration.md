# Калібрування ESC

:::info
Ці інструкції мають значення лише для [PWM ESCs](../peripherals/pwm_escs_and_servo.md) та [OneShot ESCs](../peripherals/oneshot.md).
[DShot](../peripherals/dshot.md) та [CAN](../can/README.md) ЕР ([DroneCAN](../dronecan/escs.md)/Cyphal) не потребують такої калібрування.
:::

Електронні регулятори обертів (ЕР) регулюють швидкість (і напрямок) обертання моторів на основі вхідної команди від керуючого пристрою політів (КП).
Діапазон вхідних команд, на які відповідає ЕР, часто може бути налаштований, і типовий діапазон може відрізнятися навіть між ЕР одного й того ж моделі.

Ця калібрування оновлює всі ЕР з фіксованим максимальним (2000us) та мінімальним (1000us) вхідним сигналом PWM від керуючого пристрою.
Після цього всі ЕР/мотори на літальному апараті будуть реагувати на вхід від керуючого пристрою однаковим чином по всьому діапазону вхідних сигналів.

Калібрування за допомогою цього інструменту рекомендується для всіх ЕР, що підтримують цей метод, які використовують PWM або OneShot.

:::info
Калібрування особливо важливе для недорогих ЕР, оскільки вони часто мають значні відмінності в реакції на вхідні сигнали.

Проте його також рекомендується для високоякісних контролерів.
Незважаючи на те, що вони заводськи калібруються і всі мають відреагувати однаково, на практиці діапазон вхідних сигналів може відрізнятися.
Наприклад, якщо контролер було калібровано вручну після виходу з заводу, він може вже не працювати так само.
:::

:::warning
Якщо ви хочете використовувати ESC, який не підтримує цю калібровку, то він повинен бути заводсько налаштований і реагувати послідовно відразу після вилучення з упаковки.
Це слід перевірити за допомогою [Тестування приводів](../config/actuators.md#actuator-testing).
Перейдіть до кроку [конфігурації приводу (7)](#actuatorconfig_step) (що все ще важливо).
:::

ESC OneShot слід [налаштувати на використання режиму OneShot](../peripherals/oneshot.md#px4-configuration) перед калібруванням. Ви повинні провести калібрування ESC після переключення, навіть якщо ви вже раніше калібрували їх.

## Передумови

Послідовність калібрування передбачає, що ви зможете тримати під напругою контролер польоту під час ручного вимикання та увімкнення ESC.

Якщо використовуєте контролер польоту Pixhawk, рекомендується окремо живити контролер польоту через USB та підключати/відключати батарею для живлення ESC за потреби.
Системи керування польотом, які не можуть живити автопілот через USB, потребуватимуть [іншого підходу](#problem_power_module).

Якщо акумулятор підключений через модуль живлення, процедура калібрування може виявити підключення акумулятора та використовувати його для запуску послідовності калібрування.
Якщо жодний акумулятор не виявлено, послідовність калібрування виконується на основі таймаутів.

## Кроки

Для калібрування ЕСК виконайте наступні кроки:

1. Видаліть пропелери.

   Ніколи не намагайтеся ESC калібрувати з пропелерами!

   Мотори не _повинні_ крутитися під час калібрування ESC.
   Однак, якщо калібрування починається, коли ESC вже живлені, або якщо ESC не правильно підтримує або не виявляє послідовність калібрування, то вони відповідатимуть на вхід PWM, запускаючи мотори з максимальною швидкістю.

:::

2. Map the ESCs you're calibrating as motors in the vehicle's [Actuator Configuration](../config/actuators.md).
   Лише відображені актуатори отримують вихід, і тільки ESC, відображені як мотори, будуть калібруватися.

3. Відключіть живлення ESC, від'єднавши батарею.
   Контролер польоту має залишитися увімкненим, наприклад, тримаючи USB підключеним до наземної станції.

4. Відкрийте налаштування _QGroundControl_ > **Живлення > Power**, а потім натисніть кнопку **Калібрувати**.

   ![ESC Calibration step 1](../../assets/qgc/setup/esc/qgc_esc_calibration.png)

5. Після початку послідовності калібрування без помилок безпосередньо живіть ESC (ви маєте побачити відповідне повідомлення):

   ![ESC Calibration step 2](../../assets/qgc/setup/esc/esc_calibration_step_2.png)

   Калібрування розпочнеться автоматично:

   ![ESC Calibration step 3](../../assets/qgc/setup/esc/esc_calibration_step_3.png)

6. Під час калібрування ви почуєте специфічний для моделі сигнал звуку з ESC, що вказує на окремі кроки калібрування.

   Вас сповістять, коли калібрування завершиться.

   <a id="actuatorconfig_step"></a>
   ![ESC Calibration step 4](../../assets/qgc/setup/esc/esc_calibration_step_4.png)

7. Поверніться до розділу [Налаштування актуатора](../config/actuators.md).

   Після калібрування всі мотори з тими ж (пере)каліброваними ESC мають працювати однаково за тими ж вхідними даними. Значення налаштувань PWM за замовчуванням для вихідних даних моторів в налаштуваннях актуатора тепер повинні працювати зразу після розпакування.

   Вам потрібно переконатися, що мотори дійсно працюють правильно.
   Оскільки значення конфігурації за замовчуванням встановлені консервативно, ви також можете бажати налаштувати їх для вашого конкретного ESC.

   Нижче наведено аналогічні кроки, що описані в [Налаштування актуатора > Конфігурація мотора](../config/actuators.md#motor-configuration).

:::

   Перевірте наступні значення:

   - Мінімальне значення для мотора (за замовчуванням: 1100 мкс) повинно забезпечувати повільний, але надійний оберт мотора, а також надійно запускати його після зупинки.

      Ви можете підтвердити, що мотор обертається мінімально (без пропеллерів) в [TestActuator](../config/actuators. d#actuator-testing), увімкнувши повзунки, а потім переміщуючи повзунок виводу тесту для двигуна в першу позицію відключення від низу.
      Правильне значення має зробити так, що мотор обертається негайно і надійно при пересуванні повзунка зі стану роззброєності до мінімуму.

      Щоб знайти «оптимальне» мінімальне значення, пересуньте повзунок вниз (режим роззброєності).
      Потім збільшуйте значення PWM-виходу в режимі `роззброєності` невеликими інкрементами (наприклад, 1025 мкс, 1050 мкс і т. д.), доки мотор не почне надійно обертатися (краще бути трохи вище, ніж трохи нижче).
      Введіть це значення в параметр «мінімум» для всіх вихідних PWM сигналів мотора, а вихідний сигнал `роззброєності` відновіть до `1100 мкс`.

   - Максимальне значення для мотора (за замовчуванням: `1900 мкс`) слід вибрати так, щоб збільшення значення не зробило мотор обертатися швидше.

      Ви можете підтвердити, що мотор обертається швидко при максимальному значенні у режимі [Тестування приводів](../config/actuators.md#actuator-testing), перемістивши пов'язаний слайдер випробування вверх.

      Щоб знайти "оптимальне" максимальне значення, спочатку перемістіть повзунок вниз (роззброєно).
      Потім збільште налаштування вихідної потужності PWM `вимкненої` близько до максимального значення за замовчуванням (`1900`) - мотори повинні розганятися.
      Слухайте тон мотора, коли збільшуєте максимальне значення PWM для виводу поетапно (наприклад, 1925 мкс, 1950 мкс і так далі).
      Оптимальне значення визначається в той момент, коли звук моторів не змінюється при збільшенні значення виводу.
      Введіть це значення в параметр `максимум` для всіх виводів ШІМ мотора, а також відновіть значення виводу `знято` на `1100 мкс`.

   - Значення виводу «знято» для мотора (за замовчуванням: `1000 мкс`) повинно зупиняти мотор і залишати його зупиненим.

      Ви можете підтвердити це в розділі [Тестування виконавчих механізмів](../config/actuators.md#actuator-testing), перемістивши слайдер виводу тестування до фіксованого положення у нижній частині слайдера і спостерігаючи, що двигун не обертається.

      Якщо ESC обертається за замовчуванням на значенні 1000 мкс, то ESC не правильно калібрується.
      Якщо використовуєте ESC, який не може бути калібрований, вам слід зменшити значення виведення ШІМ для виводу до значення, коли мотор більше не обертається (наприклад, 950 мкс або 900 мкс).

   ::: info
   VTOL and fixed-wing motors do not need any special PWM configuration.
   При встановленні стандартної настройки PWM вони автоматично зупиняться під час польоту при наказі автопілотом.

:::

## Усунення проблем

1. Калібрування може повідомити про успішне завершення, навіть якщо воно фактично не вдалося.

   Це може статися, якщо ви не вмикаєте ESC у відповідний час або ESC не підтримують калібрування.
   Це стається тому, що PX4 не отримує зворотного зв'язку від ESC, щоб знати, чи було калібрування успішним.
   Вам потрібно спиратися на інтерпретацію сигналів під час калібрування та наступних тестів моторів, щоб впевнитися, що калібрування пройшло успішно.

   <a id="problem_power_module"></a>

2. Калібрування не може бути запущено, якщо у вас налаштований і підключений модуль живлення (з міркувань безпеки).

   Спочатку відключіть живлення до регуляторів обертання.
   If you're blocked because a power module is necessary to keep your flight controller alive, but you can (un)power the ESCs separately, you can temporarily disable the detection of the power module just for the ESC calibration using the parameters [BATn_SOURCE](../advanced_config/parameter_reference.md#BAT1_SOURCE). Коли модуль живлення, який живить автопілот, більше не виявляється як акумулятор, можлива калібрування на основі часу.

3. PX4 перерве калібрування (з міркувань безпеки), якщо система виявить збільшення споживання струму безпосередньо після початку калібрування.Це потребує модуля живлення.
