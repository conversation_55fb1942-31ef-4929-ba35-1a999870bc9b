#!/bin/sh
#
# board sensors init
#------------------------------------------------------------------------------

param set-default IMU_GYRO_RATEMAX 1000
param set-default SENS_FLOW_RATE 150
param set-default SENS_IMU_CLPNOTI 0

# Internal SPI
if ! paw3902 -s start -Y 180
then
	paa3905 -s start -Y 180
fi

if ! icm42688p -R 0 -s start
then
	if ! iim42652 -R 0 -s start
	then
		bmi088 -A -s -R 4 start
		bmi088 -G -s -R 4 start
	fi
fi

afbrs50 start
