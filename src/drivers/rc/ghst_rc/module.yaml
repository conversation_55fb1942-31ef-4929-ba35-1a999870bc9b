module_name: GHST RC Input Driver
serial_config:
    - command: "ghst_rc start -d ${SERIAL_DEV}"
      port_config_param:
        name: RC_GHST_PRT_CFG
        group: Serial
        #default: RC
        #depends_on_port: RC
        description_extended: |
            Ghost (GHST) RC driver.

parameters:
    - group: RC
      definitions:
        RC_GHST_TEL_EN:
            description:
                short: Ghost RC telemetry enable
                long: |
                    Ghost telemetry enable

            type: boolean
            default: [0]
