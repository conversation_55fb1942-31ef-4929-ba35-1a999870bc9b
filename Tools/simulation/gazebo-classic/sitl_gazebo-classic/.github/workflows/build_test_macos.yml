name: MacOS Build Tests

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - '*'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [macos-13]
    steps:
    - uses: actions/checkout@v1
    - name: submodule update
      run: git submodule update --init --recursive
    - name: Before Install
      run: |
        brew update;
        git clone -b v1.15.2 https://github.com/google/googletest
        git clone https://github.com/mavlink/c_library_v2.git /usr/local/include/mavlink
    - name: Install
      run: |
        rm '/usr/local/bin/2to3'
        rm '/usr/local/bin/idle3'
        rm '/usr/local/bin/pydoc3'
        rm '/usr/local/bin/python3'
        rm '/usr/local/bin/python3-config'
        pushd googletest
        mkdir build
        pushd build
        cmake ..
        make
        make install
        popd
        popd
        brew install gstreamer gst-plugins-base gst-plugins-good glib python
        brew install --cask xquartz
        brew tap PX4/px4
        brew install px4-dev
        brew install px4-sim
        pip3 install --user --upgrade setuptools
        pip3 install --user --upgrade rospkg pyserial empy toml numpy pandas jinja2
    - name: Cmake Build
      run: |
        mkdir build
        cd build
        cmake ..
        make
    - name: Unit Tests
      working-directory: build
      run: |
        cmake -DENABLE_UNIT_TESTS=On ..
        make
        make test
