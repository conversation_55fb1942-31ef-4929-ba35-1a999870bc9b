# Посилання на модулі: Симуляція

## simulator_sih

Source: [modules/simulation/simulator_sih](https://github.com/PX4/PX4-Autopilot/tree/main/src/modules/simulation/simulator_sih)

### Опис

Цей модуль надає симулятор для квадрокоптерів та фіксованих крил, що повністю працює всередині апаратного автопілота.

Цей симулятор підписується на "actuator_outputs", які є сигналами ШІМ, наданими модулем розподілу керування.

Цей симулятор публікує сигнали датчиків, спотворені реалістичним шумом, щоб включити оцінювання стану в петлю.

### Імплементація

Симулятор реалізує рівняння руху за допомогою матричної алгебри.Використовується кватерніонне представлення для орієнтації.Для інтегрування використовується пряма схема Ейлера.Більшість змінних оголошуються глобальними в файлі .hpp, щоб уникнути переповнення стеку.

### Usage {#simulator_sih_usage}

```
simulator_sih <command> [arguments...]
 Commands:
   start

   stop

   status        print status info
```
