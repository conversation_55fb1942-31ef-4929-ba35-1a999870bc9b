name: Nuttx Target with extra env config

on:
  push:
    branches:
    - 'main'
    paths-ignore:
      - 'docs/**'
      - '.github/**'
  pull_request:
    branches:
    - '*'
    paths-ignore:
      - 'docs/**'
      - '.github/**'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        config: [
          px4_fmu-v5_default,
          ]

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Build PX4 and Run Test [${{ matrix.config }}]
      uses: addnab/docker-run-action@v3
      with:
        image: px4io/px4-dev:v1.16.0-rc1-258-g0369abd556
        options: -v ${{ github.workspace }}:/workspace
        run: |
          cd /workspace
          git config --global --add safe.directory /workspace
          export PX4_EXTRA_NUTTX_CONFIG="CONFIG_NSH_LOGIN_PASSWORD=\"test\";CONFIG_NSH_CONSOLE_LOGIN=y"
          echo "PX4_EXTRA_NUTTX_CONFIG: $PX4_EXTRA_NUTTX_CONFIG"
          make ${{ matrix.config }} nuttx_context
          echo "Check that the config option is set"
          grep CONFIG_NSH_LOGIN_PASSWORD build/${{ matrix.config }}/NuttX/nuttx/.config
