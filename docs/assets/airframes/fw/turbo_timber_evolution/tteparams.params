param set-default ASPD_DO_CHECKS 19
param set-default ASPD_FALLBACK 1
param set-default BAT1_N_CELLS 4
param set-default BAT1_V_CHARGED 4.2000
param set-default BAT1_V_EMPTY 2.5000
param set-default BAT_EMERGEN_THR 0.0300
param set-default BAT_LOW_THR 0.1200
param set-default CA_SV_CS0_TRQ_R 0.5000
param set-default CA_SV_CS0_TYPE 2
param set-default CA_SV_CS1_TRQ_P 1.0000
param set-default CA_SV_CS1_TRQ_R 0.0000
param set-default CA_SV_CS1_TYPE 3
param set-default CA_SV_CS2_TRQ_P 0.0000
param set-default CA_SV_CS2_TRQ_Y 1.0000
param set-default CA_SV_CS2_TYPE 4
param set-default CA_SV_CS_COUNT 3
param set-default CBRK_SUPPLY_CHK 894281
param set-default COM_ARM_SWISBTN 1
param set-default COM_DISARM_LAND 0.0000
param set-default COM_FLTMODE1 0
param set-default COM_FLTMODE4 6
param set-default COM_FLTMODE6 8
param set-default COM_PREARM_MODE 2
param set-default FD_FAIL_P_TTRI 5.0000
param set-default FW_ACRO_X_MAX 135.0000
param set-default FW_AIRSPD_MAX 26.8224
param set-default FW_AIRSPD_STALL 8.9408
param set-default FW_AIRSPD_TRIM 15.6464
param set-default FW_AT_MAN_AUX 2
param set-default FW_PR_FF 0.6235
param set-default FW_PR_I 0.2276
param set-default FW_PR_P 0.1916
param set-default FW_P_TC 0.3201
param set-default FW_RR_FF 0.1578
param set-default FW_RR_I 0.2219
param set-default FW_RR_P 0.1618
param set-default FW_R_RMAX 90.0000
param set-default FW_R_TC 0.1255
param set-default FW_THR_TRIM 0.6500
param set-default FW_YR_FF 0.9500
param set-default FW_YR_I 0.0000
param set-default FW_YR_P 0.3000
param set-default GF_ACTION 0
param set-default MAV_0_RATE 0
param set-default MC_AT_EN 0
param set-default MSP_OSD_CONFIG 104
param set-default OSD_DWELL_TIME 100
param set-default OSD_LOG_LEVEL 6
param set-default OSD_SCROLL_RATE 50
param set-default OSD_SYMBOLS 2637823
param set-default PWM_AUX_DIS1 1500
param set-default PWM_AUX_DIS2 1500
param set-default PWM_AUX_DIS4 1500
param set-default PWM_AUX_DIS5 1100
param set-default PWM_AUX_DIS8 1450
param set-default PWM_AUX_FUNC1 201
param set-default PWM_AUX_FUNC2 202
param set-default PWM_AUX_FUNC3 101
param set-default PWM_AUX_FUNC4 203
param set-default PWM_AUX_FUNC5 406
param set-default PWM_AUX_FUNC8 407
param set-default PWM_AUX_MAX1 1900
param set-default PWM_AUX_MAX2 2100
param set-default PWM_AUX_MAX5 1900
param set-default PWM_AUX_MAX8 2050
param set-default PWM_AUX_MIN1 1100
param set-default PWM_AUX_MIN2 1100
param set-default PWM_AUX_MIN3 925
param set-default PWM_AUX_MIN5 1100
param set-default PWM_AUX_MIN8 850
param set-default PWM_AUX_REV 136
param set-default PWM_AUX_TIM0 100
param set-default PWM_AUX_TIM1 100
param set-default PWM_AUX_TIM2 100
param set-default PWM_AUX_TIM3 100
param set-default RTL_DESCEND_ALT 36.5760
param set-default RTL_RETURN_ALT 36.5760
param set-default RTL_TYPE 0
param set-default SENS_EN_MS4525DO 1
param set-default SER_TEL1_BAUD 115200
param set-default TRIM_PITCH -0.4000
param set-default UAVCAN_ENABLE 0
# Make sure to add all params from the current airframe (ID=2100) as well
