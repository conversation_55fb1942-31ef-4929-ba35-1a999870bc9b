#!/bin/sh
#
# @name SIH Tailsitter Duo
#
# @type Simulation
# @class VTOL
#
# @output Motor1 motor right
# @output Motor2 motor left
# @output Servo1 elevon right
# @output Servo2 elevon left
#
# @maintainer <PERSON><PERSON> <<EMAIL>>
#
# @board px4_fmu-v2 exclude
#

. ${R}etc/init.d/rc.vtol_defaults

param set-default EKF2_FUSE_BETA 0 # side slip fusion is currently not supported for tailsitters

param set UAVCAN_ENABLE 0
param set-default VT_B_TRANS_DUR 5
param set-default VT_ELEV_MC_LOCK 0
param set-default VT_MOT_COUNT 2
param set-default VT_TYPE 0
param set-default VT_FW_DIFTHR_EN 1
param set-default VT_FW_DIFTHR_S_Y 0.3
param set-default MPC_MAN_Y_MAX 60
param set-default MC_PITCH_P 5

param set-default CA_AIRFRAME 4
param set-default CA_ROTOR_COUNT 2
param set-default CA_ROTOR0_KM -0.05
param set-default CA_ROTOR0_PY 0.2
param set-default CA_ROTOR1_KM -0.05
param set-default CA_ROTOR1_PY -0.2
param set-default CA_SV_CS_COUNT 2
param set-default CA_SV_CS0_TRQ_P 0.3
param set-default CA_SV_CS0_TRQ_Y 0.3
param set-default CA_SV_CS0_TYPE 5
param set-default CA_SV_CS1_TRQ_P 0.3
param set-default CA_SV_CS1_TRQ_Y -0.3
param set-default CA_SV_CS1_TYPE 6

param set-default FW_AIRSPD_MAX 12
param set-default FW_AIRSPD_MIN 7
param set-default FW_AIRSPD_TRIM 10

param set-default HIL_ACT_FUNC1 101
param set-default HIL_ACT_FUNC2 102
param set-default HIL_ACT_FUNC5 202
param set-default HIL_ACT_FUNC6 201
param set-default HIL_ACT_REV 32

param set-default MAV_TYPE 19



# set SYS_HITL to 2 to start the SIH and avoid sensors startup
param set-default SYS_HITL 2

# disable some checks to allow to fly:
# - without real battery
param set-default CBRK_SUPPLY_CHK 894281

param set-default SENS_DPRES_OFF 0.001

param set SIH_T_MAX 2.0
param set SIH_Q_MAX 0.0165
param set SIH_MASS 0.2
# IXX and IZZ are inverted from the thesis as the body frame is pitched by 90 deg
param set SIH_IXX 0.00354
param set SIH_IYY 0.000625
param set SIH_IZZ 0.00300
param set SIH_IXZ 0
param set SIH_KDV 0.2
param set SIH_L_ROLL 0.145

# sih as tailsitter
param set SIH_VEHICLE_TYPE 2

param set-default VT_ARSP_TRANS 6
