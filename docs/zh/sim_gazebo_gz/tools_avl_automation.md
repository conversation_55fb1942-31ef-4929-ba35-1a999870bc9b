# Advanced Lift Drag (AVL) Automation Tool

The Gazebo [Advanced Plane](../sim_gazebo_gz/vehicles.md#advanced-plane) vehicle model uses the _Advanced Lift Drag_ plugin to model vehicle lift and drag behaviour.
This tool allows you to calculate the parameters required to create a _Advanced Lift Drag_ plugin for your own particular vehicle.

You only have to provide a few parameters for each wing foil and the tool will use this information to call the Athena Lattice Vortex (AVL) to make the necessary calculations.
The results will then automatically be written into a provided plugin template that can then be copy-pasted into a model or world sdf file.

## Install

To setup the tool:

1. Download AVL 3.36 from <https://web.mit.edu/drela/Public/web/avl/>.
  The file for AVL version 3.36 can be found about halfway down the page.

2. After downloading, extract AVL and move it to the home directory using:

  ```sh
  sudo tar -xf avl3.36.tgz
  mv ./Avl /home/
  ```

3. Follow the **index.md** found in `./Avl` to finish the setup process for AVL (this requires that you set up `plotlib` and `eispack` libraries).
  We recommend using the `gfortran` compile option, which might further require that you to install `gfortran`.
  On Ubuntu can be done by running:

  ```sh
  sudo apt update
  sudo apt install gfortran
  ```

  When running the Makefile for AVL, you might encounter an `Error 1` message stating that there is a directory missing.
  This does not prevent AVL from working for our purposes.

Once the process described in the AVL README is completed, AVL is ready to be used.
No further set up is required on the side of the AVL or the tool.

If you want to move the location of the AVL directory, this can simply be done by passing the `--avl_path` flag to the `input_avl.py` file, using the desired directory location for the flag (don't forget to place a "/" behind the last part of the path).
Running this will automatically also adjust the paths where necessary.

## Run AVL

An example template has been provided in the form of the `input.yml` that implements a standard plane with two ailerons, an elevator and a rudder.
This example template can be run using: `python input_avl.py --yaml_file input.yml`.

To run the tool for your plane:

1. Copy the example `input.yml` to `<your_custom_yaml_file>.yml` and modify it to match your desired plane

2. Run the tool on your yml file:

  ```sh
  python input_avl.py <your_custom_yaml_file>.yml
  ```

  Note that the `yaml` and `argparse` packages must be present in your Python environment.

3. The tool prompts for a range of vehicle specific parameters that are needed in order to specify the geometry and physical properties of the plane.
  You can either:
  - select a predefined model template (such as a Cessna or a VTOL), which has a known number of control surfaces, and just modify some physical properties, or
  - define a completely custom model

Once the script has been executed, the generated `.avl`, `.sdf` and a plot of the proposed control surfaces can be found in `<your-plane-name>` directory.
The sdf file is the generated Advanced Lift Drag Plugin that can be copied and pasted into a model.sdf file, which can then be run in Gazebo.

## Functionality

The **input_avl.py** file takes the user-provided parameters and creates an .avl file from this that can be read by AVL (the program).
This happens in the **process.sh** file.

The output generated by AVL will be saved in two files: **custom_vehicle_body_axis_derivatives.txt** and **custom_vehicle_stability_derivatives.txt**.
These two files contain the parameters that are required in order to populate the Advanced Lift Drag Plugin.

Finally, **avl_out_parse.py** reads the generated .txt files and assigns parameters to the correct elements in sdf.

The generated Advanced Lift Drag plugin (`<custom_plane>.sdf`) can be copied into the particular **model.sdf** file used by Gazebo.

## Usability

The current implementation provides a minimal working example.
More accurate measurements can be made by adjusting the chosen number of vortices along span and chord according to desired preferences.
A good starting point for this can be found here: <https://www.redalyc.org/pdf/6735/673571173005.pdf>.

One can also more accurately model a vehicle by using a larger number of sections.
In the current .yml file, only a left and right edge are defined for each surface yielding exactly one section, but the code supports expanding this to any number of desired sections.

::: info

- A control surface in AVL is always defined from left to right.
  This means you need to first provide the left edge of a surface and then the right edge.
  If you do this the opposite way around, a surface will essentially be defined upside down.
- The tool is designed to only support at most two control surfaces of any type on any one vehicle.
  Having more surfaces than that can lead to faulty behavior.
- Another important point is that these scripts make use of the match, case syntax, which was only introduced in Python in version 3.10.
- The primary reference resource for AVL can be found at <https://web.mit.edu/drela/Public/web/avl/AVL_User_Primer.pdf>.
  This document was written by the creators of AVL and contains all the variables that could be required in defining the control surfaces.
- AVL cannot predict stall values so these need to be calculated/estimated in another way.
  In the current implementation, default stall values have been taken from PX4's Advanced Plane.
  These should naturally be changed for new/different models.

:::

## Parameter Assignment

Below is a comprehensive list on how the parameters are assigned at output and what files in AVL they are taken from.
The Advanced Lift Drag Plugin contains more detail about what each of these parameters do.

:::info
The parameters have not been verified by an expert, so you should check them in the plugin.
:::

From the stability derivatives log file, the following advanced lift drag plugin parameters are taken:

| Name in AVL | Name in Advanced Lift Drag Plugin | 描述                                                                         |
| ----------- | --------------------------------- | -------------------------------------------------------------------------- |
| Alpha       | alpha                             | The angle of attack                                                        |
| Cmtot       | Cem0                              | Pitching moment coefficient at zero angle of attack                        |
| CLtot       | CL0                               | Lift Coefficient at zero angle of attack                                   |
| CDtot       | CD0                               | Drag coefficient at zero angle of attack                                   |
| CLa         | CLa                               | dCL/da (slope of CL-alpha curve)                        |
| CYa         | CYa                               | dCy/da (sideforce slope wrt alpha)                      |
| Cla         | Cell                              | dCl/da (roll moment slope wrt alpha)                    |
| Cma         | Cema                              | dCm/da (pitching moment slope wrt aLpha - before stall) |
| Cna         | Cena                              | dCn/da (yaw moment slope wrt alpha)                     |
| CLb         | CLb                               | dCL/dbeta (lift coefficient slope wrt beta)             |
| CYb         | CYb                               | dCY/dbeta (side force slope wrt beta)                   |
| Clb         | Cell                              | dCl/dbeta (roll moment slope wrt beta)                  |
| Cmb         | Cemb                              | dCm/dbeta (pitching moment slope wrt beta)              |
| Cnb         | Cenb                              | dCn/dbeta (yaw moment slope wrt beta)                   |

From the body axis derivatives log file, the following advanced lift drag plugin parameters are taken:

| Name in AVL | Name in Advanced Lift Drag Plugin | 描述                                                                          |
| ----------- | --------------------------------- | --------------------------------------------------------------------------- |
| e           | eff                               | Wing efficiency (Oswald efficiency factor for a 3D wing) |
| CXp         | CDp                               | dCD/dp (drag coefficient slope wrt roll rate)            |
| CYp         | CYp                               | dCY/dp (sideforce slope wrt roll rate)                   |
| CZp         | CLp                               | dCL/dp (lift coefficient slope wrt roll rate)            |
| Clp         | Cellp                             | dCl/dp (roll moment slope wrt roll rate)                 |
| Cmp         | Cemp                              | dCm/dp (pitching moment slope wrt roll rate)             |
| Cmp         | Cenp                              | dCn/dp (yaw moment slope wrt roll rate)                  |
| CXq         | CDq                               | dCD/dq (drag coefficient slope wrt pitching rate)        |
| CYq         | CYq                               | dCY/dq (side force slope wrt pitching rate)              |
| CZq         | CLq                               | dCL/dq (lift coefficient slope wrt pitching rate)        |
| Clq         | Cellq                             | dCl/dq (roll moment slope wrt pitching rate)             |
| Cmq         | Cemq                              | dCm/dq (pitching moment slope wrt pitching rate)         |
| Cnq         | Cenq                              | dCn/dq (yaw moment slope wrt pitching rate)              |
| CXr         | CDr                               | dCD/dr (drag coefficient slope wrt yaw rate)             |
| CYr         | CYr                               | dCY/dr (side force slope wrt yaw rate)                   |
| CZr         | CLr                               | dCL/dr (lift coefficient slope wrt yaw rate)             |
| Clr         | Cellr                             | dCl/dr (roll moment slope wrt yaw rate)                  |
| Cmr         | Cemr                              | dCm/dr (pitching moment slope wrt yaw rate)              |
| Cnr         | Cenr                              | dCn/dr (yaw moment slope wrt yaw rate)                   |

Furthermore, every control surface also has six own parameters, which are also derived from this log file.
`{i}` below ranges from 1 to the number of unique control surface types in the model.

| Name in AVL | Name in Advanced Lift Drag Plugin | 描述                                                            |
| ----------- | --------------------------------- | ------------------------------------------------------------- |
| CXd{i}      | CD_ctrl      | Effect of the control surface's deflection on drag            |
| CYd{i}      | CY_ctrl      | Effect of the control surface's deflection on side force      |
| CZd{i}      | CL_ctrl      | Effect of the control surface's deflection on lift            |
| Cld{i}      | Cell_ctrl    | Effect of the control surface's deflection on roll moment     |
| Cmd{i}      | Cem_ctrl     | Effect of the control surface's deflection on pitching moment |
| Cnd{i}      | Cen_ctrl     | Effect of the control surface's deflection on yaw moment      |
