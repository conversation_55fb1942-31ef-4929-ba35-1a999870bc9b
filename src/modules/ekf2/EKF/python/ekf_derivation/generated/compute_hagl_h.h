// -----------------------------------------------------------------------------
// This file was autogenerated by symforce from template:
//     function/FUNCTION.h.jinja
// Do NOT modify by hand.
// -----------------------------------------------------------------------------

#pragma once

#include <matrix/math.hpp>

namespace sym {

/**
 * This function was autogenerated from a symbolic function. Do not modify by hand.
 *
 * Symbolic function: compute_hagl_h
 *
 * Args:
 *
 * Outputs:
 *     H: Matrix24_1
 */
template <typename Scalar>
void ComputeHaglH(matrix::Matrix<Scalar, 24, 1>* const H = nullptr) {
  // Total ops: 0

  // Input arrays

  // Intermediate terms (0)

  // Output terms (1)
  if (H != nullptr) {
    matrix::Matrix<Scalar, 24, 1>& _h = (*H);

    _h.set<PERSON>ero();

    _h(8, 0) = -1;
    _h(23, 0) = 1;
  }
}  // NOLINT(readability/fn_size)

// NOLINTNEXTLINE(readability/fn_size)
}  // namespace sym
