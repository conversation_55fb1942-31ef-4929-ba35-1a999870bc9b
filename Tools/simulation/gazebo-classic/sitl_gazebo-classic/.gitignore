Build/
build/
scripts/schemas
.DS_Store
*~
models/iris/*.sdf
*.pyc
.*.sw?
*-gen.sdf
*.sdf.last_generated

models/3DR_gps_mag/3DR_gps_mag.sdf
models/c920/c920.sdf
models/cloudship/cloudship.sdf
models/iris/iris.sdf
models/matrice_100/matrice_100.sdf
models/mb1240-xl-ez4/mb1240-xl-ez4.sdf
models/pixhawk/pixhawk.sdf
models/plane/plane.sdf
models/px4flow/px4flow.sdf
models/r200/r200.sdf
models/sf10a/sf10a.sdf
models/standard_vtol/standard_vtol.sdf
models/r1_rover/r1_rover.sdf
models/rover/rover.sdf
models/tiltrotor/tiltrotor.sdf
models/boat/boat.sdf
models/typhoon_h480/typhoon_h480.sdf
models/depth_camera/depth_camera.sdf
models/standard_vtol_drop/standard_vtol_drop.sdf
models/tailsitter/tailsitter.sdf
models/advanced_plane/advanced_plane.sdf
models/quadtailsitter/quadtailsitter.sdf
