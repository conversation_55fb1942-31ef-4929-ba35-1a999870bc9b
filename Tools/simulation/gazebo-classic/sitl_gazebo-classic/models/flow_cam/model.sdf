<?xml version="1.0" ?>
<sdf version="1.4">
  <model name="camera">
    <link name="link">
      <inertial>
        <pose>0.01 0.025 0.025 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <geometry>
          <box>
            <size>0.02 0.05 0.05</size>
          </box>
        </geometry>
      </visual>

      <!--752x480 MT9V034 image sensor, only 64x64 pixels used-->
      <sensor name="PX4Flow" type="camera">
        <always_on>true</always_on>
        <update_rate>100.0</update_rate>
        <visualize>true</visualize>
        <topic>/px4flow</topic>
        <camera>
          <horizontal_fov>0.088</horizontal_fov>
          <image>
            <width>64</width>
            <height>64</height>
            <format>L8</format>
          </image>
          <clip>
            <near>0.1</near>
            <far>100</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.001</stddev>
          </noise>
        </camera>
        <plugin name="opticalflow_plugin" filename="libgazebo_opticalflow_plugin.so">
            <robotNamespace></robotNamespace>
            <outputRate>20</outputRate>
        </plugin>
      </sensor>
    </link>

  </model>
</sdf>
<!-- vim: set et fenc=utf-8 ff=unix sts=0 sw=4 ts=4 : -->
