/*
 * UAVCAN data structure definition for libuavcan.
 *
 * Autogenerated, do not edit.
 *
 * Source file: ${t.source_file}
 */

#ifndef ${t.include_guard}
#define ${t.include_guard}

#include <uavcan/build_config.hpp>
#include <uavcan/node/global_data_type_registry.hpp>
#include <uavcan/marshal/types.hpp>

% for inc in t.cpp_includes:
#include <${inc}>
% endfor

/******************************* Source text **********************************
% for line in t.source_text.strip().splitlines():
${line}
% endfor
******************************************************************************/

/********************* DSDL signature source definition ***********************
% for line in t.get_dsdl_signature_source_definition().splitlines():
${line}
% endfor
******************************************************************************/

% for a in t.all_attributes:
#undef ${a.name}
% endfor

% for nsc in t.cpp_namespace_components:
namespace ${nsc}
{
% endfor

% if t.kind != t.KIND_SERVICE:
template <int _tmpl>
% endif
struct UAVCAN_EXPORT ${t.cpp_type_name}
{
<!--(macro generate_primary_body)--> #! type_name, max_bitlen, fields, constants, union
    typedef const ${type_name}<_tmpl>& ParameterType;
    typedef ${type_name}<_tmpl>& ReferenceType;

    <!--(macro expand_attr_types)--> #! group_name, attrs
    struct ${group_name}
    {
        % for a in attrs:
        typedef ${a.cpp_type} ${a.name};
        % endfor
    };
    <!--(end)-->
    ${expand_attr_types(group_name='ConstantTypes', attrs=constants)}
    ${expand_attr_types(group_name='FieldTypes', attrs=fields)}

    % if union:
    
    struct Tag
    {
        enum Type
        {
        % for idx,last,a in enum_last_value(fields):
            ${a.name}${',' if not last else ''}
        % endfor
        };
    };

    typedef ::uavcan::IntegerSpec< ::uavcan::IntegerBitLen< ${len(fields)}U - 1U >::Result,
                                   ::uavcan::SignednessUnsigned, ::uavcan::CastModeTruncate > TagType;

        <!--(macro expand_enum_per_field)--> #! enum_name, enum_comparator
    enum
    {
        ${enum_name} = TagType::BitLen +
            % for idx,last,a in enum_last_value(fields):
                % if not last:
            ::uavcan::${enum_comparator}<FieldTypes::${a.name}::${enum_name},
                % else:
                FieldTypes::${a.name}::${enum_name} ${'>::Result' * (len(fields) - 1)}
                % endif
            % endfor
    };
        <!--(end)-->

    ${expand_enum_per_field(enum_name='MinBitLen', enum_comparator='EnumMin')}
    ${expand_enum_per_field(enum_name='MaxBitLen', enum_comparator='EnumMax')}

    % else:

        <!--(macro expand_enum_per_field)--> #! enum_name
    enum
    {
        ${enum_name}
            % for idx,a in enumerate(fields):
            ${'=' if idx == 0 else '+'} FieldTypes::${a.name}::${enum_name}
            % endfor
    };
        <!--(end)-->

    ${expand_enum_per_field(enum_name='MinBitLen')}
    ${expand_enum_per_field(enum_name='MaxBitLen')}

    % endif

    // Constants
    % for a in constants:
    static const typename ::uavcan::StorageType< typename ConstantTypes::${a.name} >::Type ${a.name}; // ${a.init_expression}
    % endfor

    // Fields
    % for a in [x for x in fields if not x.void]:
    typename ::uavcan::StorageType< typename FieldTypes::${a.name} >::Type ${a.name};
    % endfor

    % if union:
private:
    typename ::uavcan::StorageType< TagType >::Type _tag_;  // The name is mangled to avoid clashing with fields

    template <typename Tag::Type T>
    struct TagToType;

public:
    % endif

    ${type_name}()
    % for idx,a in enumerate([x for x in fields if not x.void]):
        ${':' if idx == 0 else ','} ${a.name}()
    % endfor
    % if union:
        , _tag_()
    % endif
    {
        ::uavcan::StaticAssert<_tmpl == 0>::check();  // Usage check

#if UAVCAN_DEBUG
        /*
         * Cross-checking MaxBitLen provided by the DSDL compiler.
         * This check shall never be performed in user code because MaxBitLen value
         * actually depends on the nested types, thus it is not invariant.
         */
        ::uavcan::StaticAssert<${max_bitlen} == MaxBitLen>::check();
#endif
    }

    bool operator==(ParameterType rhs) const;
    bool operator!=(ParameterType rhs) const { return !operator==(rhs); }

    /**
     * This comparison is based on @ref uavcan::areClose(), which ensures proper comparison of
     * floating point fields at any depth.
     */
    bool isClose(ParameterType rhs) const;

    static int encode(ParameterType self, ::uavcan::ScalarCodec& codec,
                      ::uavcan::TailArrayOptimizationMode tao_mode = ::uavcan::TailArrayOptEnabled);

    static int decode(ReferenceType self, ::uavcan::ScalarCodec& codec,
                      ::uavcan::TailArrayOptimizationMode tao_mode = ::uavcan::TailArrayOptEnabled);

    % if union:
    /**
     * Explicit access to the tag.
     * It is safer to use is()/as()/to() instead.
     */
    typename Tag::Type getTag() const { return typename Tag::Type(_tag_); }
    void setTag(typename Tag::Type x) { _tag_ = typename ::uavcan::StorageType< TagType >::Type(x); }

    /**
     * Whether the union is set to the given type.
     * Access by tag; this will work even if there are non-unique types within the union.
     */
    bool is(typename Tag::Type x) const { return typename Tag::Type(_tag_) == x; }

    /**
     * If the union is currently set to the type T, returns pointer to the appropriate field.
     * If the union is set to another type, returns null pointer.
     */
    template <typename Tag::Type T>
    inline const typename TagToType<T>::StorageType* as() const;

    /**
     * Switches the union to the given type and returns a mutable reference to the appropriate field.
     * If the previous type was different, a default constructor will be called first.
     */
    template <typename Tag::Type T>
    inline typename TagToType<T>::StorageType& to();
    % endif
<!--(end)-->

% if t.kind == t.KIND_SERVICE:
    template <int _tmpl>
    struct Request_
    {
        ${indent(generate_primary_body(type_name='Request_', max_bitlen=t.get_max_bitlen_request(), \
                                       fields=t.request_fields, constants=t.request_constants, \
                                       union=t.request_union))}
    };

    template <int _tmpl>
    struct Response_
    {
        ${indent(generate_primary_body(type_name='Response_', max_bitlen=t.get_max_bitlen_response(), \
                                       fields=t.response_fields, constants=t.response_constants, \
                                       union=t.response_union))}
    };

    typedef Request_<0> Request;
    typedef Response_<0> Response;
% else:
    ${generate_primary_body(type_name=t.cpp_type_name, max_bitlen=t.get_max_bitlen(), \
                            fields=t.fields, constants=t.constants, union=t.union)}
% endif

    /*
     * Static type info
     */
    enum { DataTypeKind = ${t.cpp_kind} };
% if t.has_default_dtid:
    enum { DefaultDataTypeID = ${t.default_dtid} };
% else:
    // This type has no default data type ID
% endif

    static const char* getDataTypeFullName()
    {
        return "${t.full_name}";
    }

    static void extendDataTypeSignature(::uavcan::DataTypeSignature& signature)
    {
        signature.extend(getDataTypeSignature());
    }

    static ::uavcan::DataTypeSignature getDataTypeSignature();

% if t.kind == t.KIND_SERVICE:
private:
    ${t.cpp_type_name}(); // Don't create objects of this type. Use Request/Response instead.
% endif
};

/*
 * Out of line struct method definitions
 */
<!--(macro define_out_of_line_struct_methods)--> #! scope_prefix, fields, union

template <int _tmpl>
bool ${scope_prefix}<_tmpl>::operator==(ParameterType rhs) const
{
    % if union:
    if (_tag_ != rhs._tag_)
    {
        return false;
    }
        % for idx,a in enumerate(fields):
    if (_tag_ == ${idx})
    {
        return ${a.name} == rhs.${a.name};
    }
        % endfor
    UAVCAN_ASSERT(0);   // Invalid tag
    return false;
    % else:
        % if fields:
    return
            % for idx,last,a in enum_last_value([x for x in fields if not x.void]):
        ${a.name} == rhs.${a.name}${' &&' if not last else ';'}
            % endfor
        % else:
    (void)rhs;
    return true;
        % endif
    % endif
}

template <int _tmpl>
bool ${scope_prefix}<_tmpl>::isClose(ParameterType rhs) const
{
    % if union:
    if (_tag_ != rhs._tag_)
    {
        return false;
    }
        % for idx,a in enumerate(fields):
    if (_tag_ == ${idx})
    {
        return ::uavcan::areClose(${a.name}, rhs.${a.name});
    }
        % endfor
    UAVCAN_ASSERT(0);   // Invalid tag
    return false;
    % else:
        % if fields:
    return
            % for idx,last,a in enum_last_value([x for x in fields if not x.void]):
        ::uavcan::areClose(${a.name}, rhs.${a.name})${' &&' if not last else ';'}
            % endfor
        % else:
    (void)rhs;
    return true;
        % endif
    % endif
}

    <!--(macro generate_codec_calls_per_field)--> #! call_name, self_parameter_type
template <int _tmpl>
int ${scope_prefix}<_tmpl>::${call_name}(${self_parameter_type} self, ::uavcan::ScalarCodec& codec,
    ::uavcan::TailArrayOptimizationMode tao_mode)
{
    (void)self;
    (void)codec;
    (void)tao_mode;
        % if union:
    const int res = TagType::${call_name}(self._tag_, codec, ::uavcan::TailArrayOptDisabled);
    if (res <= 0)
    {
        return res;
    }
            % for idx,a in enumerate(fields):
    if (self._tag_ == ${idx})
    {
        return FieldTypes::${a.name}::${call_name}(self.${a.name}, codec, tao_mode);
    }
            % endfor
    return -1;          // Invalid tag value
        % else:
            % for a in [x for x in fields if x.void]:
    typename ::uavcan::StorageType< typename FieldTypes::${a.name} >::Type ${a.name} = 0;
            % endfor
    int res = 1;
            % for idx,last,a in enum_last_value(fields):
    res = FieldTypes::${a.name}::${call_name}(${'self.' * (not a.void)}${a.name}, codec, \
${'::uavcan::TailArrayOptDisabled' if not last else 'tao_mode'});
                % if not last:
    if (res <= 0)
    {
        return res;
    }
                % endif
            % endfor
    return res;
        % endif
}
    <!--(end)-->
${generate_codec_calls_per_field(call_name='encode', self_parameter_type='ParameterType')}
${generate_codec_calls_per_field(call_name='decode', self_parameter_type='ReferenceType')}

    % if union:
        % for idx,a in enumerate(fields):
template <>
template <>
struct ${scope_prefix}<0>::TagToType<${scope_prefix}<0>::Tag::${a.name}>
{
    typedef typename ${scope_prefix}<0>::FieldTypes::${a.name} Type;
    typedef typename ::uavcan::StorageType<Type>::Type StorageType;
};

template <>
template <>
inline const typename ${scope_prefix}<0>::TagToType< ${scope_prefix}<0>::Tag::${a.name} >::StorageType*
${scope_prefix}<0>::as< ${scope_prefix}<0>::Tag::${a.name} >() const
{
    return is(${scope_prefix}<0>::Tag::${a.name}) ? &${a.name} : UAVCAN_NULLPTR;
}

template <>
template <>
inline typename ${scope_prefix}<0>::TagToType< ${scope_prefix}<0>::Tag::${a.name} >::StorageType&
${scope_prefix}<0>::to< ${scope_prefix}<0>::Tag::${a.name} >()
{
    if (_tag_ != ${idx})
    {
        _tag_ = ${idx};
        ${a.name} = typename TagToType< ${scope_prefix}<0>::Tag::${a.name} >::StorageType();
    }
    return ${a.name};
}

        % endfor
    % endif
<!--(end)-->

% if t.kind == t.KIND_SERVICE:
${define_out_of_line_struct_methods(scope_prefix=t.cpp_type_name + '::Request_', fields=t.request_fields, \
                                    union=t.request_union)}
${define_out_of_line_struct_methods(scope_prefix=t.cpp_type_name + '::Response_', fields=t.response_fields, \
                                    union=t.response_union)}
% else:
${define_out_of_line_struct_methods(scope_prefix=t.cpp_type_name, fields=t.fields, union=t.union)}
% endif

/*
 * Out of line type method definitions
 */
% if t.kind == t.KIND_SERVICE:
inline ::uavcan::DataTypeSignature ${t.cpp_type_name}::getDataTypeSignature()
% else:
template <int _tmpl>
::uavcan::DataTypeSignature ${t.cpp_type_name}<_tmpl>::getDataTypeSignature()
% endif
{
    ::uavcan::DataTypeSignature signature(${'0x%08X' % t.get_dsdl_signature()}ULL);
<!--(macro extend_signature_per_field)--> #! scope_prefix, fields
    % for a in fields:
    ${scope_prefix}FieldTypes::${a.name}::extendDataTypeSignature(signature);
    % endfor
<!--(end)-->
% if t.kind == t.KIND_SERVICE:
${extend_signature_per_field(scope_prefix='Request::', fields=t.request_fields)}
${extend_signature_per_field(scope_prefix='Response::', fields=t.response_fields)}
% else:
${extend_signature_per_field(scope_prefix='', fields=t.fields)}
% endif
    return signature;
}

/*
 * Out of line constant definitions
 */
<!--(macro define_out_of_line_constants)--> #! scope_prefix, constants
    % for a in constants:
template <int _tmpl>
const typename ::uavcan::StorageType< typename ${scope_prefix}<_tmpl>::ConstantTypes::${a.name} >::Type
    ${scope_prefix}<_tmpl>::${a.name} = ${a.cpp_value}; // ${a.init_expression}

    % endfor
<!--(end)-->
% if t.kind == t.KIND_SERVICE:
${define_out_of_line_constants(scope_prefix=t.cpp_type_name + '::Request_', constants=t.request_constants)}
${define_out_of_line_constants(scope_prefix=t.cpp_type_name + '::Response_', constants=t.response_constants)}
% else:
${define_out_of_line_constants(scope_prefix=t.cpp_type_name, constants=t.constants)}
% endif

/*
 * Final typedef
 */
% if t.kind == t.KIND_SERVICE:
typedef ${t.cpp_type_name} ${t.short_name};
% else:
typedef ${t.cpp_type_name}<0> ${t.short_name};
% endif

% if t.has_default_dtid:
namespace
{

const ::uavcan::DefaultDataTypeRegistrator< ${t.cpp_full_type_name} > _uavcan_gdtr_registrator_${t.short_name};

}
% else:
// No default registration
% endif

% for nsc in t.cpp_namespace_components[::-1]:
} // Namespace ${nsc}
% endfor

/*
 * YAML streamer specialization
 */
namespace uavcan
{

<!--(macro define_yaml_streamer)--> #! type_name, fields, union
template <>
class UAVCAN_EXPORT YamlStreamer< ${type_name} >
{
public:
    template <typename Stream>
    static void stream(Stream& s, ${type_name}::ParameterType obj, const int level);
};

template <typename Stream>
void YamlStreamer< ${type_name} >::stream(Stream& s, ${type_name}::ParameterType obj, const int level)
{
    (void)s;
    (void)obj;
    (void)level;
    % if union:
    if (level > 0)
    {
        s << '\n';
        for (int pos = 0; pos < level; pos++)
        {
            s << "  ";
        }
    }
        % for idx,a in enumerate(fields):
    if (static_cast<int>(obj.getTag()) == ${idx})
    {
        s << "${a.name}: ";
        YamlStreamer< ${type_name}::FieldTypes::${a.name} >::stream(s, obj.${a.name}, level + 1);
    }
        % endfor
    % else:
        % for idx,a in enumerate([x for x in fields if not x.void]):
            % if idx == 0:
    if (level > 0)
    {
        s << '\n';
        for (int pos = 0; pos < level; pos++)
        {
            s << "  ";
        }
    }
            % else:
    s << '\n';
    for (int pos = 0; pos < level; pos++)
    {
        s << "  ";
    }
            % endif
    s << "${a.name}: ";
    YamlStreamer< ${type_name}::FieldTypes::${a.name} >::stream(s, obj.${a.name}, level + 1);
        % endfor
    % endif
}
<!--(end)-->
% if t.kind == t.KIND_SERVICE:
${define_yaml_streamer(type_name=t.cpp_full_type_name + '::Request', fields=t.request_fields, union=t.request_union)}
${define_yaml_streamer(type_name=t.cpp_full_type_name + '::Response', fields=t.response_fields, union=t.response_union)}
% else:
${define_yaml_streamer(type_name=t.cpp_full_type_name, fields=t.fields, union=t.union)}
% endif

}

% for nsc in t.cpp_namespace_components:
namespace ${nsc}
{
% endfor

<!--(macro define_streaming_operator)--> #! type_name
template <typename Stream>
inline Stream& operator<<(Stream& s, ${type_name}::ParameterType obj)
{
    ::uavcan::YamlStreamer< ${type_name} >::stream(s, obj, 0);
    return s;
}
<!--(end)-->
% if t.kind == t.KIND_SERVICE:
${define_streaming_operator(type_name=t.cpp_full_type_name + '::Request')}
${define_streaming_operator(type_name=t.cpp_full_type_name + '::Response')}
% else:
${define_streaming_operator(type_name=t.cpp_full_type_name)}
% endif

% for nsc in t.cpp_namespace_components[::-1]:
} // Namespace ${nsc}
% endfor

#endif // ${t.include_guard}
