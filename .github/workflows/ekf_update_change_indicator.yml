name: EKF Update Change Indicator

on:
  push:
    paths-ignore:
      - 'docs/**'
      - '.github/**'

jobs:
  unit_tests:
    runs-on: ubuntu-latest
    env:
      GIT_COMMITTER_EMAIL: <EMAIL>
      GIT_COMMITTER_NAME: PX4BuildBot
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: main test
      uses: addnab/docker-run-action@v3
      with:
        image: px4io/px4-dev-base-focal:2021-09-08
        options: -v ${{ github.workspace }}:/workspace
        run: |
          cd /workspace
          git config --global --add safe.directory /workspace
          make tests TESTFILTER=EKF

    - name: Check if there exists diff and save result in variable
      id: diff-check
      run: echo "CHANGE_INDICATED=$(git diff --exit-code --output=/dev/null || echo $?)" >> $GITHUB_OUTPUT
      working-directory: src/modules/ekf2/test/change_indication

    - name: auto-commit any changes to change indication
      uses: stefanzweifel/git-auto-commit-action@v4
      with:
        file_pattern: 'src/modules/ekf2/test/change_indication/*.csv'
        commit_user_name: ${GIT_COMMITTER_NAME}
        commit_user_email: ${GIT_COMMITTER_EMAIL}
        commit_message: |
          '[AUTO COMMIT] update change indication'

          See .github/workflopws/ekf_update_change_indicator.yml for more details

    - name: if there is a functional change, fail check
      if: ${{ steps.diff-check.outputs.CHANGE_INDICATED }}
      run: exit 1
