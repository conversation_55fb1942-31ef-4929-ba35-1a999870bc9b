<?xml version="1.0" encoding="UTF-8"?>

<PropertyList>
 <fx>
    <crank>
      <name>engstart</name>
      <path>Sounds/engstart.wav</path>
      <property>/engines/engine/cranking</property>
      <volume>
        <offset>0.4</offset>
      </volume>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </crank>

    <crank>
      <name>crank</name>
      <mode>looped</mode>
      <path>Sounds/cranking.wav</path>
      <condition>
        <property>/engines/engine/cranking</property>
        <not>
          <property>/engines/engine/running</property>
        </not>
      </condition>
      <volume>
        <offset>0.75</offset>
      </volume>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </crank>

    <crank>
      <name>cough</name>
      <path>Sounds/coughing.wav</path>
      <property>/engines/engine/running</property>
      <volume>
        <offset>0.73</offset>
      </volume>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </crank>

    <engine>
      <name>engine</name>
      <mode>looped</mode>
      <path>Sounds/wasp.wav</path>
      <property>/engines/engine/running</property>
      <volume>
        <property>/engines/engine/mp-osi</property>
        <factor>0.0375</factor>
        <min>0.15</min>
        <max>2.5</max>
      </volume>
      <volume>
        <property>/sim/current-view/internal</property>
        <offset>0.75</offset>
        <factor>-0.35</factor>
      </volume>
      <pitch>
        <property>/engines/engine/rpm</property>
        <factor>0.0004</factor>
        <min>0.1</min>
        <max>2.0</max>
        <offset>0.15</offset>
      </pitch>
      <position>
        <x> 2.0 </x>
        <y> 0.0 </y>
        <z> 0.1 </z>
      </position>
      <orientation>
        <x> 0 </x>
        <y> 1 </y>
        <z> 0 </z>
        <inner-angle>  50.0 </inner-angle>
        <outer-angle> 180.0 </outer-angle>
        <outer-gain>    0.3 </outer-gain>
      </orientation>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </engine>
    
    

    <propeller>
      <name>propeller</name>
      <mode>looped</mode>
      <path>Sounds/turboprop1.wav</path>
      <property>/engines/engine/running</property>
      <volume>
        <property>/orientation/alpha-deg</property>
        <type>log</type>
        <min>0.2</min>
        <max>0.5</max>
      </volume>
      <volume>
        <property>/sim/current-view/internal</property>
        <factor>0.5</factor>
      </volume>
      <pitch>
        <property>/engines/engine/mp-osi</property>
        <factor>0.0036</factor>
        <min>0.5</min>
        <max>2.0</max>
        <offset>0.9</offset>
      </pitch>
      <position>
        <x>  2.3 </x>
        <y>  0.0 </y>
        <z> -0.1 </z>
      </position>
      <orientation>
        <x> 1 </x>
        <y> 0 </y>
        <z> 0 </z>
        <inner-angle>  70.0 </inner-angle>
        <outer-angle> 150.0 </outer-angle>
        <outer-gain>    0.2 </outer-gain>
      </orientation>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </propeller>

    <rumble>
      <name>rumble</name>
      <mode>looped</mode>
      <path>Sounds/rumble.wav</path>
      <condition>
        <or>
        <property>/gear/gear[0]/wow</property>
        <property>/gear/gear[1]/wow</property>
        <property>/gear/gear[2]/wow</property>
        </or>
      </condition>
      <volume>
        <property>/velocities/airspeed-kt</property>
        <type>log</type>
        <factor>0.25</factor>
      </volume>
      <position>
        <x> -0.1 </x>
        <y>  0.0 </y>
        <z> -1.0 </z>
      </position>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </rumble>

    <squeal>
      <name>squeal</name>
      <path>Sounds/squeal.wav</path>
      <condition>
        <property>/gear/gear[0]/wow</property>
      </condition>
      <volume>
        <property>/velocities/speed-down-fps</property>
        <factor>0.5</factor>
        <max>1.0</max>
      </volume>
      <volume>
        <property>/velocities/airspeed-kt</property>
        <factor>0.01</factor>
        <max>1.0</max>
      </volume>
      <volume>
        <internal>dt_stop</internal>
        <factor>0.5</factor>
        <max>1.0</max>
      </volume>
      <pitch>
        <property>/velocities/airspeed-kt</property>
        <factor>0.0025</factor>
        <offset>1.2</offset>
      </pitch>
      <position>
        <x> -1.44 </x>
        <y>  0.00 </y>
        <z> -1.00 </z>
      </position>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </squeal>

    <squeal>
      <name>squeal1</name>
      <path>Sounds/squeal.wav</path>
      <condition>
        <property>/gear/gear[1]/wow</property>
      </condition>
      <volume>
        <property>/velocities/speed-down-fps</property>
        <factor>0.5</factor>
        <max>1.0</max>
      </volume>
      <volume>
        <property>/velocities/airspeed-kt</property>
        <factor>0.01</factor>
        <max>1.0</max>
      </volume>
      <volume>
        <internal>dt_stop</internal>
        <factor>0.5</factor>
        <max>1.0</max>
      </volume>
      <pitch>
        <property>/velocities/airspeed-kt</property>
        <factor>0.0025</factor>
        <offset>1.2</offset>
      </pitch>
      <position>
        <x> -0.13 </x>
        <y>  0.88 </y>
        <z> -1.00 </z>
      </position>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </squeal>

    <squeal>
      <name>squeal2</name>
      <path>Sounds/squeal.wav</path>
      <condition>
        <property>/gear/gear[2]/wow</property>
      </condition>
      <volume>
        <property>/velocities/speed-down-fps</property>
        <factor>0.5</factor>
        <max>1.0</max>
      </volume>
      <volume>
        <property>/velocities/airspeed-kt</property>
        <factor>0.01</factor>
        <max>1.0</max>
      </volume>
      <volume>
        <internal>dt_stop</internal>
        <factor>0.5</factor>
        <max>1.0</max>
      </volume>
      <pitch>
        <property>/velocities/airspeed-kt</property>
        <factor>0.0025</factor>
        <offset>1.2</offset>
      </pitch>
      <position>
        <x> -0.13 </x>
        <y> -0.88 </y>
        <z> -1.00 </z>
      </position>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </squeal>

    <wind>
      <name>wind</name>
      <mode>looped</mode>
      <path>Sounds/wind.wav</path>
      <property>/velocities/airspeed-kt</property>
      <volume>
        <property>/position/altitude-ft</property>
        <factor>-0.000015</factor>
        <offset>1.0</offset>
        <min>0.1</min>
        <max>1.0</max>
      </volume>
      <volume>
        <property>/velocities/airspeed-kt</property>
        <factor>0.0015</factor>
        <min>0.03</min>
        <max>0.25</max>
      </volume>
      <pitch>
        <property>/velocities/airspeed-kt</property>
        <factor>0.0035</factor>
        <offset>1.25</offset>
      </pitch>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </wind>


 
    <!-- rotor -->
    <rotor>
      <name>rotor</name>
      <mode>looped</mode>
      <path>Sounds/rotor.wav</path> 
      <condition>
        <and>
          <greater-than>
            <property>rotors/main/rpm</property>
            <value>50</value>
          </greater-than>
          <not>
            <property>sim/crashed</property>
          </not>
        </and>
      </condition>
      <volume>
        <property>rotors/main/rpm</property>
        <factor>0.02</factor>
        <max>0.5</max>
      </volume>
      <pitch>
        <property>rotors/main/rpm</property>
        <factor>0.0015</factor>
        <offset>-0.2</offset>
        <max>1.5</max>
      </pitch>
      <position>
        <x> -1.7 </x>
        <y>  0.0 </y>
        <z>  1.1 </z>
      </position>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </rotor>

    <rotor>
      <name>stall</name>
      <mode>looped</mode>
      <path>Sounds/rotor_stall.wav</path>
      <condition>
        <and>
          <greater-than>
            <property>rotors/main/rpm</property>
            <value>50</value>
          </greater-than>
          <not>
            <property>sim/crashed</property>
          </not>
        </and>
      </condition>
      <volume>
        <property>rotors/main/stall-filtered</property>
        <factor>350</factor>
        <max>1.0</max>
      </volume>
      <pitch>
        <property>rotors/main/rpm</property>
        <factor>0.001131</factor>
        <offset>0</offset>
        <max>1.75</max>
      </pitch>
      <position>
        <x> -1.7 </x>
        <y>  0.0 </y>
        <z>  1.1 </z>
      </position>
      <reference-dist>100</reference-dist>
      <max-dist>1000</max-dist>
    </rotor>

  </fx>

</PropertyList>
