<plugin filename="gz-sim-advanced-lift-drag-system" name="gz::sim::systems::AdvancedLiftDrag">
      <a0></a0>
      <CL0></CL0>
      <AR></AR>
      <eff></eff>
      <CLa></CLa>
      <CD0></CD0>
      <Cem0></Cem0>
      <Cema></Cema>
      <CYb></CYb>
      <Cellb></Cellb>
      <Cenb></Cenb>
      <CDp></CDp>
      <CYp></CYp>
      <CLp></CLp>
      <Cellp></Cellp>
      <Cemp></Cemp>
      <Cenp></Cenp>
      <CDq></CDq>
      <CYq></CYq>
      <CLq></CLq>
      <Cellq></Cellq>
      <Cemq></Cemq>
      <Cenq></Cenq>
      <CDr></CDr>
      <CYr></CYr>
      <CLr></CLr>
      <Cellr></Cellr>
      <Cemr></Cemr>
      <Cenr></Cenr>
      <alpha_stall></alpha_stall>
      <CLa_stall></CLa_stall>
      <CDa_stall></CDa_stall>
      <Cema_stall></Cema_stall>
      <cp></cp>
      <area></area>
      <mac></mac>
      <air_density></air_density>
      <forward></forward>
      <upward></upward>
      <link_name></link_name>
      <num_ctrl_surfaces></num_ctrl_surfaces>
  