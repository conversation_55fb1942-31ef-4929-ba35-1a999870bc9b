<PropertyList>
 <path>gpa.ac</path>
	<offsets>
		<z-m>-0.15</z-m>
	</offsets>
<!-- instruments -->
	<model>
      <path>Aircraft/TF-R1/Models/fuelgauge.xml</path>
      <offsets>
        <x-m>1.3</x-m>
        <y-m>0.01</y-m>
        <z-m>1.18</z-m>
				<pitch-deg>-22</pitch-deg>
      </offsets>
    </model>
	<model>
      <path>Aircraft/TF-R1/Models/speedometer.xml</path>
      <offsets>
        <x-m>1.33</x-m>
        <y-m>0.1</y-m>
        <z-m>1.123</z-m>
				<pitch-deg>-22</pitch-deg>
      </offsets>
    </model>
	<model>
      <path>Aircraft/TF-R1/Models/current.xml</path>
      <offsets>
        <x-m>1.3</x-m>
        <y-m>0.19</y-m>
        <z-m>1.18</z-m>
				<pitch-deg>-22</pitch-deg>
      </offsets>
    </model>


 <animation>
  <type>translate</type>
  <object-name>fwheel.L</object-name>
  <object-name>fwheel2.L</object-name>
  <object-name>fscrews.L</object-name>
  <property>gear/gear[0]/compression-norm</property>
  <interpolation>
   <entry>
    <ind>0.0</ind>
    <dep>0</dep>
   </entry>
   <entry>
    <ind>1.00</ind>
    <dep>0.2</dep>
   </entry>
  </interpolation>
  <axis>
   <x>0</x>
   <y>0</y>
   <z>1</z>
  </axis>
 </animation>
 <animation>
  <type>translate</type>
  <object-name>fwheel.R</object-name>
  <object-name>fwheel2.R</object-name>
  <object-name>fscrews.R</object-name>
  <property>gear/gear[1]/compression-norm</property>
  <interpolation>
   <entry>
    <ind>0.0</ind>
    <dep>0</dep>
   </entry>
   <entry>
    <ind>1.00</ind>
    <dep>0.2</dep>
   </entry>
  </interpolation>
  <axis>
   <x>0</x>
   <y>0</y>
   <z>1</z>
  </axis>
 </animation>
 <animation>
  <type>translate</type>
  <object-name>rwheel.L</object-name>
  <object-name>rwheel2.L</object-name>
  <object-name>rscrews.L</object-name>
  <property>gear/gear[2]/compression-norm</property>
  <interpolation>
   <entry>
    <ind>0.0</ind>
    <dep>0</dep>
   </entry>
   <entry>
    <ind>1.00</ind>
    <dep>0.2</dep>
   </entry>
  </interpolation>
  <axis>
   <x>0</x>
   <y>0</y>
   <z>1</z>
  </axis>
 </animation>
 <animation>
  <type>translate</type>
  <object-name>rwheel.R</object-name>
  <object-name>rwheel2.R</object-name>
  <object-name>rscrews.R</object-name>
  <property>gear/gear[3]/compression-norm</property>
  <interpolation>
   <entry>
    <ind>0.0</ind>
    <dep>0</dep>
   </entry>
   <entry>
    <ind>1.00</ind>
    <dep>0.2</dep>
   </entry>
  </interpolation>
  <axis>
   <x>0</x>
   <y>0</y>
   <z>1</z>
  </axis>
 </animation>
 <animation>
  <type>rotate</type>
  <object-name>fwheel.L</object-name>
  <object-name>fwheel2.L</object-name>
  <object-name>fscrews.L</object-name>
  <property>controls/flight/aileron</property>
  <factor>-25</factor>
  <center>
   <x-m>0.554</x-m>
   <y-m>-0.439</y-m>
   <z-m>0.408</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.0</y>
   <z>1.0</z>
  </axis>
 </animation>
 <animation>
  <type>rotate</type>
  <object-name>fwheel.R</object-name>
  <object-name>fwheel2.R</object-name>
  <object-name>fscrews.R</object-name>
  <property>controls/flight/aileron</property>
  <factor>-25</factor>
  <center>
   <x-m>0.554</x-m>
   <y-m>0.439</y-m>
   <z-m>0.408</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>0.0</y>
   <z>1.0</z>
  </axis>
 </animation>

 <animation>
  <type>spin</type>
  <object-name>fwheel.L</object-name>
  <object-name>fwheel2.L</object-name>
  <object-name>fscrews.L</object-name>
  <property>gear/gear[1]/rollspeed-ms</property>
  <factor>-15</factor>
  <center>
   <x-m>0.554</x-m>
   <y-m>-0.439</y-m>
   <z-m>0.408</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>1.0</y>
   <z>0.0</z>
  </axis>
 </animation>
 <animation>
  <type>spin</type>
  <object-name>fwheel.R</object-name>
  <object-name>fwheel2.R</object-name>
  <object-name>fscrews.R</object-name>
  <property>gear/gear[1]/rollspeed-ms</property>
  <factor>-15</factor>
  <center>
   <x-m>0.554</x-m>
   <y-m>0.439</y-m>
   <z-m>0.408</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>1.0</y>
   <z>0.0</z>
  </axis>
 </animation>
 <animation>
  <type>spin</type>
  <object-name>rwheel.L</object-name>
  <object-name>rwheel2.L</object-name>
  <object-name>rscrews.L</object-name>
  <property>gear/gear[2]/rollspeed-ms</property>
  <factor>-15</factor>
  <center>
   <x-m>2.696</x-m>
   <y-m>-0.746</y-m>
   <z-m>0.408</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>1.0</y>
   <z>0.0</z>
  </axis>
 </animation>
 <animation>
  <type>spin</type>
  <object-name>rwheel.R</object-name>
  <object-name>rwheel2.R</object-name>
  <object-name>rscrews.R</object-name>
  <property>gear/gear[3]/rollspeed-ms</property>
  <factor>-15</factor>
  <center>
   <x-m>2.696</x-m>
   <y-m>-0.746</y-m>
   <z-m>0.408</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>1.0</y>
   <z>0.0</z>
  </axis>
 </animation>
 <animation>
  <type>rotate</type>
  <object-name>steerwheel</object-name>
  <property>controls/flight/aileron</property>
  <factor>-100</factor>
  <center>
   <x-m>1.467</x-m>
   <y-m>-0.331</y-m>
   <z-m>1.008</z-m>
  </center>
  <axis>
   <x1-m>1.185</x1-m>
   <y1-m>-0.331</y1-m>
   <z1-m>0.713</z1-m>
   <x2-m>1.750</x2-m>
   <y2-m>-0.331</y2-m>
   <z2-m>1.303</z2-m>
  </axis>
 </animation>

 <animation>
  <type>rotate</type>
  <object-name>screen</object-name>
  <object-name>wiper.L</object-name>
  <object-name>wiper.R</object-name>
  <property>controls/gear/screen</property>
  <factor>-100</factor>
  <center>
   <x-m>1.014</x-m>
   <y-m>0.0</y-m>
   <z-m>1.175</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>1.0</y>
   <z>0.0</z>
  </axis>
 </animation>
 <animation>
  <type>rotate</type>

  <object-name>shield</object-name>
  <property>controls/gear/shield</property>
  <factor>-165^												</factor>
  <center>
   <x-m>-0.169</x-m>
   <y-m>0.0</y-m>
   <z-m>1.187</z-m>
  </center>
  <axis>
   <x>0.0</x>
   <y>1.0</y>
   <z>0.0</z>
  </axis>
 </animation>

    <model>
      <path>Aircraft/TF-R1/Models/pilot.xml</path>
	  <name>pilot</name>
		<nopreview/>
      <offsets>
        <x-m>2.1</x-m>
        <y-m>-0.32</y-m>
        <z-m>0.9</z-m>
        <pitch-deg>14</pitch-deg>
      </offsets>
    </model>
 <animation>
  <type>select</type>
  <object-name>pilot</object-name>
  <condition>
   <greater-than>
    <property>sim/current-view/view-number</property>
    <value>0.0</value>
   </greater-than>
  </condition>
 </animation>

	<model>
      <path>Aircraft/TF-R1/Models/gpatrans.xml</path>
      <offsets>
        <x-m>0.0</x-m>
        <y-m>0.0</y-m>
        <z-m>0.0</z-m>
      </offsets>
    </model>
</PropertyList>
