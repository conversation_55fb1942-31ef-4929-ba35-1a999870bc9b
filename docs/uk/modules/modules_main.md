# Modules & Commands Reference

На наступних сторінках описано модулі, драйвери та команди PX4.Вони описують надані функціональні можливості, загальний огляд реалізації на високому рівні та способи використання інтерфейсу командного рядка.

:::info
**This is auto-generated from the source code** and contains the most recent modules documentation.
:::

It is not a complete list and NuttX provides some additional commands
as well (such as `free`). Use `help` on the console to get a list of all
available commands, and in most cases `command help` will print the usage.

Since this is generated from source, errors must be reported/fixed
in the [PX4-Autopilot](https://github.com/PX4/PX4-Autopilot) repository.
Сторінки документації можуть бути згенеровані шляхом виконання наступної команди з
кореневого каталогу PX4-Autopilot :

```
make module_documentation
```

The generated files will be written to the `modules` directory.

## Категорії

- [Autotune](modules_autotune.md)
- [Command](modules_command.md)
- [Communication](modules_communication.md)
- [Controller](modules_controller.md)
- [Driver](modules_driver.md)
- [Estimator](modules_estimator.md)
- [Simulation](modules_simulation.md)
- [System](modules_system.md)
- [Template](modules_template.md)
