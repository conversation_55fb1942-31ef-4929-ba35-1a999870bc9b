# Розширене налаштування орієнтації контролера польоту

Ці інструкції можна використовувати для ручного налаштування орієнтації та рівня горизонту, наприклад, для виправлення невеликих відхилень датчиків або незначних помилок під час калібрування.

Якщо існує постійне відхилення (часто спостерігається у багатороторних апаратах, але не обмежується ними), це хороша стратегія виправити його за допомогою цих параметрів зміщення дрібної настройки кута, замість використання ручок регулювання на вашому радіокеруванні.
Це забезпечує, що апарат буде зберігати налаштування під час повністю автономного польоту.

:::info
Ці інструкції є "продвинутими" і не рекомендуються для звичайних користувачів (загальна настройка зазвичай достатня).
:::

## Налаштування параметрів орієнтації

Параметр [SENS_BOARD_ROT](../advanced_config/parameter_reference.md#SENS_BOARD_ROT) визначає обертання плати контролера польоту відносно конструкції транспортного засобу, тоді як дрібна настройка зміщень ([SENS_BOARD_X_OFF](../advanced_config/parameter_reference.md#SENS_BOARD_X_OFF), [SENS_BOARD_Y_OFF](../advanced_config/parameter_reference.md#SENS_BOARD_Y_OFF), [SENS_BOARD_Z_OFF](../advanced_config/parameter_reference.md#SENS_BOARD_Z_OFF)) встановлює обертання датчиків відносно самої плати. Дрібні настройки зміщень додаються до кута `SENS_BOARD_ROT`, щоб визначити загальні кутові зміщення для орієнтації по крену, тангажу та рулону контролера польоту.
Спочатку виконайте звичайну калібрування для [Орієнтації контролера польоту](../config/flight_controller_orientation. md) та [Калібрування горизонту на рівні](../config/level_horizon_calibration. md), щоб встановити параметр [SENS_BOARD_ROT](../advanced_config/parameter_reference. md#SENS_BOARD_ROT).

Спочатку виконайте звичайну калібрування для [Орієнтації контролера польоту](../config/flight_controller_orientation.md) та [Калібрування горизонту на рівні](../config/level_horizon_calibration.md), щоб встановити параметр [SENS_BOARD_ROT](../advanced_config/parameter_reference.md#SENS_BOARD_ROT).

Інші параметри можна встановити для точної настройки орієнтації датчиків ІМПУ відносно самої плати.

Ви можете знайти параметри в QGroundControl, як показано нижче:

1. Відкрийте меню QGroundControl: **Settings > Parameters > Sensor Calibration**.
2. Параметри, розташовані в розділі, як показано нижче (або ви можете знайти їх):

  ![FC Orientation QGC v2](../../assets/qgc/setup/sensor/fc_orientation_qgc_v2.png)

## Підсумок параметра

- [SENS_BOARD_ROT](../advanced_config/parameter_reference.md#SENS_BOARD_ROT): Поворот плати FMU відносно рами транспортного засобу.
- [SENS_BOARD_X_OFF](../advanced_config/parameter_reference.md#SENS_BOARD_X_OFF): Поворот, у градусах, навколо осі X PX4FMU або вісі Крену.
  Позитивні кути збільшуються в протипротивна годинниковій стрілці (CCW) напрямку, від'ємні кути збільшуються в напрямку за годинниковою стрілкою (CW).
- [SENS_BOARD_Y_OFF](../advanced_config/parameter_reference.md#SENS_BOARD_Y_OFF): Поворот, у градусах, навколо осі X PX4FMU або вісі Крену. Позитивні кути збільшуються в протипротивна годинниковій стрілці (CCW) напрямку, від'ємні кути збільшуються в напрямку за годинниковою стрілкою (CW).
  Позитивні кути збільшуються в протипротивна годинниковій стрілці (CCW) напрямку, від'ємні кути збільшуються в напрямку за годинниковою стрілкою (CW).
- [SENS_BOARD_Z_OFF](../advanced_config/parameter_reference.md#SENS_BOARD_Z_OFF): Поворот, у градусах, навколо осі Z PX4FMU або вісі Yaw.
  Позитивні кути збільшуються в протипротивна годинниковій стрілці (CCW) напрямку, від'ємні кути збільшуються в напрямку за годинниковою стрілкою (CW).
