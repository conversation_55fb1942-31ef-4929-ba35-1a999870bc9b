{"nodes": [{"id": "m_fw_autotune_attitude_control", "name": "fw_autotune_attitude_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_autotune_attitude_control", "name": "mc_autotune_attitude_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_landing_target_estimator", "name": "landing_target_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_temperature_compensation", "name": "temperature_compensation", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lightware_laser_serial", "name": "lightware_laser_serial", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pm_selector_auterion", "name": "pm_selector_auterion", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lightware_laser_i2c", "name": "lightware_laser_i2c", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_flight_mode_manager", "name": "flight_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_lat_lon_control", "name": "fw_lat_lon_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mag_bias_estimator", "name": "mag_bias_estimator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_is31fl3195", "name": "rgbled_is31fl3195", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_airspeed_selector", "name": "airspeed_selector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_control_allocator", "name": "control_allocator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_cdcacm_autostart", "name": "cdcacm_autostart", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gyro_calibration", "name": "gyro_calibration", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uxrce_dds_client", "name": "uxrce_dds_client", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vtol_att_control", "name": "vtol_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_ncp5623c", "name": "rgbled_ncp5623c", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_feedback", "name": "camera_feedback", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_mode_manager", "name": "fw_mode_manager", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_rate_control", "name": "fw_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_rate_control", "name": "mc_rate_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_capture", "name": "camera_capture", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_camera_trigger", "name": "camera_trigger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ulanding_radar", "name": "ulanding_radar", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_battery_status", "name": "battery_status", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_fw_att_control", "name": "fw_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_manual_control", "name": "manual_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_att_control", "name": "mc_att_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mc_pos_control", "name": "mc_pos_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled_lp5562", "name": "rgbled_lp5562", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_safety_button", "name": "safety_button", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_land_detector", "name": "land_detector", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_actuator_test", "name": "actuator_test", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_i2c_launcher", "name": "i2c_launcher", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tune_control", "name": "tune_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_esc_battery", "name": "esc_battery", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_led_control", "name": "led_control", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_teraranger", "name": "terar<PERSON>", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_septentrio", "name": "septentrio", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tone_alarm", "name": "tone_alarm", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_send_event", "name": "send_event", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_board_adc", "name": "board_adc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms5525dso", "name": "ms5525dso", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_adis16470", "name": "adis16470", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm42670p", "name": "icm42670p", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm42688p", "name": "icm42688p", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lsm303agr", "name": "lsm303agr", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mmc5983ma", "name": "mmc5983ma", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_commander", "name": "commander", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_navigator", "name": "navigator", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rc_update", "name": "rc_update", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icp201xx", "name": "icp201xx", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms4525do", "name": "ms4525do", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20602", "name": "icm20602", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20649", "name": "icm20649", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm20948", "name": "icm20948", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_icm45686", "name": "icm45686", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_iim42652", "name": "iim42652", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_qmc5883l", "name": "qmc5883l", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vcm1193l", "name": "vcm1193l", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rc_input", "name": "rc_input", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_load_mon", "name": "load_mon", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ads1115", "name": "ads1115", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_cm8jl65", "name": "cm8jl65", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tf02pro", "name": "tf02pro", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vl53l0x", "name": "vl53l0x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_vl53l1x", "name": "vl53l1x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ak09916", "name": "ak09916", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_hmc5883", "name": "hmc5883", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ist8308", "name": "ist8308", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ist8310", "name": "ist8310", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_lis3mdl", "name": "lis3mdl", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_iis2mdc", "name": "iis2mdc", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_msp_osd", "name": "msp_osd", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_pwm_out", "name": "pwm_out", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dataman", "name": "dataman", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_mavlink", "name": "mavlink", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sensors", "name": "sensors", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmp388", "name": "bmp388", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ms5611", "name": "ms5611", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ll40ls", "name": "ll40ls", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_tfmini", "name": "tfmini", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_heater", "name": "heater", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmi088", "name": "bmi088", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rgbled", "name": "rgbled", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ak8963", "name": "ak8963", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_bmm150", "name": "bmm150", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_rm3100", "name": "rm3100", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ina226", "name": "ina226", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ina228", "name": "ina228", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ina238", "name": "ina238", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_uavcan", "name": "uavcan", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gimbal", "name": "gimbal", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_logger", "name": "logger", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_sdp3x", "name": "sdp3x", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_dshot", "name": "dshot", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_px4io", "name": "px4io", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_ekf2", "name": "ekf2", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "m_gps", "name": "gps", "type": "<PERSON><PERSON><PERSON>", "color": "#666666"}, {"id": "t_distance_sensor_mode_change_request", "name": "distance_sensor_mode_change_request", "type": "topic", "color": "#5541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_longitudinal_control_configuration", "name": "longitudinal_control_configuration", "type": "topic", "color": "#8cd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_controller_landing_status", "name": "position_controller_landing_status", "type": "topic", "color": "#8541d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_set_manual_control", "name": "gimbal_manager_set_manual_control", "type": "topic", "color": "#d89a41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_autotune_attitude_control_status", "name": "autotune_attitude_control_status", "type": "topic", "color": "#d84193", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_longitudinal_setpoint", "name": "fixed_wing_longitudinal_setpoint", "type": "topic", "color": "#d84147", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position_setpoint", "name": "vehicle_local_position_setpoint", "type": "topic", "color": "#b641d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_lateral_control_configuration", "name": "lateral_control_configuration", "type": "topic", "color": "#41d847", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_attitude_status", "name": "gimbal_device_attitude_status", "type": "topic", "color": "#8c41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_register_ext_component_reply", "name": "register_ext_component_reply", "type": "topic", "color": "#c4d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fw_virtual_attitude_setpoint", "name": "fw_virtual_attitude_setpoint", "type": "topic", "color": "#41d855", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mc_virtual_attitude_setpoint", "name": "mc_virtual_attitude_setpoint", "type": "topic", "color": "#a141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_set_attitude", "name": "gimbal_manager_set_attitude", "type": "topic", "color": "#41d8bd", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_lateral_setpoint", "name": "fixed_wing_lateral_setpoint", "type": "topic", "color": "#415cd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_set_attitude", "name": "gimbal_device_set_attitude", "type": "topic", "color": "#b6d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_information", "name": "gimbal_manager_information", "type": "topic", "color": "#78d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_controls_status_0", "name": "actuator_controls_status_0", "type": "topic", "color": "#d841b6", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorControlsStatus.msg"}, {"id": "t_vehicle_attitude_setpoint", "name": "vehicle_attitude_setpoint", "type": "topic", "color": "#d89341", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_operator_id", "name": "open_drone_id_operator_id", "type": "topic", "color": "#d8bd41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_fixed_wing_runway_control", "name": "fixed_wing_runway_control", "type": "topic", "color": "#41d1d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_device_information", "name": "gimbal_device_information", "type": "topic", "color": "#417fd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_position_setpoint_triplet", "name": "position_setpoint_triplet", "type": "topic", "color": "#4e41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_selector_status", "name": "estimator_selector_status", "type": "topic", "color": "#6341d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_uavcan_parameter_request", "name": "uavcan_parameter_request", "type": "topic", "color": "#d84741", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tiltrotor_extra_controls", "name": "tiltrotor_extra_controls", "type": "topic", "color": "#d86a41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_control_allocator_status", "name": "control_allocator_status", "type": "topic", "color": "#41d863", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_arm_status", "name": "open_drone_id_arm_status", "type": "topic", "color": "#c441d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_setpoint", "name": "manual_control_setpoint", "type": "topic", "color": "#d87841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_global_position", "name": "vehicle_global_position", "type": "topic", "color": "#41d8a1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_manual_control_switches", "name": "manual_control_switches", "type": "topic", "color": "#41d8ca", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_flight_phase_estimation", "name": "flight_phase_estimation", "type": "topic", "color": "#41a8d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_visual_odometry", "name": "vehicle_visual_odometry", "type": "topic", "color": "#418cd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_failure_detector_status", "name": "failure_detector_status", "type": "topic", "color": "#4163d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_torque_setpoint", "name": "vehicle_torque_setpoint", "type": "topic", "color": "#7141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/VehicleTorqueSetpoint.msg"}, {"id": "t_vehicle_thrust_setpoint", "name": "vehicle_thrust_setpoint", "type": "topic", "color": "#d141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/VehicleThrustSetpoint.msg"}, {"id": "t_launch_detection_status", "name": "launch_detection_status", "type": "topic", "color": "#d841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_rates_setpoint", "name": "vehicle_rates_setpoint", "type": "topic", "color": "#d8ca41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_uavcan_parameter_value", "name": "uavcan_parameter_value", "type": "topic", "color": "#71d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_status_flags", "name": "estimator_status_flags", "type": "topic", "color": "#41d8d1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_local_position", "name": "vehicle_local_position", "type": "topic", "color": "#4147d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_offboard_control_mode", "name": "offboard_control_mode", "type": "topic", "color": "#85d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_manager_status", "name": "gimbal_manager_status", "type": "topic", "color": "#41d84e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_self_id", "name": "open_drone_id_self_id", "type": "topic", "color": "#41d8a8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_differential_pressure", "name": "differential_pressure", "type": "topic", "color": "#4171d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_estimator_sensor_bias", "name": "estimator_sensor_bias", "type": "topic", "color": "#4155d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_land_detected", "name": "vehicle_land_detected", "type": "topic", "color": "#d841a1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_servos_trim", "name": "actuator_servos_trim", "type": "topic", "color": "#d8d141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_control_mode", "name": "vehicle_control_mode", "type": "topic", "color": "#4ed841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_open_drone_id_system", "name": "open_drone_id_system", "type": "topic", "color": "#a841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command_ack", "name": "vehicle_command_ack", "type": "topic", "color": "#d88541", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_figure_eight_status", "name": "figure_eight_status", "type": "topic", "color": "#7fd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_target_pose", "name": "landing_target_pose", "type": "topic", "color": "#41d885", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_constraints", "name": "vehicle_constraints", "type": "topic", "color": "#41cad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vtol_vehicle_status", "name": "vtol_vehicle_status", "type": "topic", "color": "#4185d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_optical_flow", "name": "sensor_optical_flow", "type": "topic", "color": "#6a41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_trajectory_setpoint", "name": "trajectory_setpoint", "type": "topic", "color": "#d8419a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear_wheel", "name": "landing_gear_wheel", "type": "topic", "color": "#d1d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu_status", "name": "vehicle_imu_status", "type": "topic", "color": "#bdd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed_validated", "name": "airspeed_validated", "type": "topic", "color": "#a1d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_power_button_state", "name": "power_button_state", "type": "topic", "color": "#41d8b6", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_transponder_report", "name": "transponder_report", "type": "topic", "color": "#4741d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensors_status_imu", "name": "sensors_status_imu", "type": "topic", "color": "#d84155", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_time_estimate", "name": "rtl_time_estimate", "type": "topic", "color": "#41d88c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_correction", "name": "sensor_correction", "type": "topic", "color": "#41d8c4", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_spoilers_setpoint", "name": "spoilers_setpoint", "type": "topic", "color": "#d841bd", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/NormalizedUnsignedSetpoint.msg"}, {"id": "t_gimbal_v1_command", "name": "gimbal_v1_command", "type": "topic", "color": "#d841a8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mount_orientation", "name": "mount_orientation", "type": "topic", "color": "#d8418c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_navigator_status", "name": "navigator_status", "type": "topic", "color": "#d8af41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_odometry", "name": "vehicle_odometry", "type": "topic", "color": "#a8d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_selection", "name": "sensor_selection", "type": "topic", "color": "#5cd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_outputs", "name": "actuator_outputs", "type": "topic", "color": "#41d8d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ActuatorOutputs.msg"}, {"id": "t_estimator_status", "name": "estimator_status", "type": "topic", "color": "#41a1d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rc_parameter_map", "name": "rc_parameter_map", "type": "topic", "color": "#416ad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_attitude", "name": "vehicle_attitude", "type": "topic", "color": "#414ed8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_telemetry_status", "name": "telemetry_status", "type": "topic", "color": "#d841d1", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_response", "name": "dataman_response", "type": "topic", "color": "#d841c4", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_status", "name": "geofence_status", "type": "topic", "color": "#d85c41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream_ack", "name": "ulog_stream_ack", "type": "topic", "color": "#d88c41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_motors", "name": "actuator_motors", "type": "topic", "color": "#d8a841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_distance_sensor", "name": "distance_sensor", "type": "topic", "color": "#d8b641", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gps_inject_data", "name": "gps_inject_data", "type": "topic", "color": "#d8d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_key_value", "name": "debug_key_value", "type": "topic", "color": "#55d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_command", "name": "vehicle_command", "type": "topic", "color": "#41d85c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_gimbal_controls", "name": "gimbal_controls", "type": "topic", "color": "#41d87f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_dataman_request", "name": "dataman_request", "type": "topic", "color": "#bd41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_combined", "name": "sensor_combined", "type": "topic", "color": "#d84185", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_geofence_result", "name": "geofence_result", "type": "topic", "color": "#d84178", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_battery_status", "name": "battery_status", "type": "topic", "color": "#d85541", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_mission_result", "name": "mission_result", "type": "topic", "color": "#cad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_armed", "name": "actuator_armed", "type": "topic", "color": "#41d878", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_status", "name": "vehicle_status", "type": "topic", "color": "#41d893", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_trigger", "name": "camera_trigger", "type": "topic", "color": "#41d8af", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_takeoff_status", "name": "takeoff_status", "type": "topic", "color": "#41c4d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_flaps_setpoint", "name": "flaps_setpoint", "type": "topic", "color": "#41bdd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/NormalizedUnsignedSetpoint.msg"}, {"id": "t_camera_capture", "name": "camera_capture", "type": "topic", "color": "#419ad8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_satellite_info", "name": "satellite_info", "type": "topic", "color": "#5c41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_failsafe_flags", "name": "failsafe_flags", "type": "topic", "color": "#af41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_action_request", "name": "action_request", "type": "topic", "color": "#d84171", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_irlock_report", "name": "irlock_report", "type": "topic", "color": "#d87f41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_home_position", "name": "home_position", "type": "topic", "color": "#6ad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_safety_button", "name": "safety_button", "type": "topic", "color": "#41d86a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_logger_status", "name": "logger_status", "type": "topic", "color": "#41d89a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_camera_status", "name": "camera_status", "type": "topic", "color": "#4193d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_health_report", "name": "health_report", "type": "topic", "color": "#d8416a", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_actuator_test", "name": "actuator_test", "type": "topic", "color": "#d8414e", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_px4io_status", "name": "px4io_status", "type": "topic", "color": "#d8c441", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_landing_gear", "name": "landing_gear", "type": "topic", "color": "#afd841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_accel", "name": "sensor_accel", "type": "topic", "color": "#9ad841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_system_power", "name": "system_power", "type": "topic", "color": "#63d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_orbit_status", "name": "orbit_status", "type": "topic", "color": "#41b6d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tune_control", "name": "tune_control", "type": "topic", "color": "#ca41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_led_control", "name": "led_control", "type": "topic", "color": "#d84141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_roi", "name": "vehicle_roi", "type": "topic", "color": "#d84e41", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_vehicle_imu", "name": "vehicle_imu", "type": "topic", "color": "#d87141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_baro", "name": "sensor_baro", "type": "topic", "color": "#7841d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_array", "name": "debug_array", "type": "topic", "color": "#9a41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_debug_value", "name": "debug_value", "type": "topic", "color": "#d841ca", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gyro", "name": "sensor_gyro", "type": "topic", "color": "#d841af", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_ulog_stream", "name": "ulog_stream", "type": "topic", "color": "#d8417f", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_tecs_status", "name": "tecs_status", "type": "topic", "color": "#d84163", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_esc_status", "name": "esc_status", "type": "topic", "color": "#d86341", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_gps", "name": "sensor_gps", "type": "topic", "color": "#47d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/SensorGps.msg"}, {"id": "t_debug_vect", "name": "debug_vect", "type": "topic", "color": "#41d871", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_sensor_mag", "name": "sensor_mag", "type": "topic", "color": "#4178d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_adc_report", "name": "adc_report", "type": "topic", "color": "#4141d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_rtl_status", "name": "rtl_status", "type": "topic", "color": "#9341d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_input_rc", "name": "input_rc", "type": "topic", "color": "#41afd8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_airspeed", "name": "airspeed", "type": "topic", "color": "#d8415c", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/EstimatorAidSource1d.msg"}, {"id": "t_mission", "name": "mission", "type": "topic", "color": "#41d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_cpuload", "name": "cpuload", "type": "topic", "color": "#7f41d8", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/no_file.msg"}, {"id": "t_event", "name": "event", "type": "topic", "color": "#93d841", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/ButtonEvent.msg"}, {"id": "t_wind", "name": "wind", "type": "topic", "color": "#d8a141", "url": "https://github.com/PX4/PX4-Autopilot/blob/main/msg/Wind.msg"}], "links": [{"source": "m_ads1115", "target": "t_adc_report", "color": "#4141d8", "style": "dashed"}, {"source": "m_board_adc", "target": "t_system_power", "color": "#63d841", "style": "dashed"}, {"source": "m_board_adc", "target": "t_adc_report", "color": "#4141d8", "style": "dashed"}, {"source": "m_bmp388", "target": "t_sensor_baro", "color": "#7841d8", "style": "dashed"}, {"source": "m_icp201xx", "target": "t_sensor_baro", "color": "#7841d8", "style": "dashed"}, {"source": "m_ms5611", "target": "t_sensor_baro", "color": "#7841d8", "style": "dashed"}, {"source": "m_camera_capture", "target": "t_camera_trigger", "color": "#41d8af", "style": "dashed"}, {"source": "m_camera_capture", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_camera_trigger", "color": "#41d8af", "style": "dashed"}, {"source": "m_camera_trigger", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_ms4525do", "target": "t_differential_pressure", "color": "#4171d8", "style": "dashed"}, {"source": "m_ms5525dso", "target": "t_differential_pressure", "color": "#4171d8", "style": "dashed"}, {"source": "m_sdp3x", "target": "t_differential_pressure", "color": "#4171d8", "style": "dashed"}, {"source": "m_cm8jl65", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_lightware_laser_i2c", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_lightware_laser_serial", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_ll40ls", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_teraranger", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_tf02pro", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_tfmini", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_ulanding_radar", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_vl53l0x", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_vl53l1x", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_outputs", "color": "#41d8d8", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_armed", "color": "#41d878", "style": "dashed"}, {"source": "m_dshot", "target": "t_esc_status", "color": "#d86341", "style": "dashed"}, {"source": "m_dshot", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_test", "color": "#d8414e", "style": "dashed"}, {"source": "m_dshot", "target": "t_actuator_motors", "color": "#d8a841", "style": "dashed"}, {"source": "m_septentrio", "target": "t_sensor_gps", "color": "#47d841", "style": "dashed"}, {"source": "m_septentrio", "target": "t_gps_inject_data", "color": "#d8d841", "style": "dashed"}, {"source": "m_septentrio", "target": "t_satellite_info", "color": "#5c41d8", "style": "dashed"}, {"source": "m_gps", "target": "t_sensor_gps", "color": "#47d841", "style": "dashed"}, {"source": "m_gps", "target": "t_gps_inject_data", "color": "#d8d841", "style": "dashed"}, {"source": "m_gps", "target": "t_satellite_info", "color": "#5c41d8", "style": "dashed"}, {"source": "m_adis16470", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_adis16470", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_bmi088", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_bmi088", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_icm20602", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_icm20602", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_icm20649", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_icm20649", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_icm20948", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_icm42670p", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_icm42670p", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_icm42688p", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_icm42688p", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_icm45686", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_icm45686", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_iim42652", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_iim42652", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_ak09916", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_ak8963", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_bmm150", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_hmc5883", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_ist8308", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_ist8310", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_lis3mdl", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_lsm303agr", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_mmc5983ma", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_qmc5883l", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_rm3100", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_iis2mdc", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_vcm1193l", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_ina226", "target": "t_battery_status", "color": "#d85541", "style": "dashed"}, {"source": "m_ina228", "target": "t_battery_status", "color": "#d85541", "style": "dashed"}, {"source": "m_ina238", "target": "t_battery_status", "color": "#d85541", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_outputs", "color": "#41d8d8", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_armed", "color": "#41d878", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_test", "color": "#d8414e", "style": "dashed"}, {"source": "m_pwm_out", "target": "t_actuator_motors", "color": "#d8a841", "style": "dashed"}, {"source": "m_px4io", "target": "t_led_control", "color": "#d84141", "style": "dashed"}, {"source": "m_px4io", "target": "t_safety_button", "color": "#41d86a", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_outputs", "color": "#41d8d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_tune_control", "color": "#ca41d8", "style": "dashed"}, {"source": "m_px4io", "target": "t_px4io_status", "color": "#d8c441", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_armed", "color": "#41d878", "style": "dashed"}, {"source": "m_px4io", "target": "t_input_rc", "color": "#41afd8", "style": "dashed"}, {"source": "m_px4io", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_px4io", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_test", "color": "#d8414e", "style": "dashed"}, {"source": "m_px4io", "target": "t_actuator_motors", "color": "#d8a841", "style": "dashed"}, {"source": "m_rc_input", "target": "t_input_rc", "color": "#41afd8", "style": "dashed"}, {"source": "m_rc_input", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_rc_input", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_safety_button", "target": "t_led_control", "color": "#d84141", "style": "dashed"}, {"source": "m_safety_button", "target": "t_safety_button", "color": "#41d86a", "style": "dashed"}, {"source": "m_safety_button", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_safety_button", "target": "t_tune_control", "color": "#ca41d8", "style": "dashed"}, {"source": "m_tone_alarm", "target": "t_tune_control", "color": "#ca41d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_led_control", "color": "#d84141", "style": "dashed"}, {"source": "m_uavcan", "target": "t_safety_button", "color": "#41d86a", "style": "dashed"}, {"source": "m_uavcan", "target": "t_open_drone_id_arm_status", "color": "#c441d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_outputs", "color": "#41d8d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_uavcan_parameter_value", "color": "#71d841", "style": "dashed"}, {"source": "m_uavcan", "target": "t_tune_control", "color": "#ca41d8", "style": "dashed"}, {"source": "m_uavcan", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_armed", "color": "#41d878", "style": "dashed"}, {"source": "m_uavcan", "target": "t_esc_status", "color": "#d86341", "style": "dashed"}, {"source": "m_uavcan", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_uavcan", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_test", "color": "#d8414e", "style": "dashed"}, {"source": "m_uavcan", "target": "t_actuator_motors", "color": "#d8a841", "style": "dashed"}, {"source": "m_airspeed_selector", "target": "t_airspeed_validated", "color": "#a1d841", "style": "dashed"}, {"source": "m_battery_status", "target": "t_battery_status", "color": "#d85541", "style": "dashed"}, {"source": "m_camera_feedback", "target": "t_camera_capture", "color": "#419ad8", "style": "dashed"}, {"source": "m_commander", "target": "t_led_control", "color": "#d84141", "style": "dashed"}, {"source": "m_commander", "target": "t_failure_detector_status", "color": "#4163d8", "style": "dashed"}, {"source": "m_commander", "target": "t_power_button_state", "color": "#41d8b6", "style": "dashed"}, {"source": "m_commander", "target": "t_tune_control", "color": "#ca41d8", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_armed", "color": "#41d878", "style": "dashed"}, {"source": "m_commander", "target": "t_event", "color": "#93d841", "style": "dashed"}, {"source": "m_commander", "target": "t_home_position", "color": "#6ad841", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_control_mode", "color": "#4ed841", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_commander", "target": "t_vehicle_status", "color": "#41d893", "style": "dashed"}, {"source": "m_commander", "target": "t_health_report", "color": "#d8416a", "style": "dashed"}, {"source": "m_commander", "target": "t_actuator_test", "color": "#d8414e", "style": "dashed"}, {"source": "m_commander", "target": "t_failsafe_flags", "color": "#af41d8", "style": "dashed"}, {"source": "m_commander", "target": "t_register_ext_component_reply", "color": "#c4d841", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_servos_trim", "color": "#d8d141", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_control_allocator_status", "color": "#41d863", "style": "dashed"}, {"source": "m_control_allocator", "target": "t_actuator_motors", "color": "#d8a841", "style": "dashed"}, {"source": "m_dataman", "target": "t_dataman_response", "color": "#d841c4", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_ekf2", "target": "t_wind", "color": "#d8a141", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_sensor_bias", "color": "#4155d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_attitude", "color": "#414ed8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_global_position", "color": "#41d8a1", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_odometry", "color": "#a8d841", "style": "dashed"}, {"source": "m_ekf2", "target": "t_vehicle_local_position", "color": "#4147d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_selector_status", "color": "#6341d8", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status_flags", "color": "#41d8d1", "style": "dashed"}, {"source": "m_ekf2", "target": "t_sensor_selection", "color": "#5cd841", "style": "dashed"}, {"source": "m_ekf2", "target": "t_estimator_status", "color": "#41a1d8", "style": "dashed"}, {"source": "m_esc_battery", "target": "t_battery_status", "color": "#d85541", "style": "dashed"}, {"source": "m_send_event", "target": "t_led_control", "color": "#d84141", "style": "dashed"}, {"source": "m_send_event", "target": "t_tune_control", "color": "#ca41d8", "style": "dashed"}, {"source": "m_send_event", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_vehicle_constraints", "color": "#41cad8", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_trajectory_setpoint", "color": "#d8419a", "style": "dashed"}, {"source": "m_flight_mode_manager", "target": "t_landing_gear", "color": "#afd841", "style": "dashed"}, {"source": "m_fw_att_control", "target": "t_landing_gear_wheel", "color": "#d1d841", "style": "dashed"}, {"source": "m_fw_att_control", "target": "t_vehicle_rates_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_fw_autotune_attitude_control", "target": "t_autotune_attitude_control_status", "color": "#d84193", "style": "dashed"}, {"source": "m_fw_lat_lon_control", "target": "t_flight_phase_estimation", "color": "#41a8d8", "style": "dashed"}, {"source": "m_fw_lat_lon_control", "target": "t_tecs_status", "color": "#d84163", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_landing_gear", "color": "#afd841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_runway_control", "color": "#41d1d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_lateral_setpoint", "color": "#415cd8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_orbit_status", "color": "#41b6d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_launch_detection_status", "color": "#d841d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_flaps_setpoint", "color": "#41bdd8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_lateral_control_configuration", "color": "#41d847", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_position_controller_landing_status", "color": "#8541d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_longitudinal_control_configuration", "color": "#8cd841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_vehicle_local_position_setpoint", "color": "#b641d8", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_fixed_wing_longitudinal_setpoint", "color": "#d84147", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_figure_eight_status", "color": "#7fd841", "style": "dashed"}, {"source": "m_fw_mode_manager", "target": "t_spoilers_setpoint", "color": "#d841bd", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_spoilers_setpoint", "color": "#d841bd", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_flaps_setpoint", "color": "#41bdd8", "style": "dashed"}, {"source": "m_fw_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_device_set_attitude", "color": "#b6d841", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_v1_command", "color": "#d841a8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_manager_information", "color": "#78d841", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_controls", "color": "#41d87f", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_manager_status", "color": "#41d84e", "style": "dashed"}, {"source": "m_gimbal", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_gimbal", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_gimbal", "target": "t_gimbal_device_attitude_status", "color": "#8c41d8", "style": "dashed"}, {"source": "m_gimbal", "target": "t_mount_orientation", "color": "#d8418c", "style": "dashed"}, {"source": "m_land_detector", "target": "t_vehicle_land_detected", "color": "#d841a1", "style": "dashed"}, {"source": "m_landing_target_estimator", "target": "t_landing_target_pose", "color": "#41d885", "style": "dashed"}, {"source": "m_load_mon", "target": "t_cpuload", "color": "#7f41d8", "style": "dashed"}, {"source": "m_logger", "target": "t_ulog_stream", "color": "#d8417f", "style": "dashed"}, {"source": "m_logger", "target": "t_logger_status", "color": "#41d89a", "style": "dashed"}, {"source": "m_logger", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_manual_control", "target": "t_landing_gear", "color": "#afd841", "style": "dashed"}, {"source": "m_manual_control", "target": "t_action_request", "color": "#d84171", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_setpoint", "color": "#d87841", "style": "dashed"}, {"source": "m_manual_control", "target": "t_manual_control_switches", "color": "#41d8ca", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_manual_control", "target": "t_vehicle_status", "color": "#41d893", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gps", "color": "#47d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_uavcan_parameter_request", "color": "#d84741", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mission", "color": "#41d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_visual_odometry", "color": "#418cd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_dataman_request", "color": "#bd41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_key_value", "color": "#55d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_tune_control", "color": "#ca41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_battery_status", "color": "#d85541", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_information", "color": "#417fd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_fw_virtual_attitude_setpoint", "color": "#41d855", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_value", "color": "#d841ca", "style": "dashed"}, {"source": "m_mavlink", "target": "t_irlock_report", "color": "#d87f41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_mavlink", "target": "t_telemetry_status", "color": "#d841d1", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_mag", "color": "#4178d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_mavlink", "target": "t_differential_pressure", "color": "#4171d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_ulog_stream_ack", "color": "#d88c41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude_setpoint", "color": "#d89341", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_manager_set_manual_control", "color": "#d89a41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_rc_parameter_map", "color": "#416ad8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_vect", "color": "#41d871", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_gyro", "color": "#d841af", "style": "dashed"}, {"source": "m_mavlink", "target": "t_distance_sensor", "color": "#d8b641", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_operator_id", "color": "#d8bd41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_landing_target_pose", "color": "#41d885", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_rates_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_mavlink", "target": "t_trajectory_setpoint", "color": "#d8419a", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gps_inject_data", "color": "#d8d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_attitude", "color": "#414ed8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_self_id", "color": "#41d8a8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_global_position", "color": "#41d8a1", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_manager_set_attitude", "color": "#41d8bd", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_accel", "color": "#9ad841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_event", "color": "#93d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_vehicle_local_position", "color": "#4147d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_transponder_report", "color": "#4741d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_offboard_control_mode", "color": "#85d841", "style": "dashed"}, {"source": "m_mavlink", "target": "t_airspeed", "color": "#d8415c", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_optical_flow", "color": "#6a41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_sensor_baro", "color": "#7841d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_input_rc", "color": "#41afd8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_mc_virtual_attitude_setpoint", "color": "#a141d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_gimbal_device_attitude_status", "color": "#8c41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_debug_array", "color": "#9a41d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_open_drone_id_system", "color": "#a841d8", "style": "dashed"}, {"source": "m_mavlink", "target": "t_camera_status", "color": "#4193d8", "style": "dashed"}, {"source": "m_mc_att_control", "target": "t_vehicle_rates_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_mc_autotune_attitude_control", "target": "t_autotune_attitude_control_status", "color": "#d84193", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_constraints", "color": "#41cad8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_takeoff_status", "color": "#41c4d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_local_position_setpoint", "color": "#b641d8", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_trajectory_setpoint", "color": "#d8419a", "style": "dashed"}, {"source": "m_mc_pos_control", "target": "t_vehicle_attitude_setpoint", "color": "#d89341", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_vehicle_rates_setpoint", "color": "#d8ca41", "style": "dashed"}, {"source": "m_mc_rate_control", "target": "t_actuator_controls_status_0", "color": "#d841b6", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission", "color": "#41d841", "style": "dashed"}, {"source": "m_navigator", "target": "t_dataman_request", "color": "#bd41d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_roi", "color": "#d84e41", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_status", "color": "#d85c41", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_navigator", "target": "t_navigator_status", "color": "#d8af41", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_land_detected", "color": "#d841a1", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_time_estimate", "color": "#41d88c", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_status", "color": "#41d893", "style": "dashed"}, {"source": "m_navigator", "target": "t_mission_result", "color": "#cad841", "style": "dashed"}, {"source": "m_navigator", "target": "t_vehicle_global_position", "color": "#41d8a1", "style": "dashed"}, {"source": "m_navigator", "target": "t_geofence_result", "color": "#d84178", "style": "dashed"}, {"source": "m_navigator", "target": "t_transponder_report", "color": "#4741d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_position_setpoint_triplet", "color": "#4e41d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_distance_sensor_mode_change_request", "color": "#5541d8", "style": "dashed"}, {"source": "m_navigator", "target": "t_home_position", "color": "#6ad841", "style": "dashed"}, {"source": "m_navigator", "target": "t_rtl_status", "color": "#9341d8", "style": "dashed"}, {"source": "m_rc_update", "target": "t_manual_control_switches", "color": "#41d8ca", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_combined", "color": "#d84185", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu_status", "color": "#bdd841", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensors_status_imu", "color": "#d84155", "style": "dashed"}, {"source": "m_sensors", "target": "t_vehicle_imu", "color": "#d87141", "style": "dashed"}, {"source": "m_sensors", "target": "t_sensor_selection", "color": "#5cd841", "style": "dashed"}, {"source": "m_sensors", "target": "t_differential_pressure", "color": "#4171d8", "style": "dashed"}, {"source": "m_sensors", "target": "t_airspeed", "color": "#d8415c", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_led_control", "color": "#d84141", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_sensor_correction", "color": "#41d8c4", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_temperature_compensation", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_uxrce_dds_client", "target": "t_vehicle_command", "color": "#41d85c", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vtol_vehicle_status", "color": "#4185d8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_thrust_setpoint", "color": "#d141d8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_flaps_setpoint", "color": "#41bdd8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_torque_setpoint", "color": "#7141d8", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_tiltrotor_extra_controls", "color": "#d86a41", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_command_ack", "color": "#d88541", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_vehicle_attitude_setpoint", "color": "#d89341", "style": "dashed"}, {"source": "m_vtol_att_control", "target": "t_spoilers_setpoint", "color": "#d841bd", "style": "dashed"}, {"source": "m_actuator_test", "target": "t_actuator_test", "color": "#d8414e", "style": "dashed"}, {"source": "m_led_control", "target": "t_led_control", "color": "#d84141", "style": "dashed"}, {"source": "m_tune_control", "target": "t_tune_control", "color": "#ca41d8", "style": "dashed"}, {"source": "t_adc_report", "target": "m_board_adc", "color": "#4141d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_camera_capture", "color": "#41d85c", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_camera_trigger", "color": "#4147d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_camera_trigger", "color": "#41d85c", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_cdcacm_autostart", "color": "#41d878", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_lightware_laser_i2c", "color": "#41d893", "style": "normal"}, {"source": "t_distance_sensor_mode_change_request", "target": "m_lightware_laser_i2c", "color": "#5541d8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_dshot", "color": "#afd841", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_dshot", "color": "#41d878", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_dshot", "color": "#41d87f", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_dshot", "color": "#d8d141", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_dshot", "color": "#41d85c", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_dshot", "color": "#d87841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_dshot", "color": "#d1d841", "style": "normal"}, {"source": "t_actuator_test", "target": "m_dshot", "color": "#d8414e", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_dshot", "color": "#d8a841", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_septentrio", "color": "#d8d841", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_gps", "color": "#d8d841", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_heater", "color": "#9ad841", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled", "color": "#d84141", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_is31fl3195", "color": "#d84141", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_lp5562", "color": "#d84141", "style": "normal"}, {"source": "t_led_control", "target": "m_rgbled_ncp5623c", "color": "#d84141", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_msp_osd", "color": "#414ed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_msp_osd", "color": "#41d8a1", "style": "normal"}, {"source": "t_battery_status", "target": "m_msp_osd", "color": "#d85541", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_msp_osd", "color": "#a1d841", "style": "normal"}, {"source": "t_input_rc", "target": "m_msp_osd", "color": "#41afd8", "style": "normal"}, {"source": "t_home_position", "target": "m_msp_osd", "color": "#6ad841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_msp_osd", "color": "#4147d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_msp_osd", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ina226", "color": "#41d893", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_ina226", "color": "#41a8d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ina228", "color": "#41d893", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_ina228", "color": "#41a8d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ina238", "color": "#41d893", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_ina238", "color": "#41a8d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pm_selector_auterion", "color": "#41d878", "style": "normal"}, {"source": "t_landing_gear", "target": "m_pwm_out", "color": "#afd841", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_pwm_out", "color": "#41d878", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_pwm_out", "color": "#41d87f", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_pwm_out", "color": "#d8d141", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_pwm_out", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_pwm_out", "color": "#41d85c", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_pwm_out", "color": "#d1d841", "style": "normal"}, {"source": "t_actuator_test", "target": "m_pwm_out", "color": "#d8414e", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_pwm_out", "color": "#d8a841", "style": "normal"}, {"source": "t_landing_gear", "target": "m_px4io", "color": "#afd841", "style": "normal"}, {"source": "t_px4io_status", "target": "m_px4io", "color": "#d8c441", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_px4io", "color": "#41d878", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_px4io", "color": "#41d87f", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_px4io", "color": "#d8d141", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_px4io", "color": "#41d85c", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_px4io", "color": "#41d893", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_px4io", "color": "#d87841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_px4io", "color": "#d1d841", "style": "normal"}, {"source": "t_actuator_test", "target": "m_px4io", "color": "#d8414e", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_px4io", "color": "#d8a841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_rc_input", "color": "#414ed8", "style": "normal"}, {"source": "t_battery_status", "target": "m_rc_input", "color": "#d85541", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_rc_input", "color": "#41d893", "style": "normal"}, {"source": "t_adc_report", "target": "m_rc_input", "color": "#4141d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_rc_input", "color": "#41d85c", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_safety_button", "color": "#41d878", "style": "normal"}, {"source": "t_tune_control", "target": "m_tone_alarm", "color": "#ca41d8", "style": "normal"}, {"source": "t_led_control", "target": "m_uavcan", "color": "#d84141", "style": "normal"}, {"source": "t_uavcan_parameter_request", "target": "m_uavcan", "color": "#d84741", "style": "normal"}, {"source": "t_tune_control", "target": "m_uavcan", "color": "#ca41d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_uavcan", "color": "#41d85c", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_uavcan", "color": "#d87841", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_uavcan", "color": "#d8a841", "style": "normal"}, {"source": "t_open_drone_id_operator_id", "target": "m_uavcan", "color": "#d8bd41", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_uavcan", "color": "#41d878", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_uavcan", "color": "#d841a1", "style": "normal"}, {"source": "t_gimbal_controls", "target": "m_uavcan", "color": "#41d87f", "style": "normal"}, {"source": "t_actuator_servos_trim", "target": "m_uavcan", "color": "#d8d141", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_uavcan", "color": "#41d893", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_uavcan", "color": "#d8d841", "style": "normal"}, {"source": "t_landing_gear_wheel", "target": "m_uavcan", "color": "#d1d841", "style": "normal"}, {"source": "t_open_drone_id_self_id", "target": "m_uavcan", "color": "#41d8a8", "style": "normal"}, {"source": "t_landing_gear", "target": "m_uavcan", "color": "#afd841", "style": "normal"}, {"source": "t_open_drone_id_system", "target": "m_uavcan", "color": "#a841d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_uavcan", "color": "#4147d8", "style": "normal"}, {"source": "t_home_position", "target": "m_uavcan", "color": "#6ad841", "style": "normal"}, {"source": "t_actuator_test", "target": "m_uavcan", "color": "#d8414e", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_airspeed_selector", "color": "#6341d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_airspeed_selector", "color": "#414ed8", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_airspeed_selector", "color": "#d141d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_airspeed_selector", "color": "#d841a1", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_airspeed_selector", "color": "#d841d8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_airspeed_selector", "color": "#4147d8", "style": "normal"}, {"source": "t_tecs_status", "target": "m_airspeed_selector", "color": "#d84163", "style": "normal"}, {"source": "t_estimator_status", "target": "m_airspeed_selector", "color": "#41a1d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_airspeed_selector", "color": "#41d893", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_airspeed_selector", "color": "#41a8d8", "style": "normal"}, {"source": "t_airspeed", "target": "m_airspeed_selector", "color": "#d8415c", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_battery_status", "color": "#41d893", "style": "normal"}, {"source": "t_adc_report", "target": "m_battery_status", "color": "#4141d8", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_battery_status", "color": "#41a8d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_camera_feedback", "color": "#414ed8", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_camera_feedback", "color": "#8c41d8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_camera_feedback", "color": "#41d8a1", "style": "normal"}, {"source": "t_camera_trigger", "target": "m_camera_feedback", "color": "#41d8af", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_commander", "color": "#47d841", "style": "normal"}, {"source": "t_vtol_vehicle_status", "target": "m_commander", "color": "#4185d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_commander", "color": "#d85541", "style": "normal"}, {"source": "t_telemetry_status", "target": "m_commander", "color": "#d841d1", "style": "normal"}, {"source": "t_esc_status", "target": "m_commander", "color": "#d86341", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_commander", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_commander", "color": "#41d85c", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_commander", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_commander", "color": "#d88541", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_commander", "color": "#4171d8", "style": "normal"}, {"source": "t_wind", "target": "m_commander", "color": "#d8a141", "style": "normal"}, {"source": "t_actuator_motors", "target": "m_commander", "color": "#d8a841", "style": "normal"}, {"source": "t_safety_button", "target": "m_commander", "color": "#41d86a", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_commander", "color": "#d841af", "style": "normal"}, {"source": "t_navigator_status", "target": "m_commander", "color": "#d8af41", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_commander", "color": "#d8b641", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_commander", "color": "#41d878", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_commander", "color": "#d841a1", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_commander", "color": "#41d88c", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_commander", "color": "#41d893", "style": "normal"}, {"source": "t_mission_result", "target": "m_commander", "color": "#cad841", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_commander", "color": "#4155d8", "style": "normal"}, {"source": "t_logger_status", "target": "m_commander", "color": "#41d89a", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_commander", "color": "#414ed8", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_commander", "color": "#bdd841", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_commander", "color": "#41d8a1", "style": "normal"}, {"source": "t_power_button_state", "target": "m_commander", "color": "#41d8b6", "style": "normal"}, {"source": "t_geofence_result", "target": "m_commander", "color": "#d84178", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_commander", "color": "#a1d841", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_commander", "color": "#9ad841", "style": "normal"}, {"source": "t_action_request", "target": "m_commander", "color": "#d84171", "style": "normal"}, {"source": "t_event", "target": "m_commander", "color": "#93d841", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_commander", "color": "#41d8c4", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_commander", "color": "#4147d8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_commander", "color": "#41d8ca", "style": "normal"}, {"source": "t_offboard_control_mode", "target": "m_commander", "color": "#85d841", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_commander", "color": "#6341d8", "style": "normal"}, {"source": "t_estimator_status_flags", "target": "m_commander", "color": "#41d8d1", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_commander", "color": "#7841d8", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_commander", "color": "#d84155", "style": "normal"}, {"source": "t_system_power", "target": "m_commander", "color": "#63d841", "style": "normal"}, {"source": "t_cpuload", "target": "m_commander", "color": "#7f41d8", "style": "normal"}, {"source": "t_home_position", "target": "m_commander", "color": "#6ad841", "style": "normal"}, {"source": "t_estimator_status", "target": "m_commander", "color": "#41a1d8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_commander", "color": "#5cd841", "style": "normal"}, {"source": "t_failure_detector_status", "target": "m_control_allocator", "color": "#4163d8", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_control_allocator", "color": "#d141d8", "style": "normal"}, {"source": "t_flaps_setpoint", "target": "m_control_allocator", "color": "#41bdd8", "style": "normal"}, {"source": "t_vehicle_torque_setpoint", "target": "m_control_allocator", "color": "#7141d8", "style": "normal"}, {"source": "t_tiltrotor_extra_controls", "target": "m_control_allocator", "color": "#d86a41", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_control_allocator", "color": "#41d893", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_control_allocator", "color": "#41d8ca", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_control_allocator", "color": "#4ed841", "style": "normal"}, {"source": "t_spoilers_setpoint", "target": "m_control_allocator", "color": "#d841bd", "style": "normal"}, {"source": "t_dataman_request", "target": "m_dataman", "color": "#bd41d8", "style": "normal"}, {"source": "t_sensor_combined", "target": "m_ekf2", "color": "#d84185", "style": "normal"}, {"source": "t_vehicle_visual_odometry", "target": "m_ekf2", "color": "#418cd8", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_ekf2", "color": "#d8b641", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_ekf2", "color": "#41d885", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_ekf2", "color": "#a1d841", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_ekf2", "color": "#d841a1", "style": "normal"}, {"source": "t_sensors_status_imu", "target": "m_ekf2", "color": "#d84155", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_ekf2", "color": "#d841d8", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_ekf2", "color": "#d87141", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_ekf2", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_ekf2", "color": "#41d85c", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_ekf2", "color": "#5cd841", "style": "normal"}, {"source": "t_airspeed", "target": "m_ekf2", "color": "#d8415c", "style": "normal"}, {"source": "t_esc_status", "target": "m_esc_battery", "color": "#d86341", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_esc_battery", "color": "#41d893", "style": "normal"}, {"source": "t_flight_phase_estimation", "target": "m_esc_battery", "color": "#41a8d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_send_event", "color": "#d85541", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_send_event", "color": "#41d893", "style": "normal"}, {"source": "t_cpuload", "target": "m_send_event", "color": "#7f41d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_send_event", "color": "#41d85c", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_send_event", "color": "#af41d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_flight_mode_manager", "color": "#d841a1", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_flight_mode_manager", "color": "#41c4d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_flight_mode_manager", "color": "#41d85c", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_flight_mode_manager", "color": "#4147d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_flight_mode_manager", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_flight_mode_manager", "color": "#d89341", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_flight_mode_manager", "color": "#4ed841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_att_control", "color": "#414ed8", "style": "normal"}, {"source": "t_fixed_wing_runway_control", "target": "m_fw_att_control", "color": "#41d1d8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_att_control", "color": "#d841a1", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_att_control", "color": "#a1d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_att_control", "color": "#4147d8", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_fw_att_control", "color": "#d84193", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_att_control", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_att_control", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_fw_att_control", "color": "#d89341", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_att_control", "color": "#4ed841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_autotune_attitude_control", "color": "#41d893", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_autotune_attitude_control", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_lat_lon_control", "color": "#414ed8", "style": "normal"}, {"source": "t_fixed_wing_lateral_setpoint", "target": "m_fw_lat_lon_control", "color": "#415cd8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_lat_lon_control", "color": "#d841a1", "style": "normal"}, {"source": "t_flaps_setpoint", "target": "m_fw_lat_lon_control", "color": "#41bdd8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_lat_lon_control", "color": "#a1d841", "style": "normal"}, {"source": "t_lateral_control_configuration", "target": "m_fw_lat_lon_control", "color": "#41d847", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_lat_lon_control", "color": "#4147d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_lat_lon_control", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_lat_lon_control", "color": "#4ed841", "style": "normal"}, {"source": "t_longitudinal_control_configuration", "target": "m_fw_lat_lon_control", "color": "#8cd841", "style": "normal"}, {"source": "t_fixed_wing_longitudinal_setpoint", "target": "m_fw_lat_lon_control", "color": "#d84147", "style": "normal"}, {"source": "t_wind", "target": "m_fw_lat_lon_control", "color": "#d8a141", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_fw_mode_manager", "color": "#414ed8", "style": "normal"}, {"source": "t_wind", "target": "m_fw_mode_manager", "color": "#d8a141", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_fw_mode_manager", "color": "#41d8a1", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_mode_manager", "color": "#d841a1", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_mode_manager", "color": "#a1d841", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_fw_mode_manager", "color": "#4147d8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_mode_manager", "color": "#d87841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_fw_mode_manager", "color": "#d8419a", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_fw_mode_manager", "color": "#41d85c", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_fw_mode_manager", "color": "#4e41d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_mode_manager", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_mode_manager", "color": "#4ed841", "style": "normal"}, {"source": "t_battery_status", "target": "m_fw_rate_control", "color": "#d85541", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_fw_rate_control", "color": "#d841a1", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_fw_rate_control", "color": "#a1d841", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_fw_rate_control", "color": "#d8ca41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_fw_rate_control", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_fw_rate_control", "color": "#41d893", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_fw_rate_control", "color": "#41d863", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_fw_rate_control", "color": "#4ed841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_gimbal", "color": "#414ed8", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_gimbal", "color": "#41d8a1", "style": "normal"}, {"source": "t_vehicle_roi", "target": "m_gimbal", "color": "#d84e41", "style": "normal"}, {"source": "t_gimbal_manager_set_attitude", "target": "m_gimbal", "color": "#41d8bd", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_gimbal", "color": "#d841a1", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_gimbal", "color": "#417fd8", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_gimbal", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_gimbal", "color": "#41d85c", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_gimbal", "color": "#8c41d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_gimbal", "color": "#4e41d8", "style": "normal"}, {"source": "t_gimbal_manager_set_manual_control", "target": "m_gimbal", "color": "#d89a41", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_gyro_calibration", "color": "#41d8c4", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_gyro_calibration", "color": "#41d893", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_gyro_calibration", "color": "#d841af", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_gyro_calibration", "color": "#9ad841", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_land_detector", "color": "#bdd841", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_land_detector", "color": "#41d8a1", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_land_detector", "color": "#d141d8", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_land_detector", "color": "#41d878", "style": "normal"}, {"source": "t_takeoff_status", "target": "m_land_detector", "color": "#41c4d8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_land_detector", "color": "#a1d841", "style": "normal"}, {"source": "t_launch_detection_status", "target": "m_land_detector", "color": "#d841d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_land_detector", "color": "#4e41d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_land_detector", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_land_detector", "color": "#4147d8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_land_detector", "color": "#5cd841", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_land_detector", "color": "#d8419a", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_land_detector", "color": "#4ed841", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_landing_target_estimator", "color": "#414ed8", "style": "normal"}, {"source": "t_irlock_report", "target": "m_landing_target_estimator", "color": "#d87f41", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_landing_target_estimator", "color": "#4147d8", "style": "normal"}, {"source": "t_battery_status", "target": "m_logger", "color": "#d85541", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_logger", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_logger", "color": "#41d85c", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_logger", "color": "#41d893", "style": "normal"}, {"source": "t_ulog_stream_ack", "target": "m_logger", "color": "#d88c41", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mag_bias_estimator", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mag_bias_estimator", "color": "#41d893", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_manual_control", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_manual_control", "color": "#41d893", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_manual_control", "color": "#41d8ca", "style": "normal"}, {"source": "t_action_request", "target": "m_manual_control", "color": "#d84171", "style": "normal"}, {"source": "t_battery_status", "target": "m_mavlink", "color": "#d85541", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_mavlink", "color": "#d87141", "style": "normal"}, {"source": "t_esc_status", "target": "m_mavlink", "color": "#d86341", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mavlink", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_mavlink", "color": "#d88541", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mavlink", "color": "#d89341", "style": "normal"}, {"source": "t_wind", "target": "m_mavlink", "color": "#d8a141", "style": "normal"}, {"source": "t_distance_sensor", "target": "m_mavlink", "color": "#d8b641", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mavlink", "color": "#d8ca41", "style": "normal"}, {"source": "t_gps_inject_data", "target": "m_mavlink", "color": "#d8d841", "style": "normal"}, {"source": "t_mission_result", "target": "m_mavlink", "color": "#cad841", "style": "normal"}, {"source": "t_register_ext_component_reply", "target": "m_mavlink", "color": "#c4d841", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_mavlink", "color": "#bdd841", "style": "normal"}, {"source": "t_gimbal_device_set_attitude", "target": "m_mavlink", "color": "#b6d841", "style": "normal"}, {"source": "t_vehicle_odometry", "target": "m_mavlink", "color": "#a8d841", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_mavlink", "color": "#a1d841", "style": "normal"}, {"source": "t_event", "target": "m_mavlink", "color": "#93d841", "style": "normal"}, {"source": "t_figure_eight_status", "target": "m_mavlink", "color": "#7fd841", "style": "normal"}, {"source": "t_gimbal_manager_information", "target": "m_mavlink", "color": "#78d841", "style": "normal"}, {"source": "t_uavcan_parameter_value", "target": "m_mavlink", "color": "#71d841", "style": "normal"}, {"source": "t_home_position", "target": "m_mavlink", "color": "#6ad841", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_mavlink", "color": "#5cd841", "style": "normal"}, {"source": "t_debug_key_value", "target": "m_mavlink", "color": "#55d841", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mavlink", "color": "#4ed841", "style": "normal"}, {"source": "t_sensor_gps", "target": "m_mavlink", "color": "#47d841", "style": "normal"}, {"source": "t_mission", "target": "m_mavlink", "color": "#41d841", "style": "normal"}, {"source": "t_gimbal_manager_status", "target": "m_mavlink", "color": "#41d84e", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_mavlink", "color": "#41d85c", "style": "normal"}, {"source": "t_debug_vect", "target": "m_mavlink", "color": "#41d871", "style": "normal"}, {"source": "t_actuator_armed", "target": "m_mavlink", "color": "#41d878", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_mavlink", "color": "#41d885", "style": "normal"}, {"source": "t_rtl_time_estimate", "target": "m_mavlink", "color": "#41d88c", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mavlink", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_mavlink", "color": "#41d8a1", "style": "normal"}, {"source": "t_camera_trigger", "target": "m_mavlink", "color": "#41d8af", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_mavlink", "color": "#41d8c4", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_mavlink", "color": "#41d8ca", "style": "normal"}, {"source": "t_actuator_outputs", "target": "m_mavlink", "color": "#41d8d8", "style": "normal"}, {"source": "t_orbit_status", "target": "m_mavlink", "color": "#41b6d8", "style": "normal"}, {"source": "t_input_rc", "target": "m_mavlink", "color": "#41afd8", "style": "normal"}, {"source": "t_estimator_status", "target": "m_mavlink", "color": "#41a1d8", "style": "normal"}, {"source": "t_camera_capture", "target": "m_mavlink", "color": "#419ad8", "style": "normal"}, {"source": "t_camera_status", "target": "m_mavlink", "color": "#4193d8", "style": "normal"}, {"source": "t_gimbal_device_information", "target": "m_mavlink", "color": "#417fd8", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_mavlink", "color": "#4178d8", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_mavlink", "color": "#4171d8", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_mavlink", "color": "#4155d8", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mavlink", "color": "#414ed8", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mavlink", "color": "#4147d8", "style": "normal"}, {"source": "t_transponder_report", "target": "m_mavlink", "color": "#4741d8", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_mavlink", "color": "#4e41d8", "style": "normal"}, {"source": "t_satellite_info", "target": "m_mavlink", "color": "#5c41d8", "style": "normal"}, {"source": "t_estimator_selector_status", "target": "m_mavlink", "color": "#6341d8", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_mavlink", "color": "#7841d8", "style": "normal"}, {"source": "t_cpuload", "target": "m_mavlink", "color": "#7f41d8", "style": "normal"}, {"source": "t_gimbal_device_attitude_status", "target": "m_mavlink", "color": "#8c41d8", "style": "normal"}, {"source": "t_debug_array", "target": "m_mavlink", "color": "#9a41d8", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_mavlink", "color": "#b641d8", "style": "normal"}, {"source": "t_failsafe_flags", "target": "m_mavlink", "color": "#af41d8", "style": "normal"}, {"source": "t_open_drone_id_arm_status", "target": "m_mavlink", "color": "#c441d8", "style": "normal"}, {"source": "t_vehicle_thrust_setpoint", "target": "m_mavlink", "color": "#d141d8", "style": "normal"}, {"source": "t_debug_value", "target": "m_mavlink", "color": "#d841ca", "style": "normal"}, {"source": "t_dataman_response", "target": "m_mavlink", "color": "#d841c4", "style": "normal"}, {"source": "t_gimbal_v1_command", "target": "m_mavlink", "color": "#d841a8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mavlink", "color": "#d841a1", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_mavlink", "color": "#d84193", "style": "normal"}, {"source": "t_mount_orientation", "target": "m_mavlink", "color": "#d8418c", "style": "normal"}, {"source": "t_ulog_stream", "target": "m_mavlink", "color": "#d8417f", "style": "normal"}, {"source": "t_geofence_result", "target": "m_mavlink", "color": "#d84178", "style": "normal"}, {"source": "t_health_report", "target": "m_mavlink", "color": "#d8416a", "style": "normal"}, {"source": "t_tecs_status", "target": "m_mavlink", "color": "#d84163", "style": "normal"}, {"source": "t_airspeed", "target": "m_mavlink", "color": "#d8415c", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_mc_att_control", "color": "#414ed8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_att_control", "color": "#d841a1", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_att_control", "color": "#d87841", "style": "normal"}, {"source": "t_autotune_attitude_control_status", "target": "m_mc_att_control", "color": "#d84193", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_att_control", "color": "#4147d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_att_control", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_attitude_setpoint", "target": "m_mc_att_control", "color": "#d89341", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_att_control", "color": "#4ed841", "style": "normal"}, {"source": "t_vehicle_torque_setpoint", "target": "m_mc_autotune_attitude_control", "color": "#7141d8", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_autotune_attitude_control", "color": "#41d893", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_autotune_attitude_control", "color": "#d87841", "style": "normal"}, {"source": "t_actuator_controls_status_0", "target": "m_mc_autotune_attitude_control", "color": "#d841b6", "style": "normal"}, {"source": "t_vehicle_constraints", "target": "m_mc_pos_control", "color": "#41cad8", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_pos_control", "color": "#d841a1", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_mc_pos_control", "color": "#4147d8", "style": "normal"}, {"source": "t_trajectory_setpoint", "target": "m_mc_pos_control", "color": "#d8419a", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_pos_control", "color": "#4ed841", "style": "normal"}, {"source": "t_battery_status", "target": "m_mc_rate_control", "color": "#d85541", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_mc_rate_control", "color": "#d841a1", "style": "normal"}, {"source": "t_vehicle_rates_setpoint", "target": "m_mc_rate_control", "color": "#d8ca41", "style": "normal"}, {"source": "t_manual_control_setpoint", "target": "m_mc_rate_control", "color": "#d87841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_mc_rate_control", "color": "#41d893", "style": "normal"}, {"source": "t_control_allocator_status", "target": "m_mc_rate_control", "color": "#41d863", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_mc_rate_control", "color": "#4ed841", "style": "normal"}, {"source": "t_vehicle_global_position", "target": "m_navigator", "color": "#41d8a1", "style": "normal"}, {"source": "t_mission", "target": "m_navigator", "color": "#41d841", "style": "normal"}, {"source": "t_landing_target_pose", "target": "m_navigator", "color": "#41d885", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_navigator", "color": "#d841a1", "style": "normal"}, {"source": "t_geofence_status", "target": "m_navigator", "color": "#d85c41", "style": "normal"}, {"source": "t_home_position", "target": "m_navigator", "color": "#6ad841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_navigator", "color": "#41d893", "style": "normal"}, {"source": "t_position_controller_landing_status", "target": "m_navigator", "color": "#8541d8", "style": "normal"}, {"source": "t_transponder_report", "target": "m_navigator", "color": "#4741d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_navigator", "color": "#41d85c", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_navigator", "color": "#4147d8", "style": "normal"}, {"source": "t_dataman_response", "target": "m_navigator", "color": "#d841c4", "style": "normal"}, {"source": "t_wind", "target": "m_navigator", "color": "#d8a141", "style": "normal"}, {"source": "t_rtl_status", "target": "m_navigator", "color": "#9341d8", "style": "normal"}, {"source": "t_input_rc", "target": "m_rc_update", "color": "#41afd8", "style": "normal"}, {"source": "t_manual_control_switches", "target": "m_rc_update", "color": "#41d8ca", "style": "normal"}, {"source": "t_rc_parameter_map", "target": "m_rc_update", "color": "#416ad8", "style": "normal"}, {"source": "t_vehicle_imu_status", "target": "m_sensors", "color": "#bdd841", "style": "normal"}, {"source": "t_estimator_sensor_bias", "target": "m_sensors", "color": "#4155d8", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_sensors", "color": "#d841af", "style": "normal"}, {"source": "t_sensor_optical_flow", "target": "m_sensors", "color": "#6a41d8", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_sensors", "color": "#9ad841", "style": "normal"}, {"source": "t_vehicle_imu", "target": "m_sensors", "color": "#d87141", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_sensors", "color": "#4178d8", "style": "normal"}, {"source": "t_sensor_correction", "target": "m_sensors", "color": "#41d8c4", "style": "normal"}, {"source": "t_adc_report", "target": "m_sensors", "color": "#4141d8", "style": "normal"}, {"source": "t_sensor_selection", "target": "m_sensors", "color": "#5cd841", "style": "normal"}, {"source": "t_differential_pressure", "target": "m_sensors", "color": "#4171d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_sensors", "color": "#4ed841", "style": "normal"}, {"source": "t_sensor_gyro", "target": "m_temperature_compensation", "color": "#d841af", "style": "normal"}, {"source": "t_sensor_accel", "target": "m_temperature_compensation", "color": "#9ad841", "style": "normal"}, {"source": "t_sensor_baro", "target": "m_temperature_compensation", "color": "#7841d8", "style": "normal"}, {"source": "t_sensor_mag", "target": "m_temperature_compensation", "color": "#4178d8", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_temperature_compensation", "color": "#41d85c", "style": "normal"}, {"source": "t_vehicle_command_ack", "target": "m_uxrce_dds_client", "color": "#d88541", "style": "normal"}, {"source": "t_fw_virtual_attitude_setpoint", "target": "m_vtol_att_control", "color": "#41d855", "style": "normal"}, {"source": "t_vehicle_command", "target": "m_vtol_att_control", "color": "#41d85c", "style": "normal"}, {"source": "t_vehicle_land_detected", "target": "m_vtol_att_control", "color": "#d841a1", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_vtol_att_control", "color": "#41d893", "style": "normal"}, {"source": "t_vehicle_attitude", "target": "m_vtol_att_control", "color": "#414ed8", "style": "normal"}, {"source": "t_airspeed_validated", "target": "m_vtol_att_control", "color": "#a1d841", "style": "normal"}, {"source": "t_action_request", "target": "m_vtol_att_control", "color": "#d84171", "style": "normal"}, {"source": "t_vehicle_local_position", "target": "m_vtol_att_control", "color": "#4147d8", "style": "normal"}, {"source": "t_tecs_status", "target": "m_vtol_att_control", "color": "#d84163", "style": "normal"}, {"source": "t_position_setpoint_triplet", "target": "m_vtol_att_control", "color": "#4e41d8", "style": "normal"}, {"source": "t_home_position", "target": "m_vtol_att_control", "color": "#6ad841", "style": "normal"}, {"source": "t_mc_virtual_attitude_setpoint", "target": "m_vtol_att_control", "color": "#a141d8", "style": "normal"}, {"source": "t_vehicle_local_position_setpoint", "target": "m_vtol_att_control", "color": "#b641d8", "style": "normal"}, {"source": "t_vehicle_control_mode", "target": "m_vtol_att_control", "color": "#4ed841", "style": "normal"}, {"source": "t_vehicle_status", "target": "m_i2c_launcher", "color": "#41d893", "style": "normal"}]}